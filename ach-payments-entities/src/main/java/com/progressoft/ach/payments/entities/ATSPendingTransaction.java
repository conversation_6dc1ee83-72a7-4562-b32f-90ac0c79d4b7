package com.progressoft.ach.payments.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "ATSPENDINGTRANSACTIONS")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPendingTransaction extends JFWEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public ATSPendingTransaction() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String PENDING_PAYMENT_ID = "pendingPaymentId";
    private String pendingPaymentId;

    public String getPendingPaymentId() {
        return this.pendingPaymentId;
    }

    public void setPendingPaymentId(String pendingPaymentId) {
        this.pendingPaymentId = pendingPaymentId;
    }

    public static final String TRANSACTION_ID = "transactionId";
    @Column(name = "TRANSACTIONID", nullable = true, length = 50)
    private String transactionId;

    public String getTransactionId() {
        return this.transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public static final String AMOUNT = "amount";
    @Column(name="AMOUNT", nullable=true, precision=14, scale=5, length=16)
    private java.math.BigDecimal amount;
    public java.math.BigDecimal getAmount(){
        return this.amount;
    }
    public void setAmount(java.math.BigDecimal amount){
        this.amount = amount;
    }

    public static final String STATUS = "status";
    @Column(name="STATUS", nullable=true, length=255)
    private String status;
    public String getStatus(){
        return this.status;
    }
    public void setStatus(String status){
        this.status = status;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ATSPendingTransaction that = (ATSPendingTransaction) o;
        return id == that.id && Objects.equals(pendingPaymentId, that.pendingPaymentId) && Objects.equals(transactionId, that.transactionId) && Objects.equals(amount, that.amount) && Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getPendingPaymentId() == null) ? 0 : getPendingPaymentId().hashCode());
        result = prime * result + ((getTransactionId() == null) ? 0 : getTransactionId().hashCode());
        result = prime * result + ((getAmount() == null) ? 0 : getAmount().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "ATSPendingTransaction{" +
                "id=" + id +
                ", pendingPayment=" + pendingPaymentId +
                ", transactionId='" + transactionId + '\'' +
                ", amount=" + amount +
                ", status='" + status + '\'' +
                '}';
    }
}

