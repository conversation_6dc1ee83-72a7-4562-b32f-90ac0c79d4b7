<?xml version="1.0" encoding="UTF-8"?>
<!--- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Legal Notices

SWIFT SCRL@2016. All rights reserved.

This schema is a component of MyStandards, the SWIFT collaborative Web application used to manage
standards definitions and industry usage.

This is a licensed product, which may only be used and distributed in accordance with MyStandards License
Terms as specified in MyStandards Service Description and the related Terms of Use.

Unless otherwise agreed in writing with SWIFT SCRL, the user has no right to:
 - authorise external end users to use this component for other purposes than their internal use.
 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.
 - re-sell or authorise another party e.g. software and service providers, to re-sell this component.

This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties
with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual 
property rights of whatever nature in this component will remain the exclusive property of SWIFT or its 
licensors.

Trademarks
SWIFT is the trade name of S.W.I.F.T. SCRL.
The following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.
Other product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

Group: CMA Small Systems
Collection: Kuwait RTGS (restricted)
Usage Guideline: FIToFIPaymentStatusReport_pacs.002.001.10
Base Message: pacs.002.001.10
Date of publication: 23 November 2020
URL: https://www2.swift.com/mystandards/#/mp/mx/_Tx40wPpTEemXdbviE8W5HA/_Tx40xfpTEemXdbviE8W5HA
Generated by the MyStandards web platform [http://www.swift.com/mystandards] on 2020-11-23T15:03:09+00:00
-->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.10" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.10">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="IBAN" type="IBAN2007Identifier"/>
                <xs:element name="Othr" type="GenericAccountIdentification1__1"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AnyBICDec2014Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__1">
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CMA_Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CashAccount38__1">
        <xs:sequence>
            <xs:element name="Id" type="AccountIdentification4Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Prxy" type="ProxyAccountIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CategoryPurpose1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ClearingChannel2Code__1">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RTGS"/>
            <xs:enumeration value="RTNS"/>
            <xs:enumeration value="MPNS"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemMemberIdentification2__1">
        <xs:sequence>
            <xs:element name="MmbId" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DateAndDateTime2Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Dt" type="ISODate"/>
                <xs:element name="DtTm" type="ISODateTime"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="DecimalNumber">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FIToFIPmtStsRpt" type="FIToFIPaymentStatusReportV10"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPaymentGroupStatus1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPaymentTransactionStatus1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalProxyAccountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FIToFIPaymentStatusReportV10">
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader91__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlGrpInfAndSts" type="OriginalGroupHeader17__1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TxInfAndSts" type="PaymentTransaction110__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIDec2014Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CMA_Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax34Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader91__1">
        <xs:sequence>
            <xs:element name="MsgId" type="RestrictedFINXMax16Text"/>
            <xs:element name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="LEIIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="LocalInstrument2Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Max15NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max16Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="OrganisationIdentification29__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AnyBIC" type="AnyBICDec2014Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericOrganisationIdentification1__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalGroupHeader17__1">
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="RestrictedFINXMax16Text"/>
            <xs:element name="OrgnlMsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlNbOfTxs" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCtrlSum" type="DecimalNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrpSts" type="ExternalPaymentGroupStatus1Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="StsRsnInf" type="StatusReasonInformation12__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalGroupInformation29__1">
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="RestrictedFINXMax16Text"/>
            <xs:element name="OrgnlMsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalTransactionReference28__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmAmt" type="RestrictedFINActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation27__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation16__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="Party40Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="Party40Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount38__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgtAcct" type="CashAccount38__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgtAcct" type="CashAccount38__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="Party40Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount38__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtCdtr" type="Party40Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party38Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="OrgId" type="OrganisationIdentification29__1"/>
                <xs:element name="PrvtId" type="PersonIdentification13__1"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party40Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Pty" type="PartyIdentification135__1"/>
                <xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PartyIdentification135__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CMA_Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party38Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTransaction110__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="StsId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlGrpInf" type="OriginalGroupInformation29__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlInstrId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlEndToEndId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlUETR" type="UUIDv4Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxSts" type="ExternalPaymentTransactionStatus1Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="StsRsnInf" type="StatusReasonInformation12__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AccptncDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FctvIntrBkSttlmDt" type="DateAndDateTime2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctSvcrRef" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxRef" type="OriginalTransactionReference28__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTypeInformation27__1">
        <xs:sequence>
            <xs:element name="ClrChanl" type="ClearingChannel2Code__1"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="SvcLvl" type="ServiceLevel8Choice__1"/>
            <xs:element name="LclInstrm" type="LocalInstrument2Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentification13__1">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericPersonIdentification1__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PostalAddress24__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AdrLine" type="CMA_Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProxyAccountIdentification1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ProxyAccountType1Choice"/>
            <xs:element name="Id" type="Max2048Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProxyAccountType1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalProxyAccountType1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Purpose2Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentInformation7__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ReferredDocumentType4__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="RestrictedFINXMax35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType3Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType4__1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="ReferredDocumentType3Choice__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation16__1">
        <xs:sequence>
            <xs:element maxOccurs="4" minOccurs="0" name="Ustrd" type="CMA_Max35Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Strd" type="StructuredRemittanceInformation16__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="RestrictedFINActiveOrHistoricCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="14"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="RestrictedFINActiveOrHistoricCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="RestrictedFINActiveOrHistoricCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="RestrictedFINXMax16Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="([0-9a-zA-Z\-\?:\(\)\.,'\+ ]([0-9a-zA-Z\-\?:\(\)\.,'\+ ]*(/[0-9a-zA-Z\-\?:\(\)\.,'\+ ])?)*)"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="RestrictedFINXMax34Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="([0-9a-zA-Z\-\?:\(\)\.,'\+ ]([0-9a-zA-Z\-\?:\(\)\.,'\+ ]*(/[0-9a-zA-Z\-\?:\(\)\.,'\+ ])?)*)"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="RestrictedFINXMax35Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]{1,35}"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ServiceLevel8Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StatusReason6Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StatusReasonInformation12__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Orgtr" type="PartyIdentification135__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="StatusReason6Choice__1"/>
            <xs:element maxOccurs="6" minOccurs="0" name="AddtlInf" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StructuredRemittanceInformation16__1">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RfrdDocInf" type="ReferredDocumentInformation7__1"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AddtlRmtInf" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
