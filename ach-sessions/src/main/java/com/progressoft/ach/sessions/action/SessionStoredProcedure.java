package com.progressoft.ach.sessions.action;

import com.progressoft.ach.entities.ATSBDI_Session;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.hibernate.Session;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.sql.CallableStatement;

import static java.lang.String.format;

@Component
public class SessionStoredProcedure {
    @PersistenceContext(unitName = "JFWUnit")
    private EntityManager entityManager;
    private static final String AUTO_REJECT_COMM_FAILURE = "AutoRejectCommFailure";
    private static final String AUTO_REPLY_CUT_OFF_JOB = "AutoReplyCutOffJob";
    private static final String EXECUTE_FINAL_CUT_OFF_JOBS = "ExecuteFinalCutOffJobs";

    @Transactional
    public void execute(ATSBDI_Session session, String jobName) throws Exception {
        try (CallableStatement procedure = prepare(jobName)) {
            procedure.setLong(1, session.getId());
            procedure.execute();
            entityManager.flush();
        }
    }

    private CallableStatement prepare(String jobName) throws Exception {
        return switch (jobName) {
            case AUTO_REJECT_COMM_FAILURE -> entityManager.unwrap(Session.class)
                    .doReturningWork(connection -> connection.prepareCall("{call AutoRejectCommFailure(?)}"));
            case AUTO_REPLY_CUT_OFF_JOB -> entityManager.unwrap(Session.class)
                    .doReturningWork(connection -> connection.prepareCall("{call AutoReplyCutOffJob(?)}"));
            case EXECUTE_FINAL_CUT_OFF_JOBS -> entityManager.unwrap(Session.class)
                    .doReturningWork(connection -> connection.prepareCall("{call ExecuteFinalCutOffJobs(?)}"));
            default -> throw new Exception(format("this job (%s) not implemented yet ", jobName));
        };
    }

}
