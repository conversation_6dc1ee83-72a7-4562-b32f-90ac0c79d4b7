package com.progressoft.ach;

import com.progressoft.ach.builder.*;
import com.progressoft.ach.common.Constants;
import com.progressoft.ach.entities.*;
import com.progressoft.ach.sessions.action.PeriodTypes;
import com.progressoft.jfw.test.FakeItemDao;
import com.progressoft.jfw.test.FakeSystemDateImpl;
import org.joda.time.Duration;
import org.joda.time.Interval;
import org.junit.Before;
import org.junit.Test;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

import static java.util.Collections.singletonList;
import static org.junit.Assert.*;
import static org.mockito.Mockito.mock;

public class PeriodTest {
    private static final ATSMSG_Type CREDIT_MESSAGE_TYPE = new MessageTypeBuilder().code(Constants.CREDIT).name("Credit").build();
    private static final ATSMSG_Type REPLY_MESSAGE_TYPE = new MessageTypeBuilder().code(Constants.STATUS_REPORT).name("Reply").build();
    private static final ATSPRT_Participant NBO = new ParticipantBuilder().code("001").name("NBO").build();
    private static final ATSPRT_Participant AIB = new ParticipantBuilder().code("002").name("AIB").build();
    private static final ATSMSG_CtgPurp CASH_CATEGORY_PURPOSE = new CategoryPurposeBuilder().code("CASH").name("Cash").build();
    private static final ATSMSG_CtgPurp SALARY_CATEGORY_PURPOSE = new CategoryPurposeBuilder().code("SALA").name("Salary").build();

    private Period period;
    private FakeSystemDateImpl systemDate;
    private FakeItemDao itemDao;

    @Before
    public void setup() {
        itemDao = new FakeItemDao();
        systemDate = new FakeSystemDateImpl();
        SessionUpdatePublisher publisher = mock(SessionUpdatePublisher.class);
        Session session = new Session(itemDao,publisher);
        period = new Period(itemDao, systemDate, publisher);
        Window window = new Window(itemDao);

        session.setPeriod(period);
        period.setSession(session);
        period.setWindow(window);
    }

    @Test
    public void testCanBeCreated() {
        new Period();
    }

    @Test
    public void testExchangeWindowTypeIdentified() {
        ATSBDS_PeriodType exchangePeriodType = new PeriodTypeBuilder().code(PeriodTypes.Exchange).build();
        ATSBDS_Period exchangePeriodSchedule = new PeriodScheduleBuilder().type(exchangePeriodType).build();
        ATSBDI_Period p = new PeriodBuilder().schedule(exchangePeriodSchedule).build();
        assertTrue(period.isExchange(p));
    }

    @Test
    public void testIfSettlementShouldNotBeIdentifiesAsAnExchangeWindow() {
        ATSBDS_PeriodType settlementPeriodType = new PeriodTypeBuilder().code(PeriodTypes.Settlement).build();
        ATSBDS_Period settlementPeriodSchedule = new PeriodScheduleBuilder().type(settlementPeriodType).build();
        ATSBDI_Period p = new PeriodBuilder().schedule(settlementPeriodSchedule).build();
        assertFalse(period.isExchange(p));
    }

    @Test
    public void testIfAPeriodIsOpen() {
        systemDate.now(Timestamp.valueOf("2016-10-22 12:00:00"));
        ATSBDI_Period p = new PeriodBuilder()
                .start(Timestamp.valueOf("2016-10-22 10:00:00"))
                .end(Timestamp.valueOf("2016-10-22 14:00:00"))
                .build();
        assertTrue(period.isOpen(p));
    }

    @Test
    public void testIfAPeriodIsNotOpen() {
        systemDate.now(Timestamp.valueOf("2016-10-22 09:00:00"));
        ATSBDI_Period p = new PeriodBuilder()
                .start(Timestamp.valueOf("2016-10-22 10:00:00"))
                .end(Timestamp.valueOf("2016-10-22 14:00:00"))
                .build();
        assertFalse(period.isOpen(p));
    }

    @Test
    public void testIfCurrentlyOpenPeriodsAreReturned() {
        ATSBDI_Period currentlyOpenPeriod = new PeriodBuilder().build();
        itemDao.add(currentlyOpenPeriod);
        List<ATSBDI_Period> currentlyOpenPeriods = period.getCurrentlyOpenPeriods();
        assertEquals(currentlyOpenPeriod, currentlyOpenPeriods.get(0));
    }

    @Test
    public void testIfANoneOverridingWindowCanBeAdded() {
        ATSBDI_Window creditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        ATSBDI_Period exchangePeriod = new PeriodBuilder().windows(singletonList(creditWindow)).build();

        ATSBDI_Window windowThatDoesNotOverrideTheCreditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 16:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 20:00:00"))
                .build();

        assertTrue(period.canWindowBeAdded(exchangePeriod, windowThatDoesNotOverrideTheCreditWindow));
    }

    @Test
    public void testIfAnOverridingWindowCanBeAdded() {
        ATSBDI_Window creditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        ATSBDI_Period exchangePeriod = new PeriodBuilder().windows(singletonList(creditWindow)).build();

        ATSBDI_Window windowThatOverridesTheCreditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        assertFalse(period.canWindowBeAdded(exchangePeriod, windowThatOverridesTheCreditWindow));
    }

    @Test
    public void testShiftingAPeriodForward() {
        Duration duration = Duration.standardMinutes(30);
        ATSBDI_Period p = new PeriodBuilder()
                .start(Timestamp.valueOf("2016-10-22 12:00:00"))
                .end(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();
        period.shiftForward(p, duration);
        assertEquals(Timestamp.valueOf("2016-10-22 12:30:00"), p.getPeriodStart());
        assertEquals(Timestamp.valueOf("2016-10-22 16:30:00"), p.getPeriodEnd());
        assertTrue(!itemDao.getMergedItems().isEmpty());
    }

    @Test
    public void testStartingAfterAnotherPeriod() {
        ATSBDI_Period p1 = new PeriodBuilder()
                .start(Timestamp.valueOf("2016-10-22 12:00:00"))
                .end(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        ATSBDI_Period p2 = new PeriodBuilder()
                .start(Timestamp.valueOf("2016-10-22 16:00:00"))
                .end(Timestamp.valueOf("2016-10-22 18:00:00"))
                .build();

        assertTrue(period.startsAfter(p2, p1));
        assertFalse(period.startsAfter(p1, p2));
    }

    @Test
    public void testPeriodDescription() {
        ATSBDI_Period p = new PeriodBuilder()
                .id(123)
                .name("Name")
                .build();
        assertEquals("123 - Name", period.getPeriodDescription(p));
    }

    @Test
    public void testPeriodWithNameExists() {
        ATSBDI_Window creditWindow = new WindowBuilder()
                .name("Credit")
                .type(CREDIT_MESSAGE_TYPE)
                .build();

        ATSBDI_Period p = new PeriodBuilder().windows(singletonList(creditWindow)).build();

        assertTrue(period.windowWithNameExists(p, "Credit"));
        assertFalse(period.windowWithNameExists(p, "Debit"));
    }

    @Test
    public void testSuggestedWindowIntervalWithoutAnyLastAvailableWindow() {
        ATSBDI_Period p = new PeriodBuilder().build();
        Interval suggestedWindowInterval = period.getSuggestedWindowIntervalFor(p, NBO.getCode(), CREDIT_MESSAGE_TYPE.getCode(),
                new String[]{CASH_CATEGORY_PURPOSE.getCode()},
                Duration.standardMinutes(30));
        assertNull(suggestedWindowInterval);
    }

    @Test
    public void testSuggestedWindowIntervalWithoutLastAvailableWindowForSameParticipant() {
        ATSBDI_Window creditWindowForAnotherParticipant = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .participants(singletonList(AIB))
                .build();

        ATSBDI_Period p = new PeriodBuilder().windows(singletonList(creditWindowForAnotherParticipant)).build();
        Interval suggestedWindowInterval = period.getSuggestedWindowIntervalFor(p, NBO.getCode(), CREDIT_MESSAGE_TYPE.getCode(),
                new String[]{CASH_CATEGORY_PURPOSE.getCode()},
                Duration.standardMinutes(30));
        assertNull(suggestedWindowInterval);
    }

    @Test
    public void testSuggestedWindowIntervalWithoutLastAvailableWindowForSameType() {
        ATSBDI_Window creditWindowForAnotherParticipant = new WindowBuilder()
                .type(REPLY_MESSAGE_TYPE)
                .build();

        ATSBDI_Period p = new PeriodBuilder().windows(singletonList(creditWindowForAnotherParticipant)).build();
        Interval suggestedWindowInterval = period.getSuggestedWindowIntervalFor(p, NBO.getCode(), CREDIT_MESSAGE_TYPE.getCode(),
                new String[]{CASH_CATEGORY_PURPOSE.getCode()},
                Duration.standardMinutes(30));
        assertNull(suggestedWindowInterval);
    }

    @Test
    public void testSuggestedWindowIntervalWithoutLastAvailableWindowForCategoryPurpose() {
        ATSBDI_Window creditWindowForAnotherParticipant = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .purposes(singletonList(SALARY_CATEGORY_PURPOSE))
                .build();

        ATSBDI_Period p = new PeriodBuilder().windows(singletonList(creditWindowForAnotherParticipant)).build();
        Interval suggestedWindowInterval = period.getSuggestedWindowIntervalFor(p, NBO.getCode(), CREDIT_MESSAGE_TYPE.getCode(),
                new String[]{CASH_CATEGORY_PURPOSE.getCode()},
                Duration.standardMinutes(30));
        assertNull(suggestedWindowInterval);
    }

    @Test
    public void testSuggestedWindowIntervalWithLastAvailableWindowForSameParticipant() {
        ATSBDI_Window creditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        ATSBDI_Period p = new PeriodBuilder().windows(singletonList(creditWindow)).build();

        Interval suggestedWindowInterval = period.getSuggestedWindowIntervalFor(p, NBO.getCode(), CREDIT_MESSAGE_TYPE.getCode(),
                new String[]{CASH_CATEGORY_PURPOSE.getCode()},
                Duration.standardMinutes(30));

        assertEquals(new Interval(Timestamp.valueOf("2016-10-22 16:00:00").getTime(), Timestamp.valueOf("2016-10-22 16:30:00").getTime()),
                suggestedWindowInterval);
    }

    @Test
    public void testWindowAdditionWithPeriodExtension() {
        systemDate.now(Timestamp.valueOf("2016-10-22 12:30:00"));

        ATSBDI_Window creditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        List<ATSBDI_Window> windows = new ArrayList<>();
        windows.add(creditWindow);

        ATSBDI_Session session = new ATSBDI_Session();
        ATSBDI_Period exchangePeriod = new PeriodBuilder()
                .session(session)
                .id(1)
                .name("Exchange")
                .windows(windows)
                .start(Timestamp.valueOf("2016-10-22 12:00:00"))
                .end(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        session.setRefSessionBDIPeriods(singletonList(exchangePeriod));

        ATSBDI_Window newWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 16:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:30:00"))
                .build();

        period.addWindow(exchangePeriod, newWindow);

        assertTrue(exchangePeriod.getRefPeriodBDIWindows().contains(newWindow));
        assertTrue(!itemDao.getMergedItems().isEmpty());
        assertEquals(exchangePeriod.getPeriodEnd(), Timestamp.valueOf("2016-10-22 16:30:00"));
    }

    @Test
    public void testWindowAdditionWithoutPeriodExtension() {
        systemDate.now(Timestamp.valueOf("2016-10-22 12:30:00"));

        ATSBDI_Window creditWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 12:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:00:00"))
                .build();

        List<ATSBDI_Window> windows = new ArrayList<>();
        windows.add(creditWindow);

        ATSBDI_Session session = new ATSBDI_Session();
        ATSBDI_Period exchangePeriod = new PeriodBuilder()
                .session(session)
                .id(1)
                .name("Exchange")
                .windows(windows)
                .start(Timestamp.valueOf("2016-10-22 12:00:00"))
                .end(Timestamp.valueOf("2016-10-22 18:00:00"))
                .build();

        session.setRefSessionBDIPeriods(singletonList(exchangePeriod));

        ATSBDI_Window newWindow = new WindowBuilder()
                .type(CREDIT_MESSAGE_TYPE)
                .windowStart(Timestamp.valueOf("2016-10-22 16:00:00"))
                .windowEnd(Timestamp.valueOf("2016-10-22 16:30:00"))
                .build();

        period.addWindow(exchangePeriod, newWindow);

        assertTrue(exchangePeriod.getRefPeriodBDIWindows().contains(newWindow));
        assertTrue(!itemDao.getMergedItems().isEmpty());
        assertEquals(exchangePeriod.getPeriodEnd(), Timestamp.valueOf("2016-10-22 18:00:00"));
    }
}
