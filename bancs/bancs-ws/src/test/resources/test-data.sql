INSERT INTO COMMEndPOINTS (ID, Z_ORG_ID, Z_TENANT_ID, NAME, DIRECTION, SYSTEM, URI, DEADLETTERURI, ACCEPTEDMESSAGETYPES,
                           ACCEPTEDTENANTS, REJECTEDMESSAGETYPES, RE<PERSON>ECTEDTENANTS)
VALUES (1, 0, 'SY<PERSON><PERSON>', 'Default In Endpoint', 'In', 'Ach', 'AchAmq:queue:ach_in', 'AchAmq:queue:ach_in_dlq', '', '',
        '', '');
INSERT INTO COMMEndPOINTS (ID, Z_ORG_ID, Z_TENANT_ID, NAME, DIRECTION, SYSTEM, URI, DEADLETTERURI, ACCEPTEDMESSAGETYPES,
                           ACCEPTEDTENANTS, REJECTEDMESSAGETYPES, REJECTEDTENANTS)
VALUES (2, 0, 'SYSTEM', 'Default Out Endpoint', 'Out', 'Psys', 'PsysAmq:queue:psys_in', '', '', '', '', '');
INSERT INTO ATSPRT_INSTITUTIONS (ID, CODE, DESC<PERSON>PTION, NAME)
VALUES (1, '1', 'Central Bank', 'Central Bank'),
       (2, '2', 'Commercial Bank', 'Commercial Bank'),
       (3, '3', 'Clearing System', 'Clearing System'),
       (4, '4', 'Governmental Agency', 'Governmental Agency'),
       (5, '5', 'Central Securities Depository', 'CSD'),
       (6, '6', 'RTGS System', 'RTGS'),
       (7, '7', 'Payment Service Provider', 'PSP'),
       (8, '8', 'Biller', 'Biller');

INSERT INTO ATSPRT_LimitsProfile (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP,
                                  Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG,
                                  Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                                  Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE,
                                  DESCRIPTION, NAME, CBATCHCOUNT, CTRANSPERBATCH, CTXLIMIT, DBATCHCOUNT, DTRANSPERBATCH,
                                  DTXLIMIT, LIMITSPER, Z_DRAFT_ID, Z_STATUS_ID)
VALUES (1, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2021-10-13 07:39:04.212', NULL, 0, NULL, 0, NULL,
        NULL, 2921218077316, 'ATS', 'SERVICE_USER', TIMESTAMP '2021-10-13 07:39:04.391', 2921218078843, NULL,
        'COMMITED', '1', 'No Limits', 'Default', 0, 100, 0, 0, 100, 0, 'Business Day', NULL, 100025410);

INSERT INTO ATSPRT_TYPES (ID, CODE, DESCRIPTION, NAME)
VALUES (1, '1', 'Direct Participant', 'Direct');
INSERT INTO ATSPRT_TYPES (ID, CODE, DESCRIPTION, NAME)
VALUES (2, '2', 'Indirect Participant', 'Indirect');
INSERT INTO ATSPRT_TYPES (ID, CODE, DESCRIPTION, NAME)
VALUES (3, '3', 'Subdirect Participant', 'Subdirect');
INSERT INTO ATSPRT_TYPES (ID, CODE, DESCRIPTION, NAME)
VALUES (4, '4', 'Technical Participant', 'Technical');

INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (1, 0, 'SYSTEM', 'CBOMOMRU', 'Central Bank of Oman', 'CBO', 0, 0, 0, 0, 0, 1, 1, 1, 35, 'CBOM', 'MX', '001');
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (2, 0, 'SYSTEM', 'ACHMOMRX', 'Automated Clearing House', 'ACH', 0, 0, 0, 0, 0, 3, 1, 1, 35, 'ACHM', 'MX', '001');
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (3, 0, 'SYSTEM', 'NBOMOMRX', 'National Bank of Oman', 'NBO', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'NBOM', 'MX', '001');
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (4, 0, 'SYSTEM', 'IZZBOMRU', 'Alizz Islamic Bank', 'AIB', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'IZZB', 'MX', '001');
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (5, 0, 'SYSTEM', 'BMUSOMRX123', 'Bank Muscat', 'BMUS', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'BMUS', 'MX', '001');
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG,
                                 RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID,
                                 PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE)
VALUES (6, 0, 'SYSTEM', 'BBMEOMRX', 'HSBC Bank', 'HSBC', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'BBME', 'MX', '001');
-- INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE) VALUES (7, 0, 'SYSTEM', 'NBADOMRX', 'National Bank of Abu Dhabi', 'NBAD', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'NBAD', 'MX', '001');

INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (1, 0, 'SYSTEM', 'QNB Account', '1', '***********************', ********, 0, 95, -********, 0, 95, 1, 414, 1);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (2, 0, 'SYSTEM', 'CBK Account', '2', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 2);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (3, 0, 'SYSTEM', 'DOH Account', '3', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 3);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (4, 0, 'SYSTEM', 'MSH Account', '4', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 4);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (5, 0, 'SYSTEM', 'QNB Account', '100', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 5);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (6, 0, 'SYSTEM', 'UNB Account', '5', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 6);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (7, 0, 'SYSTEM', 'ICB Account', '200', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 999);


INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (1, 0, 'SYSTEM', 1, 1, 1, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (2, 0, 'SYSTEM', 1, 1, 2, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (3, 0, 'SYSTEM', 1, 1, 3, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (4, 0, 'SYSTEM', 1, 1, 4, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (5, 0, 'SYSTEM', 1, 1, 5, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (6, 0, 'SYSTEM', 1, 1, 6, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (7, 0, 'SYSTEM', 1, 1, 7, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (8, 0, 'SYSTEM', 1, 1, 1, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (9, 0, 'SYSTEM', 1, 1, 2, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (10, 0, 'SYSTEM', 1, 1, 3, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (11, 0, 'SYSTEM', 1, 1, 4, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (12, 0, 'SYSTEM', 1, 1, 5, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (13, 0, 'SYSTEM', 1, 1, 6, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (14, 0, 'SYSTEM', 1, 1, 7, 4);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (15, 0, 'SYSTEM', 1, 1, 1, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (16, 0, 'SYSTEM', 1, 1, 2, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (17, 0, 'SYSTEM', 1, 1, 3, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (18, 0, 'SYSTEM', 1, 1, 4, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (19, 0, 'SYSTEM', 1, 1, 5, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (20, 0, 'SYSTEM', 1, 1, 6, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (21, 0, 'SYSTEM', 1, 1, 7, 5);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (22, 0, 'SYSTEM', 1, 1, 1, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (23, 0, 'SYSTEM', 1, 1, 2, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (24, 0, 'SYSTEM', 1, 1, 3, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (25, 0, 'SYSTEM', 1, 1, 4, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (26, 0, 'SYSTEM', 1, 1, 5, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (27, 0, 'SYSTEM', 1, 1, 6, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (28, 0, 'SYSTEM', 1, 1, 7, 6);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (29, 0, 'SYSTEM', 1, 1, 1, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (30, 0, 'SYSTEM', 1, 1, 2, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (31, 0, 'SYSTEM', 1, 1, 3, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (32, 0, 'SYSTEM', 1, 1, 4, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (33, 0, 'SYSTEM', 1, 1, 5, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (34, 0, 'SYSTEM', 1, 1, 6, 7);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID)
VALUES (35, 0, 'SYSTEM', 1, 1, 7, 7);

INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (1, 0, 'SYSTEM', '1', 'BONU', 'BonusPayment', '1');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (2, 0, 'SYSTEM', '2', 'CASH', 'CashManagementTransfer', '2');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (3, 0, 'SYSTEM', '3', 'CBLK', 'Card Bulk Clearing', '3');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (4, 0, 'SYSTEM', '4', 'CCRD', 'Credit Card Payment', '4');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (5, 0, 'SYSTEM', '5', 'CORT', 'TradeSettlementPayment', '5');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (6, 0, 'SYSTEM', '6', 'DCRD', 'Debit Card Payment', '6');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (7, 0, 'SYSTEM', '7', 'DIVI', 'Dividend', '7');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (8, 0, 'SYSTEM', '8', 'EPAY', 'Epayment', '8');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (9, 0, 'SYSTEM', '9', 'FCOL', 'Fee Collection', '9');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (10, 0, 'SYSTEM', '10', 'GOVT', 'GovernmentPayment', '10');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (11, 0, 'SYSTEM', '11', 'HEDG', 'Hedging', '11');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (12, 0, 'SYSTEM', '12', 'ICCP', 'Irrevocable Credit Card Payment', '12');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (13, 0, 'SYSTEM', '13', 'IDCP', 'Irrevocable Debit Card Payment', '13');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (14, 0, 'SYSTEM', '14', 'INTC', 'IntraCompanyPayment', '14');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (15, 0, 'SYSTEM', '15', 'INTE', 'Interest', '15');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (16, 0, 'SYSTEM', '16', 'LOAN', 'Loan', '16');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (17, 0, 'SYSTEM', '17', 'OTHR', 'OtherPayment', '17');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (18, 0, 'SYSTEM', '18', 'PENS', 'PensionPayment', '18');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (19, 0, 'SYSTEM', '19', 'SALA', 'SalaryPayment', '19');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (20, 0, 'SYSTEM', '20', 'SECU', 'Securities', '20');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (21, 0, 'SYSTEM', '21', 'SSBE', 'SocialSecurityBenefit', '21');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (22, 0, 'SYSTEM', '22', 'SUPP', 'SupplierPayment', '22');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (23, 0, 'SYSTEM', '23', 'TAXS', 'TaxPayment', '23');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (24, 0, 'SYSTEM', '24', 'TRAD', 'Trade', '24');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (25, 0, 'SYSTEM', '25', 'TREA', 'TreasuryPayment', '25');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (26, 0, 'SYSTEM', '26', 'VATX', 'ValueAddedTaxPayment', '26');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE)
VALUES (27, 0, 'SYSTEM', '27', 'WHLD', 'WithHolding', '27');

INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (1, 0, 'SYSTEM', '1', 'CORT', 'Trade Payment SettlementPayment', 2);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (2, 0, 'SYSTEM', '2', 'SALA', 'Salary', 19);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (3, 0, 'SYSTEM', '3', 'INSU', 'Insurance', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (4, 0, 'SYSTEM', '4', 'GOVT', 'Government Payment', 10);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (5, 0, 'SYSTEM', '5', 'BILL', 'Bill Payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (6, 0, 'SYSTEM', '6', 'INTC', 'Intra Company Payment', 14);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (7, 0, 'SYSTEM', '7', 'INTE', 'Interest', 15);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (8, 0, 'SYSTEM', '8', 'CLPR', 'Car Loan Payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (9, 0, 'SYSTEM', '9', 'HOLP', 'Housing Loan Payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (10, 0, 'SYSTEM', '10', 'PENS', 'Pension Payment', 18);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (11, 0, 'SYSTEM', '11', 'COUR', 'Court Case', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (12, 0, 'SYSTEM', '12', 'SECU', 'Securities Payment', 20);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (13, 0, 'SYSTEM', '13', 'SSBE', 'Social Security Benefit', 21);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (14, 0, 'SYSTEM', '14', 'SUPP', 'Supplier Payment', 22);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (15, 0, 'SYSTEM', '15', 'TAXS', 'Tax Payment', 23);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (16, 0, 'SYSTEM', '16', 'VATX', 'Value Added Tax Payment', 26);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (17, 0, 'SYSTEM', '17', 'STAN', 'Standing Orders', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (18, 0, 'SYSTEM', '18', 'CRCP', 'Credit cards payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (19, 0, 'SYSTEM', '19', 'PFLB', 'Payment for local banks(transfers)', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (20, 0, 'SYSTEM', '20', 'PFST', 'Personal Finance settlement', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (21, 0, 'SYSTEM', '21', 'CNTP', 'Payment to Contractor', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (22, 0, 'SYSTEM', '22', 'DIVI', 'Dividend.Coupon Payment', 7);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (23, 0, 'SYSTEM', '23', 'RENT', 'Rent Payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (24, 0, 'SYSTEM', '24', 'ESRV', 'End of Service Benefits', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (25, 0, 'SYSTEM', '25', 'TERM', 'Termination Of Services', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (26, 0, 'SYSTEM', '26', 'LIAB', 'Liability Settlements', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (27, 0, 'SYSTEM', '27', 'CHQR', 'Cheque Returns', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (28, 0, 'SYSTEM', '28', 'BONU', 'Bonus Payment', 19);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (29, 0, 'SYSTEM', '29', 'TRAV', 'Travel Allowance', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (30, 0, 'SYSTEM', '30', 'LEAV', 'Leave Encashment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (31, 0, 'SYSTEM', '31', 'ALLW', 'Allowances', 19);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (32, 0, 'SYSTEM', '32', 'CHCO', 'Cheque Collection', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (33, 0, 'SYSTEM', '33', 'TUIT', 'Tuition Fees', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (34, 0, 'SYSTEM', '34', 'TRCF', 'Training Course Fees', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (35, 0, 'SYSTEM', '35', 'OFFM', 'Official Mission', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (36, 0, 'SYSTEM', '36', 'QACH', 'QATCH Return', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (37, 0, 'SYSTEM', '37', 'MOPA', 'Mobile Payment', 17);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (38, 0, 'SYSTEM', '38', 'TREA', 'Treasury Payment', 25);
INSERT INTO ATSMSG_TRANSPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, CATEGORYPURPOSEID) VALUES (39, 0, 'SYSTEM', '39', 'ISAL', 'On-us(In-house) Salary Payment', 19);

INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID)
VALUES (1, 0, 'SYSTEM', 3);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID)
VALUES (2, 0, 'SYSTEM', 4);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID)
VALUES (3, 0, 'SYSTEM', 5);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID)
VALUES (4, 0, 'SYSTEM', 6);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID)
VALUES (5, 0, 'SYSTEM', 7);

INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (1, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (2, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (3, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (4, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (5, 2);

INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (1, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (2, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (3, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (4, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID)
VALUES (5, 19);

INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE,
                              CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID)
VALUES (1, 0, 'SYSTEM', 50, 500, 0.001, 'Request Reply', 2, 634, 6);
INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE,
                              CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID)
VALUES (2, 0, 'SYSTEM', 50, 5000, 0.001, 'Request Reply', 19, 634, 6);
INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE,
                              CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID)
VALUES (3, 0, 'SYSTEM', 50, 500, 0.001, 'Resilience', 2, 634, 2);

INSERT INTO JFW_BROLES_VIEWS (ID, Z_ORG_ID, Z_TENANT_ID, Z_STATUS_ID, BUSINESS_ROLE_ID, VIEW_ID)
VALUES (999999, 0, 'SYSTEM', (SELECT Z_STATUS_ID FROM JFW_BROLES_VIEWS WHERE ROWNUM = 1),
        (SELECT ID FROM JFW_BUSINESS_ROLES WHERE NAME = 'ATSMAKER'), 1204);
INSERT INTO JFW_BROLES_VIEWS (ID, Z_ORG_ID, Z_TENANT_ID, Z_STATUS_ID, BUSINESS_ROLE_ID, VIEW_ID)
VALUES (999998, 0, 'SYSTEM', (SELECT Z_STATUS_ID FROM JFW_BROLES_VIEWS WHERE ROWNUM = 1),
        (SELECT ID FROM JFW_BUSINESS_ROLES WHERE NAME = 'ATSCHECKER'), 1204);

Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT)
values (1, 1, 'MPC', 'SR', 'Status Report', 'pacs.002.001.06', 0);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT)
values (2, 1, 'MPC', 'DB', 'Direct Debit', 'pacs.003.001.01', 1);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT)
values (4, 1, 'MPC', 'CN', 'Cancellation', 'pacs.006.001.05', 0);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT)
values (6, 1, 'MPC', 'CR', 'Direct Credit', 'pacs.008.001.01', 1);


INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (1, 0, 'SYSTEM', '1', 'Reply on credit message', 'Reply-Credit', 1, 6, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (2, 0, 'SYSTEM', '2', 'Reply on debit message', 'Reply-Debit', 1, 2, 2);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (3, 0, 'SYSTEM', '3', 'Cancellation on credit message', 'Cancellation-Credit', 4, 6, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (4, 0, 'SYSTEM', '4', 'Cancellation on debit message', 'Cancellation-Debit', 4, 2, 2);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (5, 0, 'SYSTEM', '5', 'Reply on credit cancellation message', 'Reply-Return-Credit', 1, 4, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID,
                              ROOTMESSAGEID)
values (6, 0, 'SYSTEM', '6', 'Reply on debit cancellation message', 'Reply-Return-Debit', 1, 4, 2);


INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1, 0, 'SYSTEM', '1', 'Invalid account', 'Invalid account', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (2, 0, 'SYSTEM', '2', 'Account is closed/blocked', 'Account is closed/blocked', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (3, 0, 'SYSTEM', '3', 'Deceased account holder reached', 'Deceased account holder reached', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (4, 0, 'SYSTEM', '4', 'Dormant account', 'Dormant account', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (5, 0, 'SYSTEM', '5', 'Insufficient funds', 'Insufficient funds', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (6, 0, 'SYSTEM', '6', 'Duplicate transaction', 'Duplicate transaction', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1121, 0, 'SYSTEM', '1121', 'Unreachable destination', 'Unreachable destination', 1, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (7, 0, 'SYSTEM', '516', 'NoDataFound', 'NoDataFound', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (8, 0, 'SYSTEM', '542', 'FailedToParseSwiftMessage', 'FailedToParseSwiftMessage', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (9, 0, 'SYSTEM', '1008', 'Invalid reason', 'Invalid reason', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1101, 0, 'SYSTEM', '1101', 'Technical error', 'Technical error', 1, 0, 0);


INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 2;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 2;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 3;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 3;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 4;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 4;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 5;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 5;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 6;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 6;

INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID, AUTHORITY_ID)
VALUES (2921218077336, 1);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID, AUTHORITY_ID)
VALUES (2921218077336, 6);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID, AUTHORITY_ID)
VALUES (2921218078230, 1);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID, AUTHORITY_ID)
VALUES (2921218078230, 6);

INSERT INTO JFW_CURRENCIES (ID, Z_ORG_ID, Z_TENANT_ID, STRING_ISO_CODE, DESCRIPTION, ACTIVE, NUMERIC_ISO_CODE)
values (414, 0, 'SYSTEM', 'KWD', 'Kuwaiti Dinar', 0, 414);
INSERT INTO JFW_CURRENCIES (ID, Z_ORG_ID, Z_TENANT_ID, STRING_ISO_CODE, DESCRIPTION, ACTIVE, NUMERIC_ISO_CODE)
values (635, 0, 'SYSTEM', 'USD', 'USD', 0, 635);
INSERT INTO JFW_CURRENCIES (ID, Z_ORG_ID, Z_TENANT_ID, STRING_ISO_CODE, DESCRIPTION, ACTIVE, NUMERIC_ISO_CODE)
values (636, 0, 'SYSTEM', 'JOD', 'JOD', 0, 636);
UPDATE JFW_CURRENCIES
SET JFW_CURRENCIES.ACTIVE = 0
WHERE STRING_ISO_CODE NOT IN ('USD', 'KWD', 'GBP', 'EUR');
UPDATE JFW_CURRENCIES
SET JFW_CURRENCIES.ACTIVE = 1
WHERE STRING_ISO_CODE IN ('USD', 'KWD', 'GBP', 'EUR');

INSERT INTO ATSCAPEXCEEDEDACTIONS (ID, Z_ORG_ID, Z_TENANT_ID, DEBTORPARTICIPANTID, CREDITORPARTICIPANTID, ACTION)
VALUES (1, 0, 'SYSTEM', NULL, NULL, 'RJCT');

INSERT INTO ATSCONFIGS (ID, CONFIGKEY, CONFIGVALUE)
VALUES (1, 'AccountValidationThreadSleepTime', '10000');

--------------------------------------------------------
--  Start of Sessions Data
--------------------------------------------------------
INSERT INTO ATSBDS_TEMPLATES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, PREPSTART, PREPSTOP, CURRENCYID)
VALUES (1, 0, 'SYSTEM', '1', 'BDT', 'Business Day Template', 15, 1, 414);

Insert into ATSBDI_SESSIONS (ID, Z_CREATION_DATE, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, BUSINESSDT,
                             CURRPERIOD, DETAILS, EXTENSIONDURATION, ISSPECIALCLEARING, OPENDT, PERIODEND, PERIODSTART,
                             PRIORITYVALUE, READYFLAG, RETURNCREDITDURATION,RETURNDEBITDURATION,REVERSALCREDITDURATION,REVERSALDEBITDURATION, SESSIONSEQ, SETTRETRY,
                             STARTSTAMP, WEIGHT, TEMPLATEID)
values (1, TIMESTAMP '2019-01-31 00:00:00', 1, 'SYSTEM', '1', 'Demo Session / 1', '1',
        parsedatetime('31-01-2019', 'dd-MM-yyyy'), '2 - Exchange', null, 0, 0,
        parsedatetime('31-01-2019', 'dd-MM-yyyy'), parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.012', 'dd-MM-yyyy hh:mm:ss.SSS'), 'NORM', 1, 5, 5,5,5, '1', 0,
        parsedatetime('31-01-2019 03:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1);

Insert into ATSBDI_SESSIONS (ID, Z_CREATION_DATE, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, BUSINESSDT,
                             CURRPERIOD, DETAILS, EXTENSIONDURATION, ISSPECIALCLEARING, OPENDT, PERIODEND, PERIODSTART,
                             PRIORITYVALUE, READYFLAG, RETURNCREDITDURATION,RETURNDEBITDURATION,REVERSALCREDITDURATION,REVERSALDEBITDURATION, SESSIONSEQ, SETTRETRY,
                             STARTSTAMP, WEIGHT, TEMPLATEID)
values (2, TIMESTAMP '2019-01-30 00:00:00', 1, 'SYSTEM', '2', 'Demo Session / 2', '2',
        parsedatetime('30-01-2019', 'dd-MM-yyyy'), '2 - Exchange', null, 0, 0,
        parsedatetime('30-01-2019', 'dd-MM-yyyy'), parsedatetime('30-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('30-01-2019 03:12:00.012', 'dd-MM-yyyy hh:mm:ss.SSS'), 'NORM', 1, 5,5,5, 5, '1', 0,
        parsedatetime('30-01-2019 03:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1);
Insert into ATSBDI_SESSIONS (ID, Z_CREATION_DATE, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, BUSINESSDT,
                             CURRPERIOD, DETAILS, EXTENSIONDURATION, ISSPECIALCLEARING, OPENDT, PERIODEND, PERIODSTART,
                             PRIORITYVALUE, READYFLAG, RETURNCREDITDURATION,RETURNDEBITDURATION,REVERSALCREDITDURATION,REVERSALDEBITDURATION, SESSIONSEQ, SETTRETRY,
                             STARTSTAMP, WEIGHT, TEMPLATEID)
values (4, TIMESTAMP '2019-01-31 00:00:00', 1, 'SYSTEM', '1', 'Demo Session / 1', '1',
        CURRENT_DATE, '2 - Exchange', null, 0, 0,
        parsedatetime('31-01-2019', 'dd-MM-yyyy'), parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.012', 'dd-MM-yyyy hh:mm:ss.SSS'), 'NORM', 1, 5,5,5, 5, '1', 0,
        parsedatetime('31-01-2019 03:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1);

Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (1, 0, 'SYSTEM', '1', 'Start of day period completed', 'Succeeded', 'Start',
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (2, 0, 'SYSTEM', '2', null, 'Succeeded', 'Exchange',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (3, 0, 'SYSTEM', '3', null, null, 'Cut-Off', parsedatetime('31-01-2019 09:13:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 0, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (4, 0, 'SYSTEM', '4', null, null, 'Settlement Preparations',
        parsedatetime('31-01-2019 09:14:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 09:13:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 0, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (5, 0, 'SYSTEM', '5', null, null, 'Settlement',
        parsedatetime('31-01-2019 09:15:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 09:14:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 0, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (6, 0, 'SYSTEM', '6', null, null, 'Final Cut-Off',
        parsedatetime('31-01-2019 09:16:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 09:15:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 0, 1);
Insert into ATSBDI_PERIODS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DETAILS, EXECUTIONSTATE, NAME, PERIODEND, PERIODSTART,
                            READYFLAG, REFSESSIONID)
values (7, 0, 'SYSTEM', '7', null, null, 'Closure', parsedatetime('31-01-2019 09:17:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 09:16:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 0, 1);

Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (1, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Credit',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 6, 2, null);
Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (2, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Debit',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 2, 2, null);
Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (3, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Credit Reply',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 2, 6);
Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (4, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Debit Reply',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 2, 2);
Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (5, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Credit Cancellation',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 4, 2, 6);
Insert into ATSBDI_WINDOWS (ID, Z_ORG_ID, Z_TENANT_ID, GRACEPERIOD, NAME, WINDOWEND, WINDOWSTART, MSGTYPEID,
                            REFPERIODID, RELATEDMSGTYPEID)
values (6, 0, 'SYSTEM', parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Debit Cancellation',
        parsedatetime('31-01-2019 09:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 4, 2, 2);


INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013781, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, *********, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 4, 1);
INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013782, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, *********, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 5, 1);
INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013783, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, *********, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 6, 1);
--------------------------------------------------------
--  End of Sessions Data
--------------------------------------------------------

insert into ATSTEXTMESSAGES ( Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                             Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                             Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                             Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, FROMPARTICIPANT, MESSAGEBODY, TITLE, Z_DRAFT_ID,
                             Z_STATUS_ID, DIRECTION , TEXTMESSAGEID,MESSAGETYPE)
values ( null, null, null, null, null, null, '2021-10-23 10:58:24', null, null, null, null, null, null, null, null,
        null, '2021-10-24 10:58:24', null, null, null, 'CBO', 'test message pull body', 'test message pull title', null,
        193927649, null , '123456789','Memo');

insert into JFW_WF_STATUS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                           Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                           Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                           Z_WORKFLOW_ID, Z_WS_TOKEN, CODE, DESCRIPTION, ACTIVE, LABEL)
values (193927649,null, null, null, null, null, null, '2021-10-23 10:58:24', null, null, null, null, null, null, null, null,
        null, '2021-10-24 10:58:24', null, null,'5210003','Sent',null,'JFW_WF_STATUS.193927649');

insert into ATSRefTextMessages (REFTEXTMESSAGEID, REFPARTICIPANTID,  Z_CREATED_BY, Z_CREATION_DATE,
                                           Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_ORG_ID, Z_TENANT_ID,
                                           Z_UPDATED_BY, Z_UPDATING_DATE, MESSAGETYPE, RETRYCOUNT, STATUS)
values (1, 6, null, '2021-10-23 10:58:24', null, null, null, null, null, null, '2021-10-24 10:58:24', 'Memo', 0,
        'Pending');


INSERT INTO ATSPRT_BRANCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                            Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                            Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                            Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID,
                            PARTICIPANTID)
VALUES (1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, 'code', null, 'Description', null, null, 3);
INSERT INTO ATSLKP_BATCHSOURCES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                                Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                                Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                                Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID,
                                Z_STATUS_ID)
VALUES (1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, 'code', null, 'name', null, null);

INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                      Z_CREATED_BY,
                      Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                      Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                      Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME,
                      Z_DRAFT_ID, Z_STATUS_ID)
VALUES (1, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, 'Accepted', null,
        'Accepted', null, null);
INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                      Z_CREATED_BY,
                      Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                      Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                      Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME,
                      Z_DRAFT_ID, Z_STATUS_ID)
VALUES (2, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, 'In Process', null,
        'In Process', null, null);
INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                      Z_CREATED_BY,
                      Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                      Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                      Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME,
                      Z_DRAFT_ID, Z_STATUS_ID)
VALUES (3, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null,
        null, null, 'Rejected', null,
        'Rejected', null, null);

-- Debit Pacs 3
INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID)
VALUES (1, null, null, 'CREATED', 1, 1, 'Anas', null, 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, '3-********-100', 3,
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Low', null, 1, 1, 1, 1, 634, 1, 1, 1,
        3, 2, 7, 1, 1, 2, 1);
INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID)
VALUES (2, null, null, 'CREATED', 1, 1, 'Anas', null, 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, '3-********-101', 3,
        parsedatetime('31-02-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Low', null, 1, 1, 1, 1, 634, 1, 1, 1,
        3, 2, 7, 1, 1, 2, 1);
INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,
                       ISPULLED,RcvdForInstdAgent,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID)
VALUES (3, null, null, 'CREATED', 1, 1, 'Anas', null, 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, '3-********-111', 3,
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1,0,0, 'Low', null, 1, 1, 1, 1, 634, 1, 1, 1,
        3, 2, 7, 1, 1, 2, 1);

INSERT INTO ATSBATCHESOUT(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,PULLED,RcvdForInstdAgent,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID, SOURCEBATCHID )
VALUES (99, null, null, 'CREATED', 1, 1, 'Anas', null, 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, '3-********-111', 3,
        parsedatetime('31-01-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1,0,0, 'Low', null, 1, 1, 1, 1, 634, 1, 1, 1,
        3, 2, 7, 1, 1, 2, 1, 3 );


INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                        Z_CREATED_BY,
                        Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                        Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                        Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO,
                        ISPULLED,RcvdForInstdAgent,
                        BATCHID, COUNT, CREATIONDT, Z_DRAFT_ID,
                        Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID,
                        STATEID, WINDOWID,PROCESSINGSTATUS,ISSPLITTED)
VALUES (1, parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        'CREATED', 1, 1, 'Anas', null, 'Salem', 1, parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending', 'This is test data',
        0,0,1,3,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,'Accepted',0);

INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                        Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                        Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                        Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, BATCHID, COUNT, CREATIONDT,
                        Z_DRAFT_ID, Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                        SOURCEID, STATEID, WINDOWID,PROCESSINGSTATUS,ISSPLITTED)
VALUES (2, parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'CREATED', 1, 1, 'Anas',
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending', 'This is test data',
        1, 3, parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,'Accepted',0);

INSERT INTO COMMMESSAGES(ID, CONTENT, DIRECTION, MESSAGEID, PLAYBACKNUMBER, REQUIRESACK, SUSPECTDUPLICATE)
VALUES (55,
STRINGTOUTF8('<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.02"><pacs.002.001.02><GrpHdr><MsgId>999B549017305x</MsgId><CreDtTm>2021-11-05T01:26:22</CreDtTm><InstgAgt><FinInstnId><ClrSysMmbId><Id>999</Id></ClrSysMmbId></FinInstnId></InstgAgt><InstdAgt><FinInstnId><ClrSysMmbId><Id>3</Id></ClrSysMmbId></FinInstnId></InstdAgt></GrpHdr><OrgnlGrpInfAndSts><OrgnlMsgId>3-20211202-3</OrgnlMsgId><OrgnlMsgNmId>pacs.008.001.01</OrgnlMsgNmId><GrpSts>ACTC</GrpSts><StsRsnInf><StsRsn><Prtry>2535</Prtry></StsRsn></StsRsnInf></OrgnlGrpInfAndSts></pacs.002.001.02></Document>'),
'Out', '3-********-111', 1, 1, 0);


INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,
                       ISPULLED,RcvdForInstdAgent,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID)
VALUES (5, null, null, 'CREATED', 1, 1, 'Abdallah', parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, 2, 3,
        parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1,0,0, 'Low', null, 1, 1, 1, 3, 634, 1, 1, 1,
        3, 2, 1101, 1, 1, 3, 1);

INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                        Z_CREATED_BY,
                        Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                        Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                        Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO,
                        ISPULLED,RcvdForInstdAgent,
                        BATCHID, COUNT, CREATIONDT, Z_DRAFT_ID,
                        Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID,
                        STATEID, WINDOWID,PROCESSINGSTATUS,ISSPLITTED)
VALUES (3, parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        'CREATED', 1, 1, 'Anas', parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1, parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending', 'This is test data',
        0,0,1,3,
        parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 3, 1, 3, 1, 1101, 1, 1, 3, 1,'Accepted',0);


INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                       Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                       Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID,
                       Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE,
                       ISPULLED,RcvdForInstdAgent,
                       PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID,
                       INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID,
                       SOURCEID, STATEID, WINDOWID)
VALUES (6, null, null, 'CREATED', 1, 1, 'Abdallah', parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1, null, 1, 'Admin', null, 1, 1, 'Admin', null, 1,
        'Token', 'Pending', 'This is test data', 1400, 3, 3,
        parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1,0,0, 'Low', null, 1, 1, 1, 3, 634, 1, 1, 1,
        3, 2, 1, 1, 1, 3, 1);

INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                        Z_CREATED_BY,
                        Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                        Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
                        Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO,
                        ISPULLED,RcvdForInstdAgent,
                        BATCHID, COUNT, CREATIONDT, Z_DRAFT_ID,
                        Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID,
                        STATEID, WINDOWID,PROCESSINGSTATUS,ISSPLITTED)
VALUES (4, parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        'CREATED', 1, 1, 'Anas', parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1, parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending', 'This is test data',
        0,0,2,3,
        parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 3, 1, 3, 1, 1, 1, 1, 3, 1,'Accepted',0);

insert into ATSRTRANSACTIONS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS,
                              Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE,
                              Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                              Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                              Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT,
                              ORGADDITIONALINFO, TXID, Z_DRAFT_ID, Z_STATUS_ID, BATCHID,
                              CRDTRPARTIID, CURRENCYID, DBTRPARTIID, INSTDPARTIID, INSTGPARTIID,
                              MSGTYPEID, ORGMSGTYPEID, ORGRTXID, ORGREASONID, ORGSTATEID,
                              ORGTXID, REASONID, SESSIONID, STATEID, WINDOWID)
values (1 , parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'CREATED', 1, 1, 'Anas',
        parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending','This is test data',
        1 ,null,'3-********-222-1',1,1,1,1,634,1,1,3,1,1,1,1,1,1,1101,1,3,1);

insert into ATSTRANSACTIONS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                             Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                             Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                             Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT,
                             COLLECTIONDATE,HASHCODEINFO,ISCHARGED,ISDUPLICATE,PAYMENTSEQUENCE,TXID, Z_DRAFT_ID, Z_STATUS_ID, BATCHID, CATPURPID, CRDTRBRANCHID, CRDTRPARTIID,
                             CURRENCYID, DBTRBRANCHID, DBTRPARTIID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID,
                             INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, STATEID, TXPURPID, WINDOWID)
values (1 , parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'CREATED', 1, 1, 'Anas',
        parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending','This is test data',
        1 ,null,1,1,1,1,'3-********-111-1',1,1,2,1,1,1,634,1,1,1,1,1,3,2,1101,1,3,1,1);

insert into ATSRTRANSACTIONS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS,
                              Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE,
                              Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY,
                              Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                              Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT,
                              ORGADDITIONALINFO, TXID, Z_DRAFT_ID, Z_STATUS_ID, BATCHID,
                              CRDTRPARTIID, CURRENCYID, DBTRPARTIID, INSTDPARTIID, INSTGPARTIID,
                              MSGTYPEID, ORGMSGTYPEID, ORGRTXID, ORGREASONID, ORGSTATEID,
                              ORGTXID, REASONID, SESSIONID, STATEID, WINDOWID)
values (2 , parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'CREATED', 1, 1, 'Anas',
        parsedatetime('23-01-2022 09:11:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending','This is test data',
        2 ,null,'3-********-222-2',1,1,1,1,634,1,1,3,1,1,1,1,1,1,1,1,3,1);

insert into ATSTRANSACTIONS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                             Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                             Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                             Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT,
                             COLLECTIONDATE,HASHCODEINFO,ISCHARGED,ISDUPLICATE,PAYMENTSEQUENCE,TXID, Z_DRAFT_ID, Z_STATUS_ID, BATCHID, CATPURPID, CRDTRBRANCHID, CRDTRPARTIID,
                             CURRENCYID, DBTRBRANCHID, DBTRPARTIID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID,
                             INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, STATEID, TXPURPID, WINDOWID)
values (2 , parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'),
        parsedatetime('31-03-2019 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'CREATED', 1, 1, 'Anas',
        parsedatetime('23-01-2022 06:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 'Salem', 1,
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 1, 'Admin',
        parsedatetime('31-03-2020 03:12:00.000', 'dd-MM-yyyy hh:mm:ss.SSS'), 1, 'Token', 'Pending','This is test data',
        2 ,null,1,1,1,1,'3-********-111-2',1,1,2,1,1,1,634,1,1,1,1,1,3,2,1,1,3,1,1);

INSERT INTO ATSRPT_NCP(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, FIRSTAGENTACCOUNTNUMBER, FIRSTAGENTFULLNAME, FIRSTAGENTID, FIRSTAGENTSHORTNAME, PARTICIPATEDSYSTEMS, SECONDAGENTACCOUNTNUMBER, SECONDAGENTFULLNAME, SECONDAGENTID, SECONDAGENTSHORTNAME, SETTLEMENTDT, TOTALCREDIT, TOTALDEBIT, WINDOWCYCLENUMBER, WINDOWPRESETTLEMENTDATE, WINDOWSETTLEMENTENDDATE, WINDOWSETTLEMENTRETRYNUMBER, WINDOWSETTLEMENTSTARTDATE, WINDOWSTATUS, WINDOWTEMPLATENAME, Z_DRAFT_ID, Z_STATUS_ID, COMMAGENTID, CURRENCYID, SESSIONID, SETTAGENTID, ISPULLED)
VALUES(*********, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-23 11:07:01.121000', NULL, NULL, NULL, 0, NULL, NULL, *********, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-23 11:07:01.121000', NULL, NULL, NULL, '1', 'Kuwait National Bank', '1', 'QNB', NULL, NULL, NULL, NULL, NULL, TIMESTAMP '2024-09-23 00:00:00.000000', 0, 0, 1, TIMESTAMP '2024-09-23 00:00:00.000000', TIMESTAMP '2024-09-23 00:00:00.000000', 0, TIMESTAMP '2024-09-23 00:00:00.000000', 'SettlementPending', NULL, NULL, NULL, 1, 634, *********, 1, 0);

INSERT INTO ATSRPT_NCPATT
(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE)
VALUES(*********, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-23 11:07:01.469000', NULL, NULL, NULL, 0, NULL, NULL, *********, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-23 11:07:01.469000', NULL, NULL, '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', NULL, NULL, NULL, 'text/xml', 'com.progressoft.ach.entities.ATSRpt_NCP', NULL, NULL, 'NCP_XML_QNBAQAQAXXX_1_0_1727046000000.xml', NULL, '*********', NULL, NULL, 17726);

INSERT INTO ATSRPT_TRANSACTIONATT
(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE)
VALUES(100009316, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-25 07:41:01.678000', NULL, NULL, NULL, 0, NULL, NULL, 100000141, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-25 07:41:01.678000', NULL, NULL, '1FEFBFBD08000000000000034DEFBFBD410BEFBFBD4014EFBFBD7FEFBFBDEFBFBD6179776BEFBFBDEFBFBD025729EFBFBD5BEFBFBD56EFBFBD75D1872CEFBFBD5BEFBFBD7DEFBFBDEFBFBDEFBFBDEFBFBD4310731A66E69BACEFBFBDEFBFBD5EEFBFBDEFBFBD79634941EFBFBDEFBFBD20EFBFBD1AEFBFBD1AEFBFBD143C1FEFBFBD7807C2B3EFBFBD56EFBFBDEFBFBD50EFBFBDEFBFBD1EEFBFBD3CEFBFBD78EFBFBD70EFBFBDEFBFBDEFBFBD47EFBFBD1E07243E690EEFBFBD54EFBFBDEFBFBD58EFBFBDEFBFBD741BEFBFBDEFBFBDEFBFBD3BEFBFBDEFBFBD000D6102EFBFBDEFBFBDEFBFBD0B665170EFBFBD542046EFBFBDEFBFBD3466EFBFBDEFBFBD0AEFBFBDEFBFBDEFBFBD5006EFBFBD750D7FEFBFBD0AD985EFBFBDEFBFBD3C12EFBFBDEFBFBDEFBFBDEFBFBD7F0061EFBFBD25EFBFBDEFBFBD000000', NULL, NULL, NULL, 'text/xml', 'com.progressoft.ach.entities.ATSRpt_Transaction', NULL, NULL, 'Tx_QNBAQAQAXXX_1_0_1727218800000.xml', NULL, '100009315', NULL, NULL, 315);

INSERT INTO ATSRPT_TRANSACTIONS
(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, NOTES, SETTRETRY, SETTLEMENTDT, Z_DRAFT_ID, Z_STATUS_ID, COMMAGENTID, CURRENCYID, SESSIONID, SETTAGENTID, ISPULLED)
VALUES(100009315, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-25 07:41:01.673000', NULL, NULL, NULL, 0, NULL, NULL, 100000141, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-25 07:41:01.673000', NULL, NULL, NULL, NULL, 0, TIMESTAMP '2024-09-25 00:00:00.000000', NULL, NULL, 1, 634, 4, 1, 0);
INSERT INTO ATSRPT_RECON (
    ID,
    Z_ARCHIVE_ON,
    Z_ARCHIVE_QUEUED,
    Z_ARCHIVE_STATUS,
    Z_ASSIGNED_GROUP,
    Z_ASSIGNED_USER,
    Z_CREATED_BY,
    Z_CREATION_DATE,
    Z_DELETED_BY,
    Z_DELETED_FLAG,
    Z_DELETED_ON,
    Z_EDITABLE,
    Z_LOCKED_BY,
    Z_LOCKED_UNTIL,
    Z_ORG_ID,
    Z_TENANT_ID,
    Z_UPDATED_BY,
    Z_UPDATING_DATE,
    Z_WORKFLOW_ID,
    Z_WS_TOKEN,
    Z_DRAFT_STATUS,
    DESCRIPTION,
    SETTRETRY,
    Z_DRAFT_ID,
    Z_STATUS_ID,
    COMMAGENTID,
    CURRENCYID,
    SESSIONID,
    SETTAGENTID,
    ISPULLED
) VALUES (
             1,
             CURRENT_TIMESTAMP,
             CURRENT_TIMESTAMP,
             'Archived',
             1001,
             1002,
             'admin',
             CURRENT_TIMESTAMP,
             NULL,
             0,
             NULL,
             1,
             'admin',
             CURRENT_TIMESTAMP + INTERVAL '1' DAY,
             1010,
             'ATS',
             'SYSTEM',
             CURRENT_TIMESTAMP,
             1020,
             'token123',
             'Draft',
             2,
             0,
             1030,
             1040,
             1,
             634,
             4,
             1,
          0
         );
INSERT INTO ATSRPT_RECON (
    ID,
    Z_ARCHIVE_ON,
    Z_ARCHIVE_QUEUED,
    Z_ARCHIVE_STATUS,
    Z_ASSIGNED_GROUP,
    Z_ASSIGNED_USER,
    Z_CREATED_BY,
    Z_CREATION_DATE,
    Z_DELETED_BY,
    Z_DELETED_FLAG,
    Z_DELETED_ON,
    Z_EDITABLE,
    Z_LOCKED_BY,
    Z_LOCKED_UNTIL,
    Z_ORG_ID,
    Z_TENANT_ID,
    Z_UPDATED_BY,
    Z_UPDATING_DATE,
    Z_WORKFLOW_ID,
    Z_WS_TOKEN,
    Z_DRAFT_STATUS,
    DESCRIPTION,
    SETTRETRY,
    Z_DRAFT_ID,
    Z_STATUS_ID,
    COMMAGENTID,
    CURRENCYID,
    SESSIONID,
    SETTAGENTID,
    ISPULLED
) VALUES (
             2,
             CURRENT_TIMESTAMP,
             CURRENT_TIMESTAMP,
             'Archived',
             1001,
             1002,
             'admin',
             CURRENT_TIMESTAMP,
             NULL,
             0,
             NULL,
             1,
             'admin',
             CURRENT_TIMESTAMP + INTERVAL '1' DAY,
             1010,
             'ATS',
             'SYSTEM',
             CURRENT_TIMESTAMP,
             1020,
             'token123',
             'Draft',
             2,
             0,
             1030,
             1040,
             1,
             634,
             2,
             1,
             0
         );

INSERT INTO ATSReportRecord (
    ID,
    Z_ARCHIVE_ON,
    Z_ARCHIVE_QUEUED,
    Z_ARCHIVE_STATUS,
    Z_ASSIGNED_GROUP,
    Z_ASSIGNED_USER,
    Z_CREATED_BY,
    Z_CREATION_DATE,
    Z_DELETED_BY,
    Z_DELETED_FLAG,
    Z_DELETED_ON,
    Z_EDITABLE,
    Z_LOCKED_BY,
    Z_LOCKED_UNTIL,
    Z_ORG_ID,
    Z_TENANT_ID,
    Z_UPDATED_BY,
    Z_UPDATING_DATE,
    Z_WORKFLOW_ID,
    Z_WS_TOKEN,
    Z_DRAFT_STATUS,
    Z_DRAFT_ID,
    Z_STATUS_ID,
    COMMAGENTID,
    CURRENCYID,
    SESSIONID,
    SETTAGENTID,
    REPORTCOMMUNICATIONAUTOISPULLED,
    REPORTSETTLEMENTAUTOISPULLED,
    ROWCOUNT,
    WINDOWSETTLEMENTRETRYNUMBER

) VALUES (
             1,
             CURRENT_TIMESTAMP,
             CURRENT_TIMESTAMP,
             'Archived',
             1001,
             1002,
             'admin',
             CURRENT_TIMESTAMP,
             NULL,
             0,
             NULL,
             1,
             'admin',
             CURRENT_TIMESTAMP + INTERVAL '1' DAY,
             1010,
             'ATS',
             'SYSTEM',
             CURRENT_TIMESTAMP,
             1020,
             'token123',
             'Draft',
             1030,
             1040,
             1,
             634,
             4,
             1,
             0,
             0,
             0,
             0
         );
