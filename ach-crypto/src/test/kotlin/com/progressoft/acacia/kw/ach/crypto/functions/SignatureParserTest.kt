package com.progressoft.acacia.kw.ach.crypto.functions

import com.progressoft.acacia.kw.ach.crypto.functions.TestUtils.getMessageContent
import junit.framework.TestCase.assertEquals
import junit.framework.TestCase.assertNotNull
import org.junit.Assert.assertArrayEquals
import org.junit.Before
import org.junit.Test
import java.text.SimpleDateFormat
import java.util.Base64
import java.util.Date
import java.util.TimeZone.getTimeZone
import java.util.TimeZone.setDefault

class SignatureParserTest {
    private lateinit var parser: SignatureParser
    private val testDate = Date(1620000000000L)

    @Before
    fun setUp() {
        setDefault(getTimeZone("UTC"))
        parser = SignatureParser
    }

    @Test
    fun givenValidSignatureXml_whenParsing_thenReturnCorrectSignatureDto() {
        val xmlMessage = getMessageContent("valid_signature.xml")

        val dateFormat = SimpleDateFormat("yyyy-MM-dd")
        dateFormat.timeZone = getTimeZone("UTC")
        val settlementDate = dateFormat.format(testDate)
        val updatedXmlMessage = xmlMessage.replace("\${settlementDate}", settlementDate)

        val resultDto = parser.parse(updatedXmlMessage)

        assertNotNull(resultDto)

        assertEquals("http://www.w3.org/2001/10/xml-exc-c14n#", resultDto.canonicalizationMethod)
        assertEquals("http://www.w3.org/2000/09/xmldsig#rsa-sha1", resultDto.signatureMethod)

        assertNotNull(resultDto.references)
        assertEquals(3, resultDto.references?.size)

        val reference1 = resultDto.references?.get(0)
        assertEquals("#_6fac6c03-2477-4c3d-9aff-e45d1e6cfb58", reference1?.uri)
        assertEquals("http://www.w3.org/2000/09/xmldsig#sha1", reference1?.digestMethod)
        assertArrayEquals(Base64.getDecoder().decode("fPg5YnJhnswplCeFFmkOtiNwIc8="), reference1?.digestValue)
        assertEquals(1, reference1?.transforms?.size)
        assertEquals("http://www.w3.org/2001/10/xml-exc-c14n#", reference1?.transforms?.get(0)?.algorithm)

        val reference2 = resultDto.references?.get(1)
        assertEquals("#_409a80de-3a9b-41b0-b884-cee127b3b85d", reference2?.uri)
        assertEquals("http://uri.etsi.org/01903/v1.3.2#SignedProperties", reference2?.type)
        assertEquals("http://www.w3.org/2000/09/xmldsig#sha1", reference2?.digestMethod)
        assertArrayEquals(Base64.getDecoder().decode("MtBA/wI8/XiBIeXCEcVxLe7hBDc="), reference2?.digestValue)

        val reference3 = resultDto.references?.get(2)
        assertEquals("http://www.w3.org/2000/09/xmldsig#sha1", reference3?.digestMethod)
        assertArrayEquals(Base64.getDecoder().decode("sMCRAyTGyKrgv766/MaJ4DSLANU="), reference3?.digestValue)

        assertArrayEquals(
            Base64.getDecoder()
                .decode("Js56xLg/2WGq92jJulx1rijjsiynKxnuoGcYf8VPzeUPaHK6pDWAp+Z1HEw0peetyruRZ2iEqEeWvAfEuKcQQcQzEgK5uud+keQ+6oNvdaxw+WOD0bDMIVc3rfGFYysPOHXCVZeyXRvoL5hWO6oEpUfo1gybKBGWmQO7BNR4d4nEItjbZJI34OHTqZEOrzmzETv4f1TKqLtYbyBtg4E/14GBV5zc4gCrE6JMzmdiLMR8D/w3g3oJknNfftwLTtW6PDv8U3F8JgB1ZDTO0MYIsP2B0yxbhGO+Tl+4v5mNpDmo1agLbWvbnXqbsyBZPUCIn3KUokqjZmP3Y2aYjYdKJQ=="),
            resultDto.signatureValue
        )

        assertEquals("_6fac6c03-2477-4c3d-9aff-e45d1e6cfb58", resultDto.keyInfoId)
        assertEquals(
            "CN=PACI Demonstration CA,O=The Public Authority for Civil Information,C=KW",
            resultDto.x509IssuerName
        )
        assertEquals("152155929739110160509761661623980731340", resultDto.x509SerialNumber)

        assertEquals("_409a80de-3a9b-41b0-b884-cee127b3b85d", resultDto.signedPropertiesId)

        val expectedDate = SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'").parse("${settlementDate}T07:31:14Z")
        assertEquals(expectedDate, resultDto.signingTime)
    }
}