package com.progressoft.ach.common.requests;

import com.progressoft.ach.common.dto.AttachmentDto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class WindowAdditionRequest {
    public static final String WINDOW_ADDITION_REQUEST = "WindowAdditionRequest";

    private long duration;
    private String requestReasonCode;
    private String sessionSequence;
    private String periodName;
    private String windowName;
    private String messageTypeCode;
    private String[] purposeCodes;
    private String requestId;
    private String requestingParticipantCode;
    private Date startDate;
    private Date endDate;
    private List<AttachmentDto> attachments = new ArrayList<>();

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getRequestingParticipantCode() {
        return requestingParticipantCode;
    }

    public void setRequestingParticipantCode(String requestingParticipantCode) {
        this.requestingParticipantCode = requestingParticipantCode;
    }

    public WindowAdditionRequest() {
        purposeCodes = new String[]{};
    }

    public String getRequestReasonCode() {
        return requestReasonCode;
    }

    public void setRequestReasonCode(String requestReasonCode) {
        this.requestReasonCode = requestReasonCode;
    }

    public String getSessionSequence() {
        return sessionSequence;
    }

    public void setSessionSequence(String sessionSequence) {
        this.sessionSequence = sessionSequence;
    }

    public String getPeriodName() {
        return periodName;
    }

    public void setPeriodName(String periodName) {
        this.periodName = periodName;
    }

    public String getWindowName() {
        return windowName;
    }

    public void setWindowName(String windowName) {
        this.windowName = windowName;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public String getMessageTypeCode() {
        return messageTypeCode;
    }

    public void setMessageTypeCode(String messageTypeCode) {
        this.messageTypeCode = messageTypeCode;
    }

    public String[] getPurposeCodes() {
        return purposeCodes;
    }

    public void setPurposeCodes(String[] purposeCodes) {
        this.purposeCodes = purposeCodes;
    }

    public List<AttachmentDto> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<AttachmentDto> attachments) {
        this.attachments = attachments;
    }
}
