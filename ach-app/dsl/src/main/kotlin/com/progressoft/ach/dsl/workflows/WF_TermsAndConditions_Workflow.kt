package com.progressoft.ach.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_TermsAndConditions_Workflow = Workflow {
    name = "WF_TermsAndConditions"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "Initialize_300004245"
        actionCode = "1"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"

        result {
            displayName = "Initialization-Initialize-EditStep"
            nextStepKey = 554001
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "Create_300004251"
        doRefresh = true
        actionCode = "2"
        serviceAction = true
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "DRAFT"
        actionLevel = "Maker Level"

        result {
            displayName = "Initialization-Create-EditStep"
            nextStepKey = 554001
        }
    }
    step {
        key = 554001
        name = "Submission"
        editable = true
        statusCode = "554001"
        action {
            key = 12916492
            name = "Submit"
            label = "JFW_WF_ACTION.1839588641"
            actionCode = "21"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "Submission-Submit-CreationApproval"
                nextStepKey = 554002
            }
        }
    }

    step {
        key = 554002
        name = "CreationApproval"
        editable = false
        statusCode = "554002"
        action {
            key = 270088122
            name = "Approve"
            label = "JFW_WF_ACTION.1894328455"
            doRefresh = true
            actionCode = "3"
            notify = true
            notificationTimeToLive = 0
            sendIdOnly = true
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            tenantAware = true
            actionLevel = "Checker Level"

            result {
                displayName = "CreationApproval-Approve-ViewApproved"
                nextStepKey = 554003
            }
        }
    }
    step {
        key = 554003
        name = "ViewApproved"
        editable = false
        statusCode = "554003"
        action {
            key = 2009206734
            name = "Edit"
            label = "JFW_WF_ACTION.1906371997"
            actionCode = "9"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "ViewApproved-Edit-EditApproved"
                nextStepKey = 554004
            }
        }
    }
    step {
        key = 554004
        name = "EditApproved"
        editable = true
        statusCode = "554004"
        action {
            key = 38003820
            name = "Save"
            label = "JFW_WF_ACTION.1405511607"
            doRefresh = true
            actionCode = "11"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "EditApproved-Save-ModificationApproval"
                nextStepKey = 554006
            }
        }
        action {
            key = 860404382
            name = "Cancel"
            label = "JFW_WF_ACTION.859189834"
            actionCode = "12"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "EditApproved-Cancel-ViewApproved"
                nextStepKey = 554003
            }
        }
    }

    step {
        key = 554006
        name = "ModificationApproval"
        editable = false
        statusCode = "554006"
        action {
            key = 765069488
            name = "Reject"
            label = "JFW_WF_ACTION.1227581139"
            actionCode = "14"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"

            result {
                displayName = "ModificationApproval-Reject-RepairRejected"
                nextStepKey = 554007
            }
        }
        action {
            key = 1708299783
            name = "Approve"
            label = "JFW_WF_ACTION.1894328455"
            doRefresh = true
            actionCode = "13"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "resetAcceptTermsFunction"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "resetAcceptTermsFunction"
                }
            }

            result {
                displayName = "ModificationApproval-Approve-ViewApproved"
                nextStepKey = 554003
            }
        }
    }

    step {
        key = 554007
        name = "RepairRejected"
        editable = false
        statusCode = "554007"
        action {
            key = 1003864638
            name = "Reset"
            label = "reset"
            doRefresh = true
            actionCode = "16"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "REMOVE_DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "RepairRejected-Reset-ViewApproved"
                nextStepKey = 554003
            }
        }
        action {
            key = 1088063286
            name = "Edit"
            label = "JFW_WF_ACTION.1906371997"
            actionCode = "15"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "RepairRejected-Edit-EditRejected"
                nextStepKey = 554008
            }
        }
    }
    step {
        key = 554008
        name = "EditRejected"
        editable = true
        statusCode = "554008"
        action {
            key = 2002595504
            name = "Save"
            label = "save"
            doRefresh = true
            actionCode = "17"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "EditRejected-Save-ModificationApproval"
                nextStepKey = 554006
            }
        }
        action {
            key = 2106464816
            name = "Cancel"
            label = "JFW_WF_ACTION.859189834"
            doRefresh = true
            actionCode = "18"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "EditRejected-Cancel-RepairRejected"
                nextStepKey = 554007
            }
        }
    }

}