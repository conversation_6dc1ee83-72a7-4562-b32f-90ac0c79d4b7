package com.progressoft.ach.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val ATSRpt_Recon_Detail_Entity = Entity {
	name = "com.progressoft.ach.entities.ATSRpt_Recon_Detail"
	property { 
		name="report"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1934592462"
	 }
	property { 
		name="deletedFlag"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1817226891"
	 }
	property { 
		name="deletedBy"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1997294634"
	 }
	property { 
		name="lockedBy"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1159699829"
	 }
	property { 
		name="updatedBy"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1849897279"
	 }
	property { 
		name="createdBy"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1867838292"
	 }
	property { 
		name="deletedOn"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.123235881"
	 }
	property { 
		name="lockedUntil"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.2008729828"
	 }
	property { 
		name="updatingDate"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1509734788"
	 }
	property { 
		name="creationDate"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1609531839"
	 }
	property { 
		name="statusId.description"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1174610657"
	 }
	property { 
		name="statusId.codeNamePair"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1645721355"
	 }
	property { 
		name="statusId.id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.466318570"
	 }
	property { 
		name="statusId"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.55012323"
	 }
	property { 
		name="report.currency"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.871112595"
	 }
	property { 
		name="key"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1254954682"
	 }
	property { 
		name="section"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.85589539"
	 }
	property { 
		name="description"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.285932879"
	 }
	property { 
		name="count"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1111155782"
	 }
	property { 
		name="amount"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.391309314"
	 }
	property { 
		name="id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1623489148"
	 }
	property { 
		name="report.id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.101059262"
	 }
	property { 
		name="report.description"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1390995964"
	 }
	property { 
		name="report.currency.id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1898347697"
	 }
	property { 
		name="report.currency.codeNamePair"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1977738527"
	 }
	property { 
		name="report.currency.stringISOCode"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1359642108"
	 }
	property { 
		name="orgId"
		showAsResultColumn=0
		label = "orgId"
	 }
	property { 
		name="draftStatus"
		showAsResultColumn=0
		label = "draftStatus"
	 }
	property { 
		name="jfwDraft.id"
		showAsResultColumn=0
		label = "jfwDraft.id"
	 }
	property { 
		name="jfwDraft.draftData"
		showAsResultColumn=0
		label = "jfwDraft.draftData"
	 }
}