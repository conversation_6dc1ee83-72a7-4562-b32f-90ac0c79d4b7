package com.progressoft.ach.dsl.views

import com.progressoft.jupiter.kotlin.dsl.Portlet
import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val ATSLKP_Creditors_View = View {
	hasMasterDraft
	name = "ATSLKP_Creditors.View"
	label="JFW_VIEWS.1952342618"
	workflowName = "WF_MakerCheckerLookups"
	
	entity = "com.progressoft.ach.entities.ATSLKP_Creditor"
	idProperty = "id"
	newPortal {
		changeHandlerBean = "creditorsLookupChangeHandler"
		column {
			width = 8
			portlet {
				label = "JFW_PORTLETS.1238673117"
				form {
					label = "JFW_PORTLETS.1238673117"
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyCode"
							changeHandlerBean = "creditorCompanyCodeChangeHandler"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 12
						}
						text {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "companyName"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 70
						}
						comboBox {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyBank"
							changeHandlerBean = "creditorBankChangeHandler"
							filling = Filling.REQUIRED
						}
						comboBox {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyBankBranch"
							filling = Filling.REQUIRED
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyAccNo"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 34
						}
						comboBox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "companyIdType"
							changeHandlerBean = "companyIdTypeChangeHandler"
							filling = Filling.OPTIONAL
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyIdValue"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 35
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "address"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 128
						}
						checkbox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "isTechnicalParticipant"
							changeHandlerBean = "enableParticipantValue"
							filling = Filling.OPTIONAL
						}
						comboBox {
							label = "JFW_PROPERTIES.1923833996"
							propertyPath = "participant"
							filling = Filling.OPTIONAL
						}
						date {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "effectiveStartDt"
							filling = Filling.REQUIRED
						}
						date {
							label = "JFW_PROPERTIES.1735413653"
							propertyPath = "effectiveEndDt"
							filling = Filling.REQUIRED
						}
						checkbox {
							label = "JFW_PROPERTIES.2045510345"
							propertyPath = "activeStatus"
							filling = Filling.OPTIONAL
						}
						checkbox {
							label = "JFW_PROPERTIES.1414942187"
							propertyPath = "nonCancelable"
							filling = Filling.OPTIONAL
						}
				}
			}
			portlet {
				label = "JFW_PORTLETS.1735803760"
			}
		}
		column {
			width = 4
			portlet {
				label = "JFW_PORTLETS.213463009"
				form {
					label = "JFW_PORTLETS.213463009"
						comboBox {
							label = "JFW_PROPERTIES.1898592837"
							propertyPath = "statusId"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.1503060446"
							propertyPath = "creationDate"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.446944972"
							propertyPath = "updatingDate"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.1009779521"
							propertyPath = "lockedUntil"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.713086264"
							propertyPath = "deletedOn"
							filling = Filling.READ_ONLY
						}
						text {
							label = "JFW_PROPERTIES.390416915"
							propertyPath = "createdBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.592960621"
							propertyPath = "updatedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.930035044"
							propertyPath = "lockedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.1110903249"
							propertyPath = "deletedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						checkbox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "deletedFlag"
							filling = Filling.READ_ONLY
						}
				}
			}
		}
	}
	portal {
		changeHandlerBean = "creditorsLookupChangeHandler"
		column {
			width = 8
			portlet {
				label = "JFW_PORTLETS.*********"
				form {
					label = "JFW_PORTLETS.*********"
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyCode"
							changeHandlerBean = "creditorCompanyCodeChangeHandler"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 12
						}
						text {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "companyName"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 70
						}
						comboBox {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyBank"
							changeHandlerBean = "creditorBankChangeHandler"
							filling = Filling.REQUIRED
						}
						comboBox {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyBankBranch"
							filling = Filling.REQUIRED
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyAccNo"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 34
						}
						comboBox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "companyIdType"
							changeHandlerBean = "companyIdTypeChangeHandler"
							filling = Filling.OPTIONAL
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "companyIdValue"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 35
						}
						text {
							label = "JFW_PROPERTIES.**********"
							propertyPath = "address"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 128
						}
						checkbox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "isTechnicalParticipant"
							changeHandlerBean = "enableParticipantValue"
							filling = Filling.OPTIONAL
						}
						comboBox {
							label = "JFW_PROPERTIES.1923833996"
							propertyPath = "participant"
							filling = Filling.OPTIONAL
						}
						date {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "effectiveStartDt"
							filling = Filling.REQUIRED
						}
						date {
							label = "JFW_PROPERTIES.1735413653"
							propertyPath = "effectiveEndDt"
							filling = Filling.REQUIRED
						}
						checkbox {
							label = "JFW_PROPERTIES.2045510345"
							propertyPath = "activeStatus"
							filling = Filling.OPTIONAL
						}
						checkbox {
							label = "JFW_PROPERTIES.1414942187"
							propertyPath = "nonCancelable"
							filling = Filling.OPTIONAL
						}
				}
			}
			portlet {
				label = "JFW_PORTLETS.358596991"
			}
		}
		column {
			width = 4
			portlet {
				label = "JFW_PORTLETS.514851688"
				form {
					label = "JFW_PORTLETS.514851688"
						comboBox {
							label = "JFW_PROPERTIES.1898592837"
							propertyPath = "statusId"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.1503060446"
							propertyPath = "creationDate"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.446944972"
							propertyPath = "updatingDate"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.1009779521"
							propertyPath = "lockedUntil"
							filling = Filling.READ_ONLY
						}
						dateTime {
							label = "JFW_PROPERTIES.713086264"
							propertyPath = "deletedOn"
							filling = Filling.READ_ONLY
						}
						text {
							label = "JFW_PROPERTIES.390416915"
							propertyPath = "createdBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.592960621"
							propertyPath = "updatedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.930035044"
							propertyPath = "lockedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						text {
							label = "JFW_PROPERTIES.1110903249"
							propertyPath = "deletedBy"
							filling = Filling.READ_ONLY
							minLength = 0
							maxLength = 200
						}
						checkbox {
							label = "JFW_PROPERTIES.*********"
							propertyPath = "deletedFlag"
							filling = Filling.READ_ONLY
						}
				}
			}
		}
	}
}
