package com.progressoft.ach.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_PRTSuspensions_Workflow = Workflow {
    name = "WF_PRTSuspensions"
    status = true
    initialAction {
        key = 10
        name = "Initialize"
        label = "JFW_WF_ACTION.1724742796"
        actionCode = "1"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        
        result {
            displayName = "Initialization-Initialize-DataEntry"
            nextStepKey = 1102
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "JFW_WF_ACTION.878677336"
        doRefresh = true
        actionCode = "2"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "DRAFT"
        actionLevel = "Maker Level"
        
        result {
            displayName = "Initialization-Create-DataEntry"
            nextStepKey = 1102
        }
    }
    step {
        key = 1102
        name = "DataEntry"
        editable = true
        statusCode = "1102"
        action {
            key = 538516847
            name = "Save"
            label = "JFW_WF_ACTION.833650111"
            actionCode = "14"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "DataEntry-Save-DataEntry"
                remainOnStep = true
                conditional = false
                nextStepKey = 1102
            }
        }
        action {
            key = 1469253748
            name = "Delete"
            label = "JFW_WF_ACTION.1867732202"
            actionCode = "15"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "DataEntry-Delete-End"
                nextStepKey = 1107
            }
        }
        action {
            key = 2115042196
            name = "Submit"
            label = "JFW_WF_ACTION.767110719"
            actionCode = "13"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            function {
                functionKey = null
                name = "pendingParticipantSuspValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "pendingParticipantSuspValidator"
                }
            }
            result {
                displayName = "DataEntry-Submit-CreationApproval"
                nextStepKey = 1109
            }
        }
    }
    step {
        key = 1109
        name = "CreationApproval"
        editable = false
        statusCode = "1109"
        action {
            key = 1580178338
            name = "Approve"
            label = "JFW_WF_ACTION.112971357"
            doRefresh = true
            actionCode = "3"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "CreationApproval-Approve-PendingRequest"
                nextStepKey = 1111
            }
        }
        action {
            key = 2138623372
            name = "Reject"
            label = "JFW_WF_ACTION.118679809"
            actionCode = "4"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "CreationApproval-Reject-RepairNew"
                nextStepKey = 1103
            }
        }
    }
    step {
        key = 1111
        name = "PendingRequest"
        editable = false
        statusCode = "1111"
        action {
            key = 371587118
            name = "Cancel"
            label = "JFW_WF_ACTION.682555712"
            doRefresh = true
            actionCode = "6"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "PendingRequest-Cancel-CancelationApproval"
                nextStepKey = 1105
            }
        }
        action {
            key = 1087268725
            name = "SVC_Proceed"
            label = "JFW_WF_ACTION.1961552480"
            actionCode = "5"
            serviceAction = true
            notificationTimeToLive = 0
            actionMode = "NORMAL"
            actionLevel = "Neutral"

            function {
                functionKey = null
                name = "copyParticipantSuspensionFlags"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "copyParticipantSuspensionFlags"
                }
            }
            result {
                displayName = "PendingRequest-SVC_Proceed-ViewProcessed"
                nextStepKey = 1106
            }
        }
    }
    step {
        key = 1103
        name = "RepairNew"
        editable = false
        statusCode = "1103"
        action {
            key = 1409432726
            name = "Edit"
            label = "JFW_WF_ACTION.1219216547"
            actionCode = "7"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "RepairNew-Edit-EditNew"
                nextStepKey = 1104
            }
        }
        action {
            key = 1982167764
            name = "Delete"
            label = "JFW_WF_ACTION.1431825066"
            doRefresh = true
            actionCode = "8"
            notificationTimeToLive = 0
            deleteItem = "DELETE"
            actionMode = "REMOVE_DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "RepairNew-Delete-End"
                nextStepKey = 1107
            }
        }
    }
    step {
        key = 1104
        name = "EditNew"
        editable = true
        statusCode = "1104"
        action {
            key = 9003377
            name = "Cancel"
            label = "JFW_WF_ACTION.267726510"
            doRefresh = true
            actionCode = "10"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditNew-Cancel-RepairNew"
                nextStepKey = 1103
            }
        }
        action {
            key = 279835804
            name = "Save"
            label = "JFW_WF_ACTION.102114271"
            doRefresh = true
            actionCode = "9"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            function {
                functionKey = null
                name = "pendingParticipantSuspValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "pendingParticipantSuspValidator"
                }
            }
            result {
                displayName = "EditNew-Save-CreationApproval"
                nextStepKey = 1109
            }
        }
    }
    step {
        key = 1105
        name = "CancelationApproval"
        editable = false
        statusCode = "1105"
        action {
            key = 850407510
            name = "Approve"
            label = "JFW_WF_ACTION.496341072"
            doRefresh = true
            actionCode = "11"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "CancelationApproval-Approve-Canceled"
                nextStepKey = 1108
            }
        }
        action {
            key = 1305183209
            name = "Reject"
            label = "JFW_WF_ACTION.601845143"
            actionCode = "12"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "CancelationApproval-Reject-RepairNew"
                nextStepKey = 1103
            }
        }
    }
    step {
        key = 1106
        name = "ViewProcessed"
        editable = false
        statusCode = "1106"
    }
    step {
        key = 1107
        name = "End"
        editable = false
        statusCode = "1107"
    }
    step {
        key = 1108
        name = "Canceled"
        editable = false
        statusCode = "1108"
    }
}