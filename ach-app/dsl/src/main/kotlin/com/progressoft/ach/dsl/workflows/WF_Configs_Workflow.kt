package com.progressoft.ach.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_Configs_Workflow = Workflow {
    name = "WF_Configs"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "JFW_WF_ACTION.569380699"
        actionCode = "1"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        
        result {
            displayName = "Initialization-Initialize-CreationApproval"
            nextStepKey = 4403
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "JFW_WF_ACTION.911736379"
        doRefresh = true
        actionCode = "2"
        serviceAction = false
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "DRAFT"
        actionLevel = "Maker Level"
        
        result {
            displayName = "Initialization-Create-Submission"
            nextStepKey = 4402
        }
    }
    step {
        key = 4402
        name = "Submission"
        editable = true
        statusCode = "4402"
        action {
            key = 782125623
            name = "Submit"
            label = "JFW_WF_ACTION.1262620834"
            actionCode = "3"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "Submission-Submit-CreationApproval"
                nextStepKey = 4403
            }
        }
    }
    step {
        key = 4403
        name = "CreationApproval"
        editable = false
        statusCode = "4403"
        action {
            key = 1727727564
            name = "Approve"
            label = "JFW_WF_ACTION.1122244157"
            doRefresh = true
            actionCode = "5"
            notify = false
            notificationTimeToLive = 0
            sendIdOnly = false
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            tenantAware = false
            actionLevel = "Checker Level"
            
            result {
                displayName = "CreationApproval-Approve-ViewApproved"
                nextStepKey = 4406
            }
        }
        action {
            key = 905485869
            name = "Reject"
            label = "JFW_WF_ACTION.1449956575"
            actionCode = "6"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "CreationApproval-Reject-RepairNew"
                nextStepKey = 4404
            }
        }
    }
    step {
        key = 4404
        name = "RepairNew"
        editable = false
        statusCode = "4404"
        action {
            key = 1783378503
            name = "Edit"
            label = "JFW_WF_ACTION.2132993159"
            actionCode = "7"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "RepairNew-Edit-EditNew"
                nextStepKey = 4405
            }
        }
    }
    step {
        key = 4405
        name = "EditNew"
        editable = true
        statusCode = "4405"
        action {
            key = 562507207
            name = "Cancel"
            label = "JFW_WF_ACTION.38381903"
            doRefresh = true
            actionCode = "10"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditNew-Cancel-RepairNew"
                nextStepKey = 4404
            }
        }
        action {
            key = 1210429614
            name = "Save"
            label = "JFW_WF_ACTION.297488875"
            doRefresh = false
            actionCode = "9"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditNew-Save-EditNew"
                nextStepKey = 4405
            }
        }
        action {
            key = 334411069
            name = "Submit"
            label = "JFW_WF_ACTION.351767477"
            actionCode = "24"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditNew-Submit-CreationApproval"
                nextStepKey = 4403
            }
        }
    }
    step {
        key = 4406
        name = "ViewApproved"
        editable = false
        statusCode = "4406"
        action {
            key = 1215374138
            name = "Edit"
            label = "JFW_WF_ACTION.1665131023"
            actionCode = "11"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "ViewApproved-Edit-EditApproved"
                nextStepKey = 4407
            }
        }
    }
    step {
        key = 4407
        name = "EditApproved"
        editable = true
        statusCode = "4407"
        action {
            key = 760733908
            name = "Save"
            label = "JFW_WF_ACTION.1687502532"
            doRefresh = false
            actionCode = "13"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditApproved-Save-EditApproved"
                nextStepKey = 4407
            }
        }
        action {
            key = 801653734
            name = "Cancel"
            label = "JFW_WF_ACTION.1019752038"
            actionCode = "14"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditApproved-Cancel-ViewApproved"
                nextStepKey = 4406
            }
        }
        action {
            key = 1038180185
            name = "Submit"
            label = "JFW_WF_ACTION.1208750466"
            actionCode = "25"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditApproved-Submit-ModificationApproval"
                nextStepKey = 4408
            }
        }
    }
    step {
        key = 4408
        name = "ModificationApproval"
        editable = false
        statusCode = "4408"
        action {
            key = 1673432679
            name = "Approve"
            label = "JFW_WF_ACTION.765342956"
            doRefresh = true
            actionCode = "15"
            notify = false
            notificationTimeToLive = 0
            sendIdOnly = false
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            tenantAware = false
            actionLevel = "Checker Level"
            
            result {
                displayName = "ModificationApproval-Approve-ViewApproved"
                nextStepKey = 4406
            }
        }
        action {
            key = 1130884664
            name = "Reject"
            label = "JFW_WF_ACTION.1851182983"
            actionCode = "16"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Checker Level"
            
            result {
                displayName = "ModificationApproval-Reject-RepairRejected"
                nextStepKey = 4409
            }
        }
    }
    step {
        key = 4409
        name = "RepairRejected"
        editable = false
        statusCode = "4409"
        action {
            key = 297500116
            name = "Edit"
            label = "JFW_WF_ACTION.13589064"
            actionCode = "17"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "RepairRejected-Edit-EditRejected"
                nextStepKey = 4410
            }
        }
        action {
            key = 1750399184
            name = "Reset"
            label = "JFW_WF_ACTION.1642748129"
            doRefresh = true
            actionCode = "18"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "REMOVE_DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "RepairRejected-Reset-ViewApproved"
                nextStepKey = 4406
            }
        }
    }
    step {
        key = 4410
        name = "EditRejected"
        editable = true
        statusCode = "4410"
        action {
            key = 101905867
            name = "Save"
            label = "JFW_WF_ACTION.1333282552"
            doRefresh = false
            actionCode = "19"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditRejected-Save-EditRejected"
                nextStepKey = 4410
            }
        }
        action {
            key = 2088449029
            name = "Cancel"
            label = "JFW_WF_ACTION.1628799749"
            doRefresh = true
            actionCode = "20"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditRejected-Cancel-RepairRejected"
                nextStepKey = 4409
            }
        }
        action {
            key = 289378214
            name = "Submit"
            label = "JFW_WF_ACTION.1403909229"
            actionCode = "26"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"
            
            result {
                displayName = "EditRejected-Submit-ModificationApproval"
                nextStepKey = 4408
            }
        }
    }
    step {
        key = 4412
        name = "End"
        editable = false
        statusCode = "4412"
    }
}