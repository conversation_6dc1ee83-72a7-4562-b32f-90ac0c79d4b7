package com.progressoft.ach.dsl.views

import com.progressoft.jupiter.kotlin.dsl.Portlet
import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val ATSUnwindForecastR_View = View {
    name = "ATSUnwindForecastR.View"
    label = "JFW_VIEWS.1112905268"
    workflowName = null

    entity = "com.progressoft.ach.entities.ATSUnwindForecastR"
    joinProperty = "refQuery.id"
    idProperty = "id"
    newPortal {
        column {
            width = 12
            portlet {
                label = "JFW_PORTLETS.*********"
                form {
                    label = "JFW_PORTLETS.*********"
                    row {
                        comboBox {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "participant"
                            filling = Filling.READ_ONLY
                        }
                        lookup {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "account"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        currency {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "currency"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "currentBalance"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        amount {
                            label = "JFW_PROPERTIES.********"
                            propertyPath = "ttlUnwindDbTx"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "ttlUnwindCrTx"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        amount {
                            label = "JFW_PROPERTIES.85609188"
                            propertyPath = "ttlUnwindDbRtx"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.1509341423"
                            propertyPath = "ttlUnwindCrRtx"
                            filling = Filling.READ_ONLY
                        }
                    }
                    amount {
                        label = "JFW_PROPERTIES.764154271"
                        propertyPath = "forecastedBalance"
                        filling = Filling.READ_ONLY
                    }
                }
            }
        }
    }
    portal {
        column {
            width = 12
            portlet {
                label = "JFW_PORTLETS.**********"
                form {
                    label = "JFW_PORTLETS.**********"
                    row {
                        comboBox {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "participant"
                            filling = Filling.READ_ONLY
                        }
                        lookup {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "account"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        currency {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "currency"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "currentBalance"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        amount {
                            label = "JFW_PROPERTIES.********"
                            propertyPath = "ttlUnwindDbTx"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.**********"
                            propertyPath = "ttlUnwindCrTx"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        amount {
                            label = "JFW_PROPERTIES.85609188"
                            propertyPath = "ttlUnwindDbRtx"
                            filling = Filling.READ_ONLY
                        }
                        amount {
                            label = "JFW_PROPERTIES.1509341423"
                            propertyPath = "ttlUnwindCrRtx"
                            filling = Filling.READ_ONLY
                        }
                    }
                    amount {
                        label = "JFW_PROPERTIES.764154271"
                        propertyPath = "forecastedBalance"
                        filling = Filling.READ_ONLY
                    }
                }
            }
        }
    }
}
