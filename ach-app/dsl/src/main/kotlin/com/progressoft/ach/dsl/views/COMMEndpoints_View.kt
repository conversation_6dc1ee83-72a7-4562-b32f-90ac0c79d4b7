package com.progressoft.ach.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val COMMEndpoints_View = View {
	name = "COMMEndpoints.View"
	label="endpoints.view_600000826"
	workflowName = null
	
	entity = "com.progressoft.communication.entity.COMMEndpoint"
	idProperty = "id"
	ordering = "creationDate:DESC"
	portal {
		column {
			width = 12
			portlet {
				label = "info_600000966"
				form {
					label = "info_600000966"
					row {
						text {
							label = "name_600000846"
							propertyPath = "name"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 255
						}
						radioGroup {
							label = "direction_600000851"
							propertyPath = "direction"
							filling = Filling.REQUIRED
							option {
								label = "endpoints.inward_600000856"
								value = "In"
							}
							option {
								label = "endpoints.outward_600000860"
								value = "Out"
							}
						}
					}
					row {
						text {
							label = "system_600000864"
							propertyPath = "system"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
						text {
							label = "acceptedtenants_600000869"
							propertyPath = "acceptedTenants"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
					}
					row {
						text {
							label = "rejectedtenants_600000874"
							propertyPath = "rejectedTenants"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
						text {
							label = "acceptedmessagetypes_600000879"
							propertyPath = "acceptedMessageTypes"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
					}
					row {
						text {
							label = "rejectedmessagetypes_600000884"
							propertyPath = "rejectedMessageTypes"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
						text {
							label = "uri_600000889"
							propertyPath = "uri"
							filling = Filling.REQUIRED
							minLength = 0
							maxLength = 4000
						}
					}
					row {
						text {
							label = "deadletteruri_600000894"
							propertyPath = "deadLetterUri"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
						text {
							label = "JFW_PROPERTIES.1866638287"
							propertyPath = "unhandledResponseUri"
							filling = Filling.OPTIONAL
							minLength = 0
							maxLength = 4000
						}
					}
					text {
						label = "JFW_PROPERTIES.1491573091"
						propertyPath = "originalMessageType"
						filling = Filling.OPTIONAL
						minLength = 0
						maxLength = 4000
					}
				}
			}
			portlet {
				label = "activity_600000990"
				attachment {
					label = "endpoints.attachments.tab_600000995"
					attachmentFieldUrl = "/api/ach/list-attachments/COMMEndpoints.View/{itemId}"
				}
			}
		}
	}
}
