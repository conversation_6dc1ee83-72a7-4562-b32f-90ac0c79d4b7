package com.progressoft.ach.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val ATSUserAuditItemsReport_View = View {
    name = "ATSUserAuditItemsReport.View";
    workflowName = null;
    entity = "com.progressoft.ach.jfwcustom.JupiterChangeHistoryItemEntity";
    label = "security.audit.report.title";
    idProperty = "id";
    ordering = "fieldName:ASC";

    portal {
        column {
            width = 10;
            portlet {
                columnNumber = 1
                label = "security.audit.report.title";
                form {
                    label = "security.audit.report.title";
                    row {
                        text {
                            label = "audit.report.createdby"
                            propertyPath = "createdBy"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 200
                        }
                        dateTime {
                            label = "audit.report.creationdate"
                            propertyPath = "creationDate"
                            filling = Filling.READ_ONLY

                        }
                    }
                    row {
                        text {
                            label = "audit.report.item.fieldname"
                            propertyPath = "fieldName"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 255
                        }
                        text {
                            label = "audit.report.item.oldvalue"
                            propertyPath = "oldValue"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 200
                        }
                    }
                    row {
                        text {
                            label = "audit.report.item.newvalue"
                            propertyPath = "newValue"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 255
                        }
                    }

                }
            }
        }
    };
}