package com.progressoft.ach.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var NEW_ACH_LABELS = Labels {
    label("JFW_VIEWS.1659881758") {
        en("Report Records")
        ar("سجلات التقرير")
    }
    label("JFW_PORTLETS.1090221493") {
        en("Report Record")
        ar("سجل التقرير")
    }
    label("JFW_PROPERTIES.708568905") {
        en("Settlement Agent")
        ar("وكيل التسوية")
    }
    label("JFW_PROPERTIES.459012432") {
        en("Session")
        ar("حصة")
    }
    label("JFW_PROPERTIES.1125468775") {
        en("Settlement Date")
        ar("تاريخ التسوية")
    }
    label("JFW_PROPERTIES.112519999") {
        en("forDate")
        ar("للتاريخ")
    }
    label("JFW_PROPERTIES.11252000") {
        en("fromHour")
        ar("من الساعة")
    }
    label("JFW_PROPERTIES.112520021") {
        en("toHour")
        ar("إلى الساعة")
    }
    label("JFW_PROPERTIES.2048520451") {
        en("Currency")
        ar("عملة")
    }
    label("JFW_PROPERTIES.1113019228") {
        en("Participated Systems")
        ar("الأنظمة المشاركة")
    }
    label("JFW_PROPERTIES.790060810") {
        en("Communicator Agent")
        ar("وكيل التواصل")
    }
    label("JFW_PROPERTIES.921725756") {
        en("Deleted")
        ar("محذوف")
    }
    label("JFW_PROPERTIES.1816547127") {
        en("Deleted By")
        ar("حذف من")
    }
    label("JFW_PROPERTIES.195057994") {
        en("Locked By")
        ar("محجوز من")
    }
    label("JFW_PROPERTIES.20732663") {
        en("Updated by")
        ar("تم تحديثه بواسطة")
    }
    label("JFW_PROPERTIES.1378732723") {
        en("Created by")
        ar("تم إنشاؤها بواسطة")
    }
    label("JFW_PROPERTIES.334821762") {
        en("Deleted on")
        ar("تاريخ الحذف")
    }
    label("JFW_PROPERTIES.38450964") {
        en("Due on")
        ar("بسبب")
    }
    label("JFW_PROPERTIES.1520841895") {
        en("Updated on")
        ar("تم تحديثه")
    }
    label("JFW_PROPERTIES.1789729397") {
        en("Created on")
        ar("تم إنشاؤها على")
    }
    label("JFW_PROPERTIES.2049656972") {
        en("Status")
        ar("حالة")
    }
    label("JFW_PROPERTIES.1585097343") {
        en("StatusCode")
        ar("رمز الحالة")
    }
    label("JFW_PROPERTIES.1573731231") {
        en("StatusId")
        ar("حالة")
    }
    label("JFW_PROPERTIES.697994483") {
        en("Status")
        ar("حالة")
    }
    label("JFW_PROPERTIES.632946691") {
        en("ID")
        ar("بطاقة تعريف")
    }
    label("JFW_PROPERTIES.1787859113") {
        en("commAgent.id")
        ar("معرف الوكيل")
    }
    label("JFW_PROPERTIES.1940526710") {
        en("commAgent.code")
        ar("كود وكيل الاتصالات")
    }
    label("JFW_PROPERTIES.1380181182") {
        en("commAgent.name")
        ar("اسم وكيل الاتصال")
    }
    label("JFW_PROPERTIES.1230597199") {
        en("commAgent.codeNamePair")
        ar("كود واسم وكيل الاتصال")
    }
    label("JFW_PROPERTIES.468288818") {
        en("settAgent.id")
        ar("معرف وكيل التسوية")
    }
    label("JFW_PROPERTIES.85088462") {
        en("settAgent.code")
        ar("رمز وكيل التسوية")
    }
    label("JFW_PROPERTIES.1536661872") {
        en("settAgent.name")
        ar("اسم وكيل التسوية")
    }
    label("JFW_PROPERTIES.567774899") {
        en("settAgent.codeNamePair")
        ar("كود واسم وكيل التسوية")
    }
    label("JFW_PROPERTIES.1944786244") {
        en("session.id")
        ar("معرف الجلسة")
    }
    label("JFW_PROPERTIES.474095876") {
        en("session.code")
        ar("كود الجلسة")
    }
    label("JFW_PROPERTIES.115511420") {
        en("session.name")
        ar("اسم الجلسة")
    }
    label("JFW_PROPERTIES.1676250170") {
        en("session.codeNamePair")
        ar("كود واسم الجلسة")
    }
    label("JFW_PROPERTIES.253945961") {
        en("currency.id")
        ar("معرف العملة")
    }
    label("JFW_PROPERTIES.205290549") {
        en("currency.codeNamePair")
        ar("كود واسم العملة")
    }
    label("JFW_PROPERTIES.1470187815") {
        en("currency.stringISOCode")
        ar("سلسلة العملة رمز ISO")
    }
    label("JFW_PROPERTIES.1238650664") {
        en("Deleted")
        ar("محذوف")
    }
    label("JFW_PROPERTIES.1811999970") {
        en("Deleted By")
        ar("حذف من")
    }
    label("JFW_PROPERTIES.927238880") {
        en("Locked By")
        ar("محجوز من")
    }
    label("JFW_PROPERTIES.440764036") {
        en("Updated by")
        ar("عدَل من")
    }
    label("JFW_PROPERTIES.1814886824") {
        en("Created by")
        ar("انشئ من")
    }
    label("JFW_PROPERTIES.1178930721") {
        en("Deleted on")
        ar("تاريخ الحذف")
    }
    label("JFW_PROPERTIES.715959045") {
        en("Due on")
        ar("مستحق في")
    }
    label("JFW_PROPERTIES.1054836027") {
        en("Updated on")
        ar("تم التحديث في")
    }
    label("JFW_PROPERTIES.1137258970") {
        en("Created on")
        ar("تم الإنشاء في")
    }
    label("JFW_PROPERTIES.1522587072") {
        en("Status")
        ar("الحالة")
    }
    label("JFW_PROPERTIES.28733305") {
        en("StatusCode")
        ar("رمز الحالة")
    }
    label("JFW_PROPERTIES.825034877") {
        en("StatusId")
        ar("معرف الحالة")
    }
    label("JFW_PROPERTIES.1181444115") {
        en("Status")
        ar("الحالة")
    }
    label("JFW_VIEWS.625264323") {
        en("System Configurations")
        ar("تكوينات النظام")
    }
    label("JFW_VIEWS.725264883") {
        en("ACL Configurations")
        ar("تكوينات ACL")
    }
    label("JFW_PORTLETS.894221660") {
        en("Info")
        ar("معلومات")
    }
    label("JFW_PROPERTIES.1164482933") {
        en("Key")
        ar("مفتاح")
    }
    label("JFW_PROPERTIES.1595989319") {
        en("Value")
        ar("قيمة")
    }
    label("JFW_PORTLETS.1351525683") {
        en("Activity")
        ar("نشاط")
    }
    label("JFW_PORTLETS.857539717") {
        en("Workflow Status")
        ar("حالة سير العمل")
    }
    label("JFW_PROPERTIES.933734309") {
        en("ID")
        ar("معرف")
    }
    label("JFW_WF_ACTION.569380699") {
        en("Initialize")
        ar("تهيئة")
    }
    label("JFW_WF_ACTION.911736379") {
        en("Create")
        ar("إنشاء")
    }
    label("JFW_WF_ACTION.1262620834") {
        en("Submit")
        ar("تقديم")
    }
    label("JFW_WF_ACTION.1122244157") {
        en("Approve")
        ar("موافقة")
    }
    label("JFW_WF_ACTION.1449956575") {
        en("Reject")
        ar("يرفض")
    }
    label("JFW_WF_ACTION.2132993159") {
        en("Edit")
        ar("تعديل")
    }
    label("JFW_WF_ACTION.38381903") {
        en("Cancel")
        ar("إلغاء")
    }
    label("JFW_WF_ACTION.297488875") {
        en("Save")
        ar("حفظ")
    }
    label("JFW_WF_ACTION.351767477") {
        en("Submit")
        ar("تقديم")
    }
    label("JFW_WF_ACTION.1665131023") {
        en("Edit")
        ar("تعديل")
    }
    label("JFW_WF_ACTION.1687502532") {
        en("Save")
        ar("حفظ")
    }
    label("JFW_WF_ACTION.1019752038") {
        en("Cancel")
        ar("إلغاء")
    }
    label("JFW_WF_ACTION.1208750466") {
        en("Submit")
        ar("تقديم")
    }
    label("JFW_WF_ACTION.765342956") {
        en("Approve")
        ar("موافقة")
    }
    label("JFW_WF_ACTION.1851182983") {
        en("Reject")
        ar("يرفض")
    }
    label("JFW_WF_ACTION.13589064") {
        en("Edit")
        ar("تعديل")
    }
    label("JFW_WF_ACTION.1642748129") {
        en("Reset")
        ar("إعادة تعيين")
    }
    label("JFW_WF_ACTION.**********") {
        en("Save")
        ar("حفظ")
    }
    label("JFW_WF_ACTION.**********") {
        en("Cancel")
        ar("إلغاء")
    }
    label("JFW_WF_ACTION.**********") {
        en("Submit")
        ar("تقديم")
    }
    label("JFW_MENU.*********") {
        en("System Configurations")
        ar("تكوينات النظام")
    }
    label("JFW_MENU.*********") {
        en("Internal Accounts")
        ar("الحسابات الداخلية")
    }
    label("JFW_VIEWS.*********") {
        en("Internal Accounts")
        ar("الحسابات الداخلية")
    }
    label("JFW_MENU.*********") {
        en("Report Records")
        ar("سجلات التقرير")
    }
    label("JFW_PORTLETS.*********") {
        en("Activity")
        ar("نشاط")
    }
    label("JFW_TABS.**********") {
        en("Attachments")
        ar("مرفقات")
    }
    label("JFW_PROPERTIES.*********") {
        en("Accounts")
        ar("الحسابات")
    }
    label("JFW_PROPERTIES.*********") {
        en("Max Amount")
        ar("المبلغ الأقصى")
    }
    label("JFW_PROPERTIES.**********") {
        en("Users")
        ar("المستخدمين")
    }
    label("JFW_PROPERTIES.**********") {
        en("users.id")
        ar("معرف المستخدم")
    }
    label("JFW_PROPERTIES.**********") {
        en("users.email")
        ar("بريد المستخدم الإلكتروني")
    }
    label("JFW_PROPERTIES.**********") {
        en("View Name")
        ar("اسم المنظر")
    }
    label("acl-config.existing.user.error") {
        en("One or more of the selected users and view name already have an ACL configuration.")
        ar("واحد أو أكثر من المستخدمين المحددين واسم العرض لديه بالفعل تكوين ACL.")
    }
    label("subscription-fee.existing.participant.error") {
        en("One or more of the selected participants already have a Subscription Fee.")
        ar("أحد أو أكثر من المشاركين المحددين لديهم رسوم اشتراك بالفعل.")
    }
    label("kassip.time.table.validation.error") {
        en("The current time is not within the time range of KASSIP session time table")
        ar("الوقت الحالي ليس ضمن النطاق الزمني لجدول وقت جلسة KASSIP")
    }
    label("kassip.time.table.validation.error.2") {
        en("The new settlement time is not within the exchange time range of KASSIP session time table")
        ar("الوقت الحالي ليس ضمن النطاق الزمني لجدول وقت جلسة KASSIP")
    }
    label("session.must.have.settlement.period") {
        en("Session must have settlement period")
        ar("يجب أن يكون للجلسة فترة تسوية")
    }
    label("acl-config.unprovided.amount.and.account.error") {
        en("Max Amount or Accounts is required.")
        ar("مبلغ الحد الأقصى أو الحسابات مطلوبة.")
    }
    label("JFW_PROPERTIES.***********") {
        en("Payment Matrix Report")
        ar("تقرير مصفوفة الدفع")
    }
    label("JFW_PROPERTIES.**********") {
        en("Session Date")
        ar("تاريخ الجلسة")
    }
    label("JFW_PROPERTIES.***********") {
        en("Session Sequence")
        ar("تسلسل الجلسة")
    }
    label("JFW_PROPERTIES.***********") {
        en("Working Date")
        ar("تاريخ العمل")
    }
    label("JFW_PROPERTIES.****************") {
        en("Action Mode")
        ar("وضع العمل")
    }
    label("JFW_PROPERTIES.***********") {
        en("Query Type")
        ar("نوع الاستعلام")
    }
    label("JFW_PROPERTIES.***********") {
        en("Participants")
        ar("المشاركون")
    }
    label("JFW_PROPERTIES.**********") {
        en("Report Format")
        ar("تنسيق التقرير")
    }
    label("payment.matrix.filters_520001308") {
        en("Payment Matrix Report Filter")
        ar("عامل تصفية تقرير مصفوفة الدفع")
    }
    label("JFW_PROPERTIES.************") {
        en("Generated Reports")
        ar("التقارير التي تم إنشاؤها")
    }
    label("user.report") {
        en("User Reports")
        ar("تقارير المستخدم")
    }
    label("generate.action") {
        en("Generate")
        ar("إنشاء")
    }
    label("accept.action") {
        en("Accept")
        ar("قبول")
    }
    label("report.generated") {
        en("Generated")
        ar("تم إنشاؤها")
    }
    label("ready.for.generation") {
        en("Ready For Generation")
        ar("جاهز للإنشاء")
    }
    label("ready.for.acceptance") {
        en("Ready For Acceptance")
        ar("جاهز للقبول")
    }
    label("accepted") {
        en("Accepted")
        ar("مقبول")
    }
    label("JFW_PROPERTIES.************") {
        en("Batches Report")
        ar("تقرير الدُفعات")
    }
    label("JFW_PROPERTIES.************") {
        en("User Accounts Self-Locking")
        ar("حسابات المستخدم قفل الذات")
    }
    label("JFW_PROPERTIES.**********") {
        en("Session Date")
        ar("تاريخ الجلسة")
    }
    label("JFW_PROPERTIES.***********") {
        en("Session Sequence Number")
        ar("رقم تسلسل الجلسة")
    }
    label("JFW_PROPERTIES.**********") {
        en("Report Format")
        ar("تنسيق التقرير")
    }
    label("batches.report.filters_15600123") {
        en("Batches Report Filter")
        ar("تصفية تقرير الدفعات")
    }
    label("outgoing.transaction.report.filters_156001") {
        en("Outgoing Transaction Report Filter")
        ar("تصفية تقرير الدفعات")
    }
    label("cancelled.payment.report.filters_15623") {
        en("Cancelled Payment Matrix Report Filter")
        ar("تصفية تقرير الدفعات")
    }
    label("JFW_PROPERTIES.************") {
        en("Generated Reports")
        ar("التقارير")
    }
    label("user.report") {
        en("User Reports")
        ar("تقارير المستخدم")
    }
    label("generate.action") {
        en("Generate")
        ar("إنشاء")
    }
    label("report.generated") {
        en("Generated")
        ar("تم إنشاؤها")
    }
    label("ready.for.generation") {
        en("Ready For Generation")
        ar("جاهز للجيل")
    }
    label("JFW_PROPERTIES.************") {
        en("Batch Id Number")
        ar("رقم معرف الدفعة")
    }
    label("JFW_PROPERTIES.***********") {
        en("Show Transactions")
        ar("إظهار المعاملات")
    }
    label("bank.balances.report.failed.generation") {
        en( "Failed To Generate Bank Balances Report")
        ar("فشل في إنشاء تقرير أرصدة البنك")
    }
    label("bank.volume.summary.report.failed.generation") {
        en( "Failed To Generate Bank Volume Summary Report")
        ar("فشل في إنشاء تقرير أرصدة البنك")
    }
    label("cancelled.payments.matrix.report.failed.generation") {
        en( "Failed To Generate Canceled Payments Matrix Report")
        ar("فشل في إنشاء تقرير مصفوفة الحوالات الملغاة")
    }
    label("incoming.transaction.report.failed.generation") {
        en( "Failed To Generate Incoming Transaction Report")
        ar("فشل في إنشاء تقرير المعاملات الواردة")
    }
    label("bancs.reconciliation.report.failed.generation") {
        en( "Failed To Generate Bancs Reconcillation Report")
        ar("فشل في إنشاء تقرير أرصدة البنك")
    }
    label("outgoing.transaction.report.failed.generation") {
        en( "Failed To Generate Outgoing Transaction Report")
        ar("فشل في إنشاء تقرير أرصدة البنك")
    }
    label("reasons.over.banks.report.failed.generation") {
        en("Failed To Generate Reasons Over Banks Report")
        ar("فشل في إنشاء تقرير الأسباب بشأن البنوك")
    }
    label("JFW_MENU.************") {
        en("Recon BaNCS Report")
        ar("تقرير المخالصة")
    }
    label("JFW_MENU.************") {
        en("Cancelled Payment Matrix Report")
        ar("تقرير المخالصة")
    }
    label("JFW_MENU.************") {
        en("Outgoing Transaction Report")
        ar("تقرير المخالصة")
    }
    label("ats.user.allotments.view") {
        en("Users Allotments")
        ar("تخصيصات المستخدمين")
    }
    label("ats.recon.scheduled.times.view") {
        en("Reconciliation scheduled times")
        ar("أوقات التسوية المجدولة")
    }
    label("ats.user.allotments.assigned.user") {
        en("Assigned User")
        ar("المستخدم المعين")
    }
    label("ats.reconcilation.scheduled.times") {
        en("Reconciliation scheduled times")
        ar("أوقات التسوية المجدولة")
    }

    label("ats.user.allotments.views") {
        en("Views")
        ar("عرض الحصص")
    }

    label("ats.user.allotments.fromtime") {
        en("Allotment Start Time")
        ar("وقت بدء التخصيص")
    }

    label("ats.user.allotments.totime") {
        en("Allotment End Time")
        ar("وقت انتهاء التخصيص")
    }

    label("ats.user.allotments.status") {
        en("Status")
        ar("الحالة")
    }

    label("ats.user.allotments.creationdate") {
        en("Creation Date")
        ar("تاريخ الإنشاء")
    }

    label("ats.user.allotments.updatingdate") {
        en("Updating Date")
        ar("تاريخ التحديث")
    }

    label("ats.user.allotments.createdby") {
        en("Created By")
        ar("تم الإنشاء بواسطة")
    }

    label("ats.user.allotments.updatedby") {
        en("Updated By")
        ar("تم التحديث بواسطة")
    }

    label("ats.user.allotments.workflowstatus") {
        en("Workflow Status")
        ar("حالة سير العمل")
    }

    label("pending.activation") {
        en("Pending Activation")
        ar("في انتظار التفعيل")
    }

    label("reject") {
        en("Reject")
        ar("رفض")
    }

    label("ats.user.allotments.user") {
        en("User")
        ar("مستخدم")
    }

    label("ats.recon.scheduled.session") {
        en("Scheduled Session")
        ar("الجلسة المجدولة")
    }

    label("ats.user.allotments.starttime") {
        en("Start Time")
        ar("وقت البدء")
    }

    label("ats.scheduledtimes.scheduledTime") {
        en("Scheduled Time")
        ar("الوقت المجدول")
    }

    label("ats.user.allotments.endtime") {
        en("End Time")
        ar("وقت الانتهاء")
    }

    label("ats.scheduledtimes") {
        en("ScheduledTimes")
        ar("الأوقات المجدولة")
    }

    label("ats.user.allotments") {
        en("Allotments")
        ar("الحصص")
    }

    label("request.deactivation") {
        en("Request Deactivation")
        ar("طلب إلغاء التفعيل")
    }

    label("ats.user.allotments.sub.view") {
        en("Allotment Views")
        ar("عرض الحصص")
    }

    label("ats.scheduled.times.sub.view") {
        en("ScheduledTimes Views")
        ar("عرض الأوقات المجدولة")
    }

    label("active") {
        en("Active")
        ar("نشط")
    }

    label("end.time.before.start.time") {
        en("End Time is defined before start time")
        ar("تم تعريف وقت الانتهاء قبل وقت البدء")
    }

    label("at.least.one.scheduled.time.required") {
        en("At least one scheduled time is required. Please ensure that a valid scheduled time is defined.")
        ar("يجب أن يكون هناك وقت مجدول واحد على الأقل. يرجى التأكد من تحديد وقت مجدول صالح.")
    }

    label("saved") {
        en("Saved")
        ar("تم الحفظ")
    }

    label("pending.deactivation") {
        en("Pending Deactivation")
        ar("في انتظار إلغاء التفعيل")
    }
    label("mis.performance.report.filters_156001") {
        en("Mis Performance Report Filter")
        ar("تصفية تقرير أداء نظام المعلومات الإدارية")
    }
    label("JFW_MENU_ReasonsOverBanksReports") {
        en("Reasons Over Banks Report")
        ar("تقرير أسباب تتعلق بالبنوك")
    }
    label("JFW_MENU.***********") {
        en("MIS Performance Report")
        ar("تقرير سوء الأداء")
    }

    label("JFW_VIEWS.**********") {
        en( "Communicated Messages Audit Trail")
        ar("مسار تدقيق الرسائل المُرسلة")
    }
    label("JFW_VIEWS.************") {
        en( "Message Reference")
        ar("مرجع الرسالة")
    }
    label("JFW_VIEWS.***********") {
        en( "Error Code")
        ar("رمز الخطأ")
    }
    label("JFW_VIEWS.************") {
        en( "Error Message")
        ar("رسالة خطأ")
    }
    label("JFW_VIEWS.************") {
        en( "Is Valid Signature")
        ar("التوقيع صالح")
    }
    label("JFW_VIEWS.*************") {
        en( "Message Status")
        ar("حالة الرسالة")
    }
    label("incoming.transaction.report.filters_156001") {
        en("Incoming Transaction Report Filter")
        ar("تصفية تقرير الدفعات")
    }
    label("JFW_MENU.92900000000") {
        en("Incoming Transaction Report")
        ar("تقرير المخالصة")
    }
    label("ats.user.allotments.view") {
        en("Users Allotments")
        ar("حصص المستخدمين")
    }
    label("ats.user.allotments.assigned.user") {
        en("Assigned User")
        ar("المستخدم المعين")
    }

    label("ats.user.allotments.views") {
        en("Views")
        ar("عرض الحصص")
    }

    label("ats.user.allotments.fromtime") {
        en("Allotment Start Time")
        ar("وقت بدء التخصيص")
    }
    label("ats.user.allotments.totime") {
        en("Allotment End Time")
        ar("وقت انتهاء التخصيص")
    }
    label("ats.user.allotments.status") {
        en("Status")
        ar("الحالة")
    }
    label("ats.user.allotments.creationdate") {
        en("Creation Date")
        ar("تاريخ الإنشاء")
    }
    label("ats.user.allotments.updatingdate") {
        en("Updating Date")
        ar("تاريخ التحديث")
    }
    label("ats.user.allotments.createdby") {
        en("Created By")
        ar("تم إنشاؤها بواسطة")
    }
    label("ats.user.allotments.updatedby") {
        en("Updated By")
        ar("تم تحديثه بواسطة")
    }
    label("ats.user.allotments.workflowstatus") {
        en("Workflow Status")
        ar("حالة سير العمل")
    }
    label("pending.activation") {
        en("Pending Activation")
        ar("في انتظار التفعيل")
    }
    label("reject") {
        en("Reject")
        ar("يرفض")
    }
    label("ats.user.allotments.user") {
        en("Allotments User")
        ar("مستخدم المخصصات")
    }
    label("ats.user.allotments.starttime") {
        en("Allotment Start Time")
        ar("وقت بدء التخصيص")
    }
    label("ats.user.allotments.endtime") {
        en("Allotment End Time")
        ar("وقت انتهاء التخصيص")
    }
    label("ats.user.allotments") {
        en("Allotments")
        ar("الحصص")
    }
    label("request.deactivation") {
        en("Request Deactivation")
        ar("طلب إلغاء التفعيل")
    }
    label("ats.user.allotments.sub.view") {
        en("Allotment Views")
        ar("عرض الحصص")
    }

    label("active") {
        en("Active")
        ar("نشط")
    }
    label("end.time.before.start.time") {
        en("End Time is defined before start time")
        ar("تم تعريف وقت الانتهاء قبل وقت البدء")
    }
    label("saved") {
        en("Saved")
        ar("تم الحفظ")
    }
    label("pending.deactivation") {
        en("Pending Deactivation")
        ar("في انتظار إلغاء التفعيل")
    }

    label("audit.reports.menu") {
        en("Audit Reports")
        ar("تقارير التدقيق")
    }
    label("audit.report.title") {
        en("Audit Reports")
        ar("تقارير التدقيق")
    }
    label("security.audit.report.title") {
        en("Security Audit Reports")
        ar("تقارير تدقيق الأمن")
    }
    label("audit.report.createdby") {
        en("User")
        ar("مستخدم")
    }
    label("audit.report.creationdate") {
        en("Execution Date/Time")
        ar("تاريخ التنفيذ/الوقت")
    }
    label("audit.report.includesrvoperation") {
        en("Include Service Operations")
        ar("تشمل عمليات الخدمة")
    }
    label("audit.report.operationtype") {
        en("Operation Type")
        ar("نوع العملية")
    }
    label("audit.report.entityId") {
        en("Record Id")
        ar("معرف السجل")
    }
    label("audit.report.collectionname") {
        en("View Name")
        ar("اسم العرض")
    }
    label("audit.report.actionname") {
        en("Executed Action")
        ar("العمل المنفذ")
    }
    label("audit.report.workstation") {
        en("Workstation")
        ar("محطة العمل")
    }
    label("audit.report.item.fieldname") {
        en("Field Name")
        ar("اسم الحقل")
    }
    label("audit.report.item.oldvalue") {
        en("Old Value")
        ar("القيمة القديمة")
    }
    label("audit.report.item.newvalue") {
        en("New Value")
        ar("قيمة جديدة")
    }
    label("audit.report.items.title") {
        en("Change Records")
        ar("تغيير السجلات")
    }
    label("JFW_MENU_ReasonsOverBanksReports") {
        en("Reasons Over Banks Report")
        ar("تقرير عن الأسباب المتعلقة بالبنوك")
    }
    label("JFW_MENU.************") {
        en("Bank Volume Summary Report")
        ar("تقرير ملخص حجم البنك")
    }
    label("bank.volume.summary.report.filters_156001") {
        en("Bank Volume Summary Report Filter")
        ar("مرشح تقرير ملخص حجم البنك")
    }
    label("JFW_PROPERTIES.***********") {
        en("Session Date From")
        ar("تاريخ الجلسة من")
    }
    label("JFW_PROPERTIES.**********") {
        en("Session Date To")
        ar("تاريخ الجلسة إلى")
    }
    label("JFW_PROPERTIES.**********") {
        en("Transaction Amount From")
        ar("مبلغ المعاملة من")
    }
    label("JFW_PROPERTIES.**********") {
        en("Transaction Amount To")
        ar("مبلغ المعاملة إلى")
    }
    label("JFW_PROPERTIES.**********") {
        en("Direction")
        ar("اتجاه")
    }
    label("JFW_PROPERTIES.**********") {
        en("Category Purpose")
        ar("اتجاه")
    }
    label("JFW_MENU.*************") {
        en("Bank Volume Summary Report")
        ar("تقرير المخالصة")
    }
    label("kassip.timetable.request.info") {
        en("Timetable Request Info")
        ar("معلومات طلب جدول الاوقات")
    }
    label("kassip.timetable.request.view") {
        en("Timetable Request")
        ar("طلب جدول الاوقات")
    }
    label("request.id") {
        en("Request ID")
        ar("رقم الطلب")
    }
    label("unapproved-subscription-fee.config.error") {
        en("There is no approved subscription fee configuration to be applied in the charge record recalculation.")
        ar("لا يوجد أي تكوين معتمد لرسوم الاشتراك ليتم تطبيقه في إعادة حساب سجل الرسوم.")
    }
    label("unapproved-billing-profile.config.error") {
        en("There is no approved billing profile configuration to be applied in the charge record recalculation.")
        ar("لا يوجد أي تكوين معتمد لملف تعريف الفوترة ليتم تطبيقه في إعادة حساب سجل الرسوم.")
    }
    label("unapproved-reason-charge-def.error") {
        en("There is no approved reason charge definition to be applied in the charge record recalculation.")
        ar("لا يوجد تعريف معتمد لسبب الرسوم ليتم تطبيقه في إعادة حساب سجل الرسوم.")
    }
    label("subscription-fee.invalid.discount.error") {
        en("The provided discount should be less than the subscription fee amount.")
        ar("يجب أن يكون الخصم المقدم أقل من مبلغ رسوم الاشتراك.")
    }
    label("reason-charge-def-range-limit.error") {
        en("Range upper limit should exceed lower limit.")
        ar("يجب أن يتجاوز الحد الأعلى للنطاق الحد الأدنى.")
    }
    label("invalid-iban.error") {
        en("Provide a valid IBAN.")
        ar("توفير رقم IBAN صالح")
    }
}