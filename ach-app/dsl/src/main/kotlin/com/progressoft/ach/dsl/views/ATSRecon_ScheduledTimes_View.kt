package com.progressoft.ach.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val ATSRecon_ScheduledTimes_View = View {
    hasMasterDraft;
    hasChangeHistory;
    name = "ATSRecon_ScheduledTimes.View";
    label = "ats.recon.scheduled.times.view";
    workflowName = "WF_Recon_ScheduledTimes_Workflow";
    entity = "com.progressoft.ach.entities.ATSRecon_ScheduledTimes";
    idProperty = "id";
    ordering = "creationDate:DESC";

    newPortal {
        column {
            width = 8
            portlet {
                label = "ats.reconcilation.scheduled.times"
                form {
                    label = "ats.reconcilation.scheduled.times"
                    comboBox {
                        label = "ats.recon.scheduled.session"
                        propertyPath = "scheduledSession"
                        filling = Filling.REQUIRED
                    }
                }
            }

        }

    }
    portal {
        column {
            width = 8
            portlet {
                label = "ats.reconcilation.scheduled.times"
                form {
                    label = "ats.reconcilation.scheduled.times"
                    comboBox {
                        label = "ats.recon.scheduled.session"
                        propertyPath = "scheduledSession"
                        filling = Filling.REQUIRED
                    }
                }
            }
            portlet {
                label = "ats.scheduledtimes"
                subView {
                    viewName = "ATSRecon_ScheduledTimesSub.View"
                    joinProperty = "reconScheduledTimesId.id"
                    idProperty = "id"
                    label = "ats.recon.scheduled.times.view"
                }
            }

        }
        column {
            width = 4
            portlet {
                label = "JFW_PORTLETS.688397429"
                form {
                    label = "JFW_PORTLETS.688397429"
                    comboBox {
                        label = "JFW_PROPERTIES.200734696"
                        propertyPath = "statusId"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "JFW_PROPERTIES.1659161205"
                        propertyPath = "creationDate"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "JFW_PROPERTIES.503391943"
                        propertyPath = "updatingDate"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "JFW_PROPERTIES.1699804144"
                        propertyPath = "lockedUntil"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "JFW_PROPERTIES.54680994"
                        propertyPath = "deletedOn"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "JFW_PROPERTIES.161279139"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    text {
                        label = "JFW_PROPERTIES.351430264"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    text {
                        label = "JFW_PROPERTIES.2093022802"
                        propertyPath = "lockedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    text {
                        label = "JFW_PROPERTIES.1280385671"
                        propertyPath = "deletedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    checkbox {
                        label = "JFW_PROPERTIES.230745728"
                        propertyPath = "deletedFlag"
                        filling = Filling.READ_ONLY
                    }
                }
            }
        }
    }
}