package com.progressoft.ach.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val ATSLKP_RecurringUnit_Entity = Entity {
	name = "com.progressoft.ach.entities.ATSLKP_RecurringUnit"
	property { 
		name="statusId"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1325743493"
	 }
	property { 
		name="statusId.id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.2014020409"
	 }
	property { 
		name="statusId.description"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1853469480"
	 }
	property { 
		name="statusId.codeNamePair"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1897675768"
	 }
	property { 
		name="code"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1426040053"
	 }
	property { 
		name="name"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1874182457"
	 }
	property { 
		name="description"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.971917001"
	 }
	property { 
		name="creationDate"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.865608489"
	 }
	property { 
		name="updatingDate"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1415550693"
	 }
	property { 
		name="lockedUntil"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.642223027"
	 }
	property { 
		name="deletedOn"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.2088967096"
	 }
	property { 
		name="createdBy"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.2030177077"
	 }
	property { 
		name="updatedBy"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.552425884"
	 }
	property { 
		name="lockedBy"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.192428519"
	 }
	property { 
		name="deletedBy"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1661458334"
	 }
	property { 
		name="deletedFlag"
		showAsResultColumn=1
		label = "JFW_PROPERTIES.1619862000"
	 }
	property { 
		name="id"
		showAsResultColumn=0
		label = "JFW_PROPERTIES.1459607621"
	 }
	property { 
		name="orgId"
		showAsResultColumn=0
		label = "orgId"
	 }
	property { 
		name="draftStatus"
		showAsResultColumn=0
		label = "draftStatus"
	 }
	property { 
		name="jfwDraft.id"
		showAsResultColumn=0
		label = "jfwDraft.id"
	 }
	property { 
		name="jfwDraft.draftData"
		showAsResultColumn=0
		label = "jfwDraft.draftData"
	 }
}