package com.progressoft.ach.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val CHRGNCPCharge_Entity = Entity {
	name = "com.progressoft.ach.charges.entities.CHRGNCPCharge"
	property { 
		name="statusId"
		showAsResultColumn=1
		label = "statusid_520000972"
	 }
	property { 
		name="statusId.id"
		showAsResultColumn=0
		label = "statusid.id_520000977"
	 }
	property { 
		name="deletedFlag"
		showAsResultColumn=0
		label = "deletedflag_520001032"
	 }
	property { 
		name="deletedBy"
		showAsResultColumn=0
		label = "deletedby_520001027"
	 }
	property { 
		name="lockedBy"
		showAsResultColumn=0
		label = "lockedby_520001022"
	 }
	property { 
		name="updatedBy"
		showAsResultColumn=0
		label = "updatedby_520001017"
	 }
	property { 
		name="deletedOn"
		showAsResultColumn=0
		label = "deletedon_520001007"
	 }
	property { 
		name="lockedUntil"
		showAsResultColumn=0
		label = "lockeduntil_520001002"
	 }
	property { 
		name="updatingDate"
		showAsResultColumn=0
		label = "updatingdate_520000997"
	 }
	property { 
		name="statusId.description"
		showAsResultColumn=0
		label = "statusid.description_520000987"
	 }
	property { 
		name="statusId.codeNamePair"
		showAsResultColumn=0
		label = "statusid.codenamepair_520000982"
	 }
	property { 
		name="year"
		showAsResultColumn=1
		label = "year_520000962"
	 }
	property { 
		name="month"
		showAsResultColumn=1
		label = "month_520000967"
	 }
	property { 
		name="creationDate"
		showAsResultColumn=1
		label = "creationdate_520000992"
	 }
	property { 
		name="createdBy"
		showAsResultColumn=1
		label = "createdby_520001012"
	 }
	property { 
		name="id"
		showAsResultColumn=0
		label = "id_520000957"
	 }
	property { 
		name="orgId"
		showAsResultColumn=0
		label = "orgId"
	 }
	property { 
		name="draftStatus"
		showAsResultColumn=0
		label = "draftStatus"
	 }
	property { 
		name="jfwDraft.id"
		showAsResultColumn=0
		label = "jfwDraft.id"
	 }
	property { 
		name="jfwDraft.draftData"
		showAsResultColumn=0
		label = "jfwDraft.draftData"
	 }
}