{
	"statistics" : [
		{
			"key" : "TTL_AUTO_DEBIT",
			"section" : "Summary",
			"label" : "Debit Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} OR INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_AUTO_CREDIT",
			"section" : "Summary",
			"label" : "Credit Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} OR INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_AUTO_CANCELLATION",
			"section" : "Summary",
			"label" : "Cancellation Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} AND INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_AUTO_REJECTION",
			"section" : "Summary",
			"label" : "Total Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} OR INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID IN (SELECT ID FROM ATSMSG_TYPES WHERE CODE IN('DB','CR'))
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_AUTO_RETURN",
			"section" : "Summary",
			"label" : "Return Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} AND INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_AUTO_REVERSAL",
			"section" : "Summary",
			"label" : "Reversal Auto Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant} AND INSTDPARTIID={communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE CODE IN('648','652'))
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_MAN_CANCEL",
			"section" : "Summary",
			"label" : "Cancellation Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_MAN_REJECTION",
			"section" : "Summary",
			"label" : "Total Sent Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID IN (SELECT ID FROM ATSMSG_TYPES WHERE CODE IN('DB','CR'))
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_MAN_RETURN",
			"section" : "Summary",
			"label" : "Return Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_MAN_REVERSAL",
			"section" : "Summary",
			"label" : "Reversal Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT",
			"section" : "Summary",
			"label" : "Total Sent Debits",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE CRDTRPARTIID={settlementParticipant}
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND STATEID !=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT",
			"section" : "Summary",
			"label" : "Total Sent Credits",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE DBTRPARTIID={settlementParticipant}
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND STATEID !=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_REJ",
			"section" : "Summary",
			"label" : "Sent Debits Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_REJ",
			"section" : "Summary",
			"label" : "Sent Credits Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT",
			"section" : "Summary",
			"label" : "Total Received Debits",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE DBTRPARTIID={settlementParticipant}
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT",
			"section" : "Summary",
			"label" : "Total Received Credits",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE CRDTRPARTIID={settlementParticipant}
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_REJ",
			"section" : "Summary",
			"label" : "Received Debit Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_REJ",
			"section" : "Summary",
			"label" : "Received Credit Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_CANCL",
			"section" : "Summary",
			"label" : "Total Sent Debit Cancellations",
			"statement": "
			SELECT COUNT(1) AS COUNT,NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_CANCL",
			"section" : "Summary",
			"label" : "Total Sent Credit Cancellations",
			"statement": "
			SELECT COUNT(1) AS COUNT,NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_CANCL",
			"section" : "Summary",
			"label" : "Total Received Debit Cancellation",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_CANCL",
			"section" : "Summary",
			"label" : "Total Received Credit Cancellation",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CN')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_REV",
			"section" : "Summary",
			"label" : "Total Sent Debit Reversals",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_REV",
			"section" : "Summary",
			"label" : "Total Received Debit Reversals",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_REV_REJ",
			"section" : "Summary",
			"label" : "Sent Debit Reversals Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_REV_REJ",
			"section" : "Summary",
			"label" : "Received Debit Reversal Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_RET",
			"section" : "Summary",
			"label" : "Total Sent Debit Returns",
			"statement": "
			SELECT COUNT(1) AS COUNT,NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_RET",
			"section" : "Summary",
			"label" : "Total Received Debit Returns",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_DEBIT_RET_REJ",
			"section" : "Summary",
			"label" : "Sent Debit Returns Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_DEBIT_RET_REJ",
			"section" : "Summary",
			"label" : "Received Debit Return Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'DB')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_REV",
			"section" : "Summary",
			"label" : "Total Sent Credit Reversals",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_REV",
			"section" : "Summary",
			"label" : "Total Received Credit Reversals",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_REV_REJ",
			"section" : "Summary",
			"label" : "Sent Credit Reversals Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_REV_REJ",
			"section" : "Summary",
			"label" : "Received Credit Reversal Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RV')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_RET",
			"section" : "Summary",
			"label" : "Total Sent Credit Returns",
			"statement": "
			SELECT COUNT(1) AS COUNT,NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_RET",
			"section" : "Summary",
			"label" : "Total Received Credit Returns",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID={communicatingParticipant}
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND (REASONID IS NULL OR REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE INSTRUCTEDAWARE = 1))
		AND SESSIONID={sessionId}
		"
		},
		{
			"key" : "TTL_SENT_CREDIT_RET_REJ",
			"section" : "Summary",
			"label" : "Sent Credit Returns Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTGPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		},
		{
			"key" : "TTL_RCVD_CREDIT_RET_REJ",
			"section" : "Summary",
			"label" : "Received Credit Return Rejected",
			"statement": "
			SELECT COUNT(1) AS COUNT, NVL(SUM(AMOUNT), 0) AS AMOUNT FROM ATSRTRANSACTIONS
			WHERE INSTDPARTIID = {communicatingParticipant}
		AND STATEID=(SELECT ID FROM ATSSTATES WHERE CODE = 'Rejected')
		AND ((CRDTRPARTIID IS NULL OR DBTRPARTIID IS NULL) OR (CRDTRPARTIID <> DBTRPARTIID))
		AND MSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'RT')
		AND ORGMSGTYPEID=(SELECT ID FROM ATSMSG_TYPES WHERE CODE = 'CR')
		AND REASONID IN (SELECT ID FROM ATSMSG_REASONS WHERE ISSYSTEM = 0)
		AND SESSIONID = {sessionId}
		"
		}
	]
}
