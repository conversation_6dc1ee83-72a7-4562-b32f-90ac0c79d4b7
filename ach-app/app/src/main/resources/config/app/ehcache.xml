<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd" updateCheck="false" name="appCachingConfig">
	<diskStore path="java.io.tmpdir" />
	<defaultCache eternal="true" maxElementsInMemory="10000"   memoryStoreEvictionPolicy="LRU" />
	<cache name="ATS_ActivitiMessageProcessContext" maxEntriesLocalHeap="100000000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="86400" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<!-- Cached JFWLookable ATS Entities-->
	<cache name="org.hibernate.cache.internal.StandardQueryCache" eternal="true" maxElementsInMemory="50000"  memoryStoreEvictionPolicy="LRU" statistics="true" />

	<cache name="com.progressoft.ach.entities.ATSLKP_BatchSource" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_Direction" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_MandateReason" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_PendingReason" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_PrivateIdentification" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_RecurringPresent" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_RecurringUnit" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_CtgPurp" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_Reason" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_TransPurp" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_Type" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Branch" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Institution" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_LimitsProfile" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Participant" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Type" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />

	<cache name="com.progressoft.ach.entities.ATSBDS_PeriodType" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBDS_Template" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_Mnd_RequestsType" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_TypesLink" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSRequestReason" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSRequestRReason" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSRequestType" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSBillingProfile" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSBillingScheme" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSChargeType" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSDistributionProfile" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.payments.entities.ATSState" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />

	<!-- Cached Non-JFWLookable ATS Entities-->
	<cache name="com.progressoft.ach.entities.ATSPRT_LimitsAmount" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMSG_PurpProf" maxEntriesLocalHeap="1000" eternal="true" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Message" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSACC_Account" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMndt_Policy_AutoReply" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBDS_BusinessDay" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_Creditor" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSPRT_Purp" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSMndt_Policy_General" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_AmountDiscount" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_PurpAmntChrg" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_ChrgDistProfile" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_R_TimeDef" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_PurpDef" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_ElapsedTimeDef" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_ElapsedTimeChrg" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_RolloverChrg" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_BatchSource" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_Scheme" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_R_ReasonDefChrg" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_R_TimeChrg" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_R_ReasonDef" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_CountDiscount" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_BillingProfile" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSBIL_RsnAutoReply" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSChargeTier" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.entities.ATSLKP_FwdDatedSetting" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />
	<cache name="com.progressoft.ach.billing.entities.ATSPurposeDefinition" maxEntriesLocalHeap="1000" eternal="false" timeToIdleSeconds="6000" timeToLiveSeconds="6000" memoryStoreEvictionPolicy="LRU" statistics="true" />


</ehcache>