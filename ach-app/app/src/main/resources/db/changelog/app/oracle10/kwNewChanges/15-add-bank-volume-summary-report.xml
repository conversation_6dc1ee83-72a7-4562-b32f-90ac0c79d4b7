<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.25.xsd">
    <changeSet author="Ayo" id="add-bank-volume-summary-report-table">
        <createTable tableName="ATSBANKVOLUMESUMMARYREPORT">
            <column name="ID" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_5F1845434REPORT_PK"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_ASSIGNED_GROUP" type="NUMBER(19, 0)"/>
            <column name="Z_ASSIGNED_USER" type="NUMBER(19, 0)"/>
            <column name="Z_CREATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DELETED_FLAG" type="NUMBER(1, 0)"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="NUMBER(1, 0)"/>
            <column name="Z_LOCKED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="NUMBER(19, 0)"/>
            <column name="Z_TENANT_ID" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="NUMBER(19, 0)"/>
            <column name="Z_WS_TOKEN" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_ID" type="NUMBER(19, 0)"/>
            <column name="Z_STATUS_ID" type="NUMBER(19, 0)"/>
            <column name="REPORTFORMAT" type="VARCHAR2(19 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="ATTACHMENTUUID" type="VARCHAR2(50)"/>
            <column name="REPORTTYPE" type="VARCHAR2(5)"/>
            <column name="AMOUNTFROM" type="NUMBER(14, 5)"/>
            <column name="AMOUNTTO" type="NUMBER(14, 5)"/>
            <column name="DIRECTION" type="VARCHAR2(20 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="SESSIONDATEFROM" type="date"/>
            <column name="SESSIONDATETO" type="date"/>
            <column name="SESSIONSEQ" type="NUMBER(19, 0)"/>
            <column name="PARTICIPANTID" type="NUMBER(19, 0)"/>
            <column name="CATEGORYPURPOSES" type="NUMBER(19, 0)"/>
        </createTable>

            <createTable tableName="ATSBANKVOLUMESUMMARYREPORTATT">
                <column name="ID" type="NUMBER(19, 0)">
                    <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_7F17472H3PK"/>
                </column>
                <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
                <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
                <column name="Z_ARCHIVE_STATUS" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_ASSIGNED_GROUP" type="NUMBER(19, 0)"/>
                <column name="Z_ASSIGNED_USER" type="NUMBER(19, 0)"/>
                <column name="Z_CREATED_BY" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
                <column name="Z_DELETED_BY" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_DELETED_FLAG" type="NUMBER(1, 0)"/>
                <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
                <column name="Z_EDITABLE" type="NUMBER(1, 0)"/>
                <column name="Z_LOCKED_BY" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
                <column name="Z_ORG_ID" type="NUMBER(19, 0)"/>
                <column name="Z_TENANT_ID" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_UPDATED_BY" type="VARCHAR2(255 CHAR)"/>
                <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
                <column name="Z_WORKFLOW_ID" type="NUMBER(19, 0)"/>
                <column name="Z_WS_TOKEN" type="VARCHAR2(255 CHAR)"/>
                <column name="ATTFILE" type="BLOB"/>
                <column name="ATTACHMENT_SOURCE" type="VARCHAR2(255 CHAR)"/>
                <column name="ATTACHMENT_TOKEN" type="VARCHAR2(255 CHAR)"/>
                <column name="COMMENTS" type="VARCHAR2(255 CHAR)"/>
                <column name="CONTENTTYPE" type="VARCHAR2(255 CHAR)"/>
                <column name="ENTITYID" type="VARCHAR2(255 CHAR)"/>
                <column name="IMAGE_THUMBNAIL" type="BLOB"/>
                <column name="IMAGE_TYPE" type="VARCHAR2(255 CHAR)"/>
                <column name="NAME" type="VARCHAR2(255 CHAR)"/>
                <column name="ORIGINAL_MICR" type="VARCHAR2(255 CHAR)"/>
                <column name="RECORDID" type="VARCHAR2(255 CHAR)"/>
                <column name="REF_VALUE" type="VARCHAR2(255 CHAR)"/>
                <column name="REV" type="NUMBER(10, 0)"/>
                <column name="ATTACHMENT_SIZE" type="NUMBER(19, 2)"/>
            </createTable>

            <createTable tableName="ATSBANKVOLUMESUMMARYPURPOSES">
                <column name="BANKVOLUMESUMMARYID" type="NUMBER(19, 0)">
                    <constraints nullable="false"/>
                </column>
                <column name="CATEGORY_PURPOSE_ID" type="NUMBER(19, 0)">
                    <constraints nullable="false"/>
                </column>
            </createTable>

            <addForeignKeyConstraint baseColumnNames="BANKVOLUMESUMMARYID" baseTableName="ATSBANKVOLUMESUMMARYPURPOSES"
                                     constraintName="FK_BABTVDLVY8VUL98TNSQKM" deferrable="false"
                                     initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                     referencedColumnNames="ID" referencedTableName="ATSBANKVOLUMESUMMARYREPORT"
                                     validate="true"/>

        <addForeignKeyConstraint baseColumnNames="SESSIONSEQ" baseTableName="ATSBANKVOLUMESUMMARYREPORT"
                                 constraintName="FK_JJKCJOG652NH89F8FK8874ALU9" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="ATSBDI_Sessions" validate="true"/>

        <addForeignKeyConstraint baseColumnNames="CATEGORYPURPOSES" baseTableName="ATSBANKVOLUMESUMMARYREPORT"
                                 constraintName="FK_ONDCJOG652058GXH8Y98O44315" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="ATSMSG_CtgPurps" validate="true"/>

    </changeSet>
</databaseChangeLog>