--Procedure to generate txn_report
create or replace PROCEDURE getparttranspersessionxml(
    pprt_id       IN            NUMBER,
    psession_id   IN            NUMBER,
    rxml          OUT           BLOB
) IS
    CURSOR rrefcursor IS
SELECT '<details id="'||TXID||'"  type="'|| msgtype.code||'" amount="'|| amount||'"  state="'||    (CASE WHEN REASONID IS NULL THEN 'ACPT' ELSE 'RJCT' END)||'"  reason="'||reason.code||'"  onus="'||   (CASE WHEN INSTGPARTIID = INSTDPARTIID THEN 'true' ELSE 'false' END)||'" />'
FROM atstransactions tx, ATSMSG_TYPES msgtype, ATSMSG_REASONS reason
WHERE     sessionid = (SELECT id
                       FROM atsbdi_sessions
                       WHERE code = psession_id)
  AND (INSTGPARTIID = pprt_id OR INSTDPARTIID = pprt_id)
  AND  REASONID IN (SELECT id
                    FROM atsmsg_reasons
                    WHERE INSTRUCTEDAWARE = 1)
  AND tx.MSGTYPEID = msgtype.id
  and tx.REASONID=reason.id
union
SELECT '<details id="'||TXID||'"'|| '  type="'|| msgtype.code||'"' || '  amount="'|| amount||'" state="ACSC"'|| '  onus="'||   (CASE WHEN INSTGPARTIID = INSTDPARTIID THEN 'true' ELSE 'false' END)||'"/>'
FROM atstransactions tx, ATSMSG_TYPES msgtype
WHERE     sessionid = (SELECT id
                       FROM atsbdi_sessions
                       WHERE code = psession_id)
  AND (INSTGPARTIID = pprt_id OR INSTDPARTIID = pprt_id)
  AND  REASONID IS NULL
  AND tx.MSGTYPEID = msgtype.id
union
SELECT '<details id="'||TXID||'"  type="'|| msgtype.code||'" amount="'|| amount||'"  state="'||    (CASE WHEN REASONID IS NULL THEN 'ACPT' ELSE 'RJCT' END)||'"  reason="'||reason.code||'" />'
FROM atsrtransactions tx, ATSMSG_TYPES msgtype, ATSMSG_REASONS reason
WHERE     sessionid = (SELECT id
                       FROM atsbdi_sessions
                       WHERE code = psession_id)
  AND (INSTGPARTIID = pprt_id OR INSTDPARTIID = pprt_id)
  AND  REASONID IN (SELECT id
                    FROM atsmsg_reasons
                    WHERE INSTRUCTEDAWARE = 1)
  AND tx.MSGTYPEID = msgtype.id
  and tx.REASONID=reason.id
  and msgtype.code in ('RT','RV')
union
SELECT '<details id="'||TXID||'"'|| '  type="'|| msgtype.code||'"' || '  amount="'|| amount||'"  state="ACSC" />'
FROM atsrtransactions tx, ATSMSG_TYPES msgtype
WHERE     sessionid = (SELECT id
                       FROM atsbdi_sessions
                       WHERE code =psession_id)
  AND (INSTGPARTIID = pprt_id OR INSTDPARTIID = pprt_id)
  AND  REASONID IS NULL
  AND tx.MSGTYPEID = msgtype.id
  and msgtype.code in ('RT','RV')
;

vrow_xml           VARCHAR2(32767);
    header_det  varchar(500);
    rrecord_cnt number(10,0);
    starttime date;
    temp_clob1 clob;
    temp_clob2 clob;
    temp_clob3 clob;
BEGIN
    starttime := sysdate;
    rrecord_cnt := 0;
    rxml := to_blob('');

SELECT    '<txReport settlementDate="'
              || (SELECT TO_CHAR (BUSINESSDT, 'YYYY-MM-DD')
                  FROM atsbdi_sessions
                  WHERE code = psession_id)
              || '"'
              || ' sessionSequence="'
              || psession_id
              || '"'
              || ' currency="KWD" participant="'
              || (SELECT code
                  FROM ATSPRT_PARTICIPANTS
                  WHERE id = pprt_id)
              || '" settlementRetry="0">'
INTO header_det
FROM DUAL;

temp_clob1 := to_clob('<?xml version="1.0" encoding="UTF-8" standalone="yes"?>');
    temp_clob1 :=concat(temp_clob1,chr(13));
    temp_clob2 := to_clob(' ');
    temp_clob3 := to_clob(' ');

OPEN rrefcursor;
LOOP
FETCH rrefcursor INTO vrow_xml;
            EXIT WHEN rrefcursor%notfound;
            IF vrow_xml IS NOT NULL THEN
                temp_clob2 := concat(temp_clob2, vrow_xml);
                temp_clob2 :=concat(temp_clob2,chr(13));
                rrecord_cnt := rrecord_cnt + 1;
END IF;
END LOOP;
CLOSE rrefcursor;

IF rrecord_cnt > 0 THEN
          temp_clob3 := concat(temp_clob3,temp_clob2);

END IF;
     header_det := concat(header_det,chr(13));
    temp_clob1 := concat(temp_clob1,header_det);
    temp_clob1 := concat(temp_clob1,temp_clob3);
    temp_clob1 := concat(temp_clob1,'</txReport>');
    rxml := UTL_COMPRESS.LZ_COMPRESS (CLOB_TO_BLOB ( temp_clob1, DBMS_LOB.DEFAULT_CSID, 0));
   log_xml_timing(pprt_id,rxml,null,rrecord_cnt,starttime,psession_id);
END;