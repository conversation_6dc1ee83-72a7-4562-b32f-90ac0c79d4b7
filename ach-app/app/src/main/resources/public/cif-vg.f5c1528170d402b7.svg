<svg xmlns="http://www.w3.org/2000/svg" width="400" height="200" viewBox="0 0 300 150">
  <defs>
    <linearGradient id="e" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="f" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="g" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="h" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="i" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="j" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="k" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="l" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="m" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="n" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="o" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <linearGradient id="p" x1="103.083" x2="92.551" y1="111.276" y2="107.764" gradientTransform="matrix(.99614 0 0 2.25255 703.842 -75.047)" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="red"/>
      <stop offset="1" stop-color="#ff0"/>
    </linearGradient>
    <clipPath id="a">
      <path d="M 0 0 L 150 0 L 150 75 L 0 75 Z M 0 0"/>
    </clipPath>
    <clipPath id="b">
      <path d="M 0 0 L 0 37.5 L 175 37.5 L 175 75 L 150 75 Z M 150 0 L 75 0 L 75 87.5 L 0 87.5 L 0 75 Z M 150 0"/>
    </clipPath>
    <clipPath id="c">
      <path d="M 0 0 L 175 0 L 175 88 L 0 88 Z M 0 0"/>
    </clipPath>
    <clipPath id="d">
      <path d="M 0 0 L 0 37.5 L 175 37.5 L 175 75 L 150 75 Z M 150 0 L 75 0 L 75 87.5 L 0 87.5 L 0 75 Z M 150 0"/>
    </clipPath>
  </defs>
  <rect width="300" height="150" fill="#012169"/>
  <path stroke="#fff" stroke-width="60" d="M 0 0 L 600 300 M 600 0 L 0 300" transform="scale(.25)"/>
  <g clip-path="url(#a)">
    <g clip-path="url(#b)">
      <path d="M 0 0 L 150 75 M 150 0 L 0 75"/>
    </g>
  </g>
  <g clip-path="url(#c)">
    <g clip-path="url(#d)">
      <path fill="none" stroke="#c8102e" stroke-width="40" d="M 0 0 L 600 300 M 600 0 L 0 300" transform="scale(.25)"/>
    </g>
  </g>
  <path stroke="#fff" stroke-width="100" d="M 300 0 L 300 350 M 0 150 L 700 150" transform="scale(.25)"/>
  <path stroke="#c8102e" stroke-width="60" d="M 300 0 L 300 350 M 0 150 L 700 150" transform="scale(.25)"/>
  <path fill="#012169" d="M 0 75 L 150 75 L 150 0 L 300 0 L 300 150 L 0 150 Z M 0 75"/>
  <path fill="#fff" d="M 194.066406 36.015625 L 256.664062 35.785156 L 256.550781 91.53125 C 256.550781 91.53125 258.753906 100.011719 230.300781 112.902344 C 240.523438 111.859375 251.671875 100.941406 251.671875 100.941406 C 251.671875 100.941406 256.199219 95.132812 258.40625 98.386719 C 260.613281 101.636719 262.703125 103.261719 264.332031 104.539062 C 265.957031 105.816406 267.234375 109.300781 264.796875 111.859375 C 262.355469 114.414062 258.523438 114.761719 257.476562 111.625 C 255.851562 112.4375 245.863281 124.515625 225.421875 125.097656 C 204.632812 124.75 193.253906 111.507812 193.253906 111.507812 C 193.253906 111.507812 190.464844 115.921875 186.515625 112.4375 C 182.683594 107.910156 185.585938 105.003906 185.585938 105.003906 C 185.585938 105.003906 188.839844 103.148438 189.769531 101.871094 C 191.277344 100.128906 191.742188 97.804688 194.296875 97.804688 C 197.316406 98.035156 198.476562 100.476562 198.476562 100.476562 C 198.476562 100.476562 208.929688 111.507812 220.195312 112.902344 C 194.761719 100.707031 193.832031 93.15625 193.949219 91.300781 Z M 194.066406 36.015625"/>
  <path fill="#006129" stroke="#000" stroke-width="1.887" d="M 782.296875 150.109375 L 1021.09375 148.71875 L 1021.09375 363.34375 C 1021.546875 391.21875 974.625 419.5625 901.234375 455.796875 C 825.5 416.765625 781.828125 393.078125 781.375 362.875 Z M 782.296875 150.109375" transform="scale(.25)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.796875 181.828125 L 828.890625 161.125 L 843.046875 181.875" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.71875 174.09375 C 831.71875 175.640625 830.46875 176.890625 828.921875 176.890625 C 827.375 176.890625 826.125 175.640625 826.125 174.09375 C 826.125 172.546875 827.375 171.296875 828.921875 171.296875 C 830.46875 171.296875 831.71875 172.546875 831.71875 174.09375 Z M 831.71875 174.09375" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.4375 197.125 L 839.296875 197.1875 C 839.296875 197.1875 839.640625 194.34375 837.015625 192.625 C 848.453125 191.046875 845.484375 180.953125 855.078125 180.375 C 856.9375 180.671875 850.0625 184.671875 850.0625 184.671875 C 850.0625 184.671875 844.28125 188.75 846.921875 190.828125 C 849 192.46875 849.921875 189.828125 850.203125 187.8125 C 850.5 185.8125 859.5 184.53125 858.21875 178.8125 C 856.078125 174.078125 843.484375 181.953125 843.484375 181.953125 L 834.5 181.90625 C 833.9375 180.890625 831.46875 176.859375 828.90625 176.828125 C 825.875 176.9375 823.734375 181.953125 823.734375 181.953125 L 803.421875 181.953125 C 803.421875 181.953125 802.734375 187.171875 813.03125 188.171875 C 815.34375 191.203125 817.140625 192.046875 819.15625 192.828125 C 817.8125 193.953125 817.4375 195.296875 817.4375 197.125 Z M 817.4375 197.125" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.015625 192.6875 L 837.21875 192.625" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.6875 181.953125 C 810.6875 181.953125 812.140625 190.46875 818.859375 192.484375" transform="scale(.25)"/>
  <path fill="url(#e)" stroke="#000" stroke-width=".944" d="M 809.5625 181.84375 C 810.234375 178.59375 811.46875 178.03125 812.8125 173.890625 C 813.03125 169.859375 809.5625 170.296875 810.5625 167.734375 C 812.359375 164.921875 811.46875 162.234375 808.109375 160.109375 C 808.78125 163.8125 803.734375 167.28125 803.734375 170.296875 C 803.734375 173.328125 806.3125 172.65625 805.984375 177.25 C 806.203125 179.9375 805.3125 179.265625 805.078125 181.84375 Z M 809.5625 181.84375" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.5 161.65625 C 831.5 163.078125 830.34375 164.234375 828.921875 164.234375 C 827.5 164.234375 826.34375 163.078125 826.34375 161.65625 C 826.34375 160.234375 827.5 159.078125 828.921875 159.078125 C 830.34375 159.078125 831.5 160.234375 831.5 161.65625 Z M 831.5 161.65625" transform="scale(.25)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.787826 181.83661 L 828.881576 161.133485 L 843.053451 181.86786" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.725326 174.08661 C 831.725326 175.633485 830.459701 176.883485 828.912826 176.883485 C 827.365951 176.883485 826.115951 175.633485 826.115951 174.08661 C 826.115951 172.539735 827.365951 171.289735 828.912826 171.289735 C 830.459701 171.289735 831.725326 172.539735 831.725326 174.08661 Z M 831.725326 174.08661" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.444076 197.11786 L 839.287826 197.18036 C 839.287826 197.18036 839.631576 194.352235 837.006576 192.61786 C 848.459701 191.05536 845.490951 180.945985 855.069076 180.383485 C 856.928451 180.664735 850.069076 184.664735 850.069076 184.664735 C 850.069076 184.664735 844.287826 188.74286 846.912826 190.820985 C 849.006576 192.477235 849.912826 189.820985 850.209701 187.820985 C 850.490951 185.820985 859.506576 184.52411 858.225326 178.80536 C 856.069076 174.08661 843.475326 181.945985 843.475326 181.945985 L 834.506576 181.89911 C 833.944076 180.89911 831.459701 176.852235 828.912826 176.83661 C 825.881576 176.945985 823.740951 181.945985 823.740951 181.945985 L 803.428451 181.945985 C 803.428451 181.945985 802.725326 187.164735 813.022201 188.164735 C 815.350326 191.195985 817.147201 192.039735 819.162826 192.820985 C 817.819076 193.945985 817.444076 195.30536 817.444076 197.11786 Z M 817.444076 197.11786" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.022201 192.68036 L 837.225326 192.61786" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.678451 181.945985 C 810.678451 181.945985 812.131576 190.46161 818.850326 192.477235" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="url(#f)" stroke="#000" stroke-width=".944" d="M 809.553451 181.83661 C 810.225326 178.58661 811.459701 178.039735 812.803451 173.883485 C 813.037826 169.852235 809.553451 170.30536 810.569076 167.727235 C 812.365951 164.93036 811.459701 162.24286 808.100326 160.11786 C 808.772201 163.80536 803.740951 167.27411 803.740951 170.30536 C 803.740951 173.320985 806.319076 172.64911 805.975326 177.24286 C 806.194076 179.93036 805.303451 179.258485 805.084701 181.83661 Z M 809.553451 181.83661" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.490951 161.664735 C 831.490951 163.08661 830.334701 164.227235 828.912826 164.227235 C 827.490951 164.227235 826.350326 163.08661 826.350326 161.664735 C 826.350326 160.227235 827.490951 159.08661 828.912826 159.08661 C 830.334701 159.08661 831.490951 160.227235 831.490951 161.664735 Z M 831.490951 161.664735" transform="matrix(.25 0 0 .25 -.193 10.326)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.792326 181.830735 L 828.886076 161.12761 L 843.057951 181.87761" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.714201 174.09636 C 831.714201 175.643235 830.464201 176.893235 828.917326 176.893235 C 827.370451 176.893235 826.120451 175.643235 826.120451 174.09636 C 826.120451 172.549485 827.370451 171.28386 828.917326 171.28386 C 830.464201 171.28386 831.714201 172.549485 831.714201 174.09636 Z M 831.714201 174.09636" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.448576 197.111985 L 839.292326 197.174485 C 839.292326 197.174485 839.636076 194.34636 837.011076 192.62761 C 848.464201 191.049485 845.479826 180.955735 855.073576 180.37761 C 856.932951 180.65886 850.057951 184.674485 850.057951 184.674485 C 850.057951 184.674485 844.276701 188.75261 846.917326 190.830735 C 849.011076 192.47136 849.917326 189.81511 850.198576 187.81511 C 850.495451 185.81511 859.511076 184.53386 858.214201 178.799485 C 856.073576 174.080735 843.479826 181.955735 843.479826 181.955735 L 834.495451 181.90886 C 833.932951 180.893235 831.464201 176.84636 828.901701 176.830735 C 825.870451 176.94011 823.745451 181.955735 823.745451 181.955735 L 803.417326 181.955735 C 803.417326 181.955735 802.729826 187.15886 813.026701 188.174485 C 815.339201 191.19011 817.151701 192.049485 819.167326 192.830735 C 817.823576 193.94011 817.432951 195.299485 817.448576 197.111985 Z M 817.448576 197.111985" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.011076 192.69011 L 837.214201 192.62761" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.682951 181.955735 C 810.682951 181.955735 812.136076 190.47136 818.854826 192.486985" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="url(#g)" stroke="#000" stroke-width=".944" d="M 809.557951 181.84636 C 810.229826 178.59636 811.464201 178.03386 812.807951 173.893235 C 813.026701 169.861985 809.557951 170.299485 810.573576 167.72136 C 812.354826 164.924485 811.464201 162.236985 808.104826 160.111985 C 808.776701 163.81511 803.729826 167.28386 803.729826 170.299485 C 803.729826 173.330735 806.307951 172.65886 805.979826 177.25261 C 806.198576 179.94011 805.307951 179.268235 805.073576 181.84636 Z M 809.557951 181.84636" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.495451 161.65886 C 831.495451 163.080735 830.339201 164.236985 828.917326 164.236985 C 827.495451 164.236985 826.339201 163.080735 826.339201 161.65886 C 826.339201 160.236985 827.495451 159.080735 828.917326 159.080735 C 830.339201 159.080735 831.495451 160.236985 831.495451 161.65886 Z M 831.495451 161.65886" transform="matrix(.25 0 0 .25 -.272 20.503)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.795076 181.822865 L 828.888826 161.135365 L 843.045076 181.86974" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.716951 174.08849 C 831.716951 175.635365 830.466951 176.885365 828.920076 176.885365 C 827.373201 176.885365 826.123201 175.635365 826.123201 174.08849 C 826.123201 172.541615 827.373201 171.291615 828.920076 171.291615 C 830.466951 171.291615 831.716951 172.541615 831.716951 174.08849 Z M 831.716951 174.08849" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.451326 197.11974 L 839.295076 197.18224 C 839.295076 197.18224 839.638826 194.33849 837.013826 192.61974 C 848.451326 191.041615 845.482576 180.947865 855.076326 180.385365 C 856.935701 180.666615 850.060701 184.666615 850.060701 184.666615 C 850.060701 184.666615 844.279451 188.74474 846.920076 190.822865 C 848.998201 192.46349 849.920076 189.822865 850.201326 187.822865 C 850.498201 185.822865 859.498201 184.52599 858.216951 178.80724 C 856.076326 174.08849 843.482576 181.947865 843.482576 181.947865 L 834.498201 181.90099 C 833.935701 180.90099 831.466951 176.854115 828.904451 176.83849 C 825.873201 176.947865 823.732576 181.947865 823.732576 181.947865 L 803.420076 181.947865 C 803.420076 181.947865 802.732576 187.166615 813.029451 188.166615 C 815.341951 191.197865 817.138826 192.041615 819.154451 192.822865 C 817.810701 193.947865 817.435701 195.30724 817.451326 197.11974 Z M 817.451326 197.11974" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.013826 192.68224 L 837.216951 192.61974" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.685701 181.947865 C 810.685701 181.947865 812.138826 190.46349 818.857576 192.479115" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="url(#h)" stroke="#000" stroke-width=".944" d="M 809.560701 181.83849 C 810.232576 178.58849 811.466951 178.02599 812.810701 173.885365 C 813.029451 169.854115 809.560701 170.30724 810.576326 167.729115 C 812.357576 164.93224 811.466951 162.24474 808.107576 160.104115 C 808.779451 163.80724 803.732576 167.27599 803.732576 170.30724 C 803.732576 173.322865 806.310701 172.65099 805.982576 177.24474 C 806.201326 179.93224 805.310701 179.260365 805.076326 181.83849 Z M 809.560701 181.83849" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.498201 161.65099 C 831.498201 163.072865 830.341951 164.229115 828.920076 164.229115 C 827.498201 164.229115 826.341951 163.072865 826.341951 161.65099 C 826.341951 160.229115 827.498201 159.08849 828.920076 159.08849 C 830.341951 159.08849 831.498201 160.229115 831.498201 161.65099 Z M 831.498201 161.65099" transform="matrix(.25 0 0 .25 6.407 31.525)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.792951 181.821365 L 828.886701 161.133865 L 843.058576 181.86824" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.714826 174.08699 C 831.714826 175.633865 830.464826 176.883865 828.917951 176.883865 C 827.371076 176.883865 826.121076 175.633865 826.121076 174.08699 C 826.121076 172.540115 827.371076 171.290115 828.917951 171.290115 C 830.464826 171.290115 831.714826 172.540115 831.714826 174.08699 Z M 831.714826 174.08699" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.449201 197.11824 L 839.292951 197.18074 C 839.292951 197.18074 839.636701 194.352615 837.011701 192.61824 C 848.464826 191.040115 845.480451 180.946365 855.074201 180.383865 C 856.933576 180.665115 850.058576 184.665115 850.058576 184.665115 C 850.058576 184.665115 844.277326 188.74324 846.917951 190.821365 C 849.011701 192.477615 849.917951 189.821365 850.199201 187.821365 C 850.496076 185.821365 859.511701 184.52449 858.214826 178.80574 C 856.074201 174.08699 843.480451 181.946365 843.480451 181.946365 L 834.496076 181.89949 C 833.933576 180.89949 831.464826 176.852615 828.902326 176.83699 C 825.871076 176.946365 823.746076 181.946365 823.746076 181.946365 L 803.417951 181.946365 C 803.417951 181.946365 802.730451 187.165115 813.027326 188.165115 C 815.339826 191.196365 817.152326 192.040115 819.167951 192.821365 C 817.824201 193.946365 817.433576 195.30574 817.449201 197.11824 Z M 817.449201 197.11824" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.011701 192.68074 L 837.214826 192.61824" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.683576 181.946365 C 810.683576 181.946365 812.136701 190.46199 818.855451 192.477615" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="url(#i)" stroke="#000" stroke-width=".944" d="M 809.558576 181.83699 C 810.230451 178.58699 811.464826 178.040115 812.808576 173.883865 C 813.027326 169.852615 809.558576 170.30574 810.574201 167.727615 C 812.355451 164.93074 811.464826 162.24324 808.105451 160.11824 C 808.777326 163.80574 803.730451 167.27449 803.730451 170.30574 C 803.730451 173.321365 806.308576 172.64949 805.980451 177.24324 C 806.199201 179.93074 805.308576 179.258865 805.074201 181.83699 Z M 809.558576 181.83699" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.496076 161.64949 C 831.496076 163.08699 830.339826 164.227615 828.917951 164.227615 C 827.496076 164.227615 826.339826 163.08699 826.339826 161.64949 C 826.339826 160.227615 827.496076 159.08699 828.917951 159.08699 C 830.339826 159.08699 831.496076 160.227615 831.496076 161.64949 Z M 831.496076 161.64949" transform="matrix(.25 0 0 .25 -1.144 40.54)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.797826 181.836365 L 828.891576 161.13324 L 843.047826 181.867615" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.719701 174.086365 C 831.719701 175.63324 830.469701 176.88324 828.922826 176.88324 C 827.375951 176.88324 826.125951 175.63324 826.125951 174.086365 C 826.125951 172.53949 827.375951 171.28949 828.922826 171.28949 C 830.469701 171.28949 831.719701 172.53949 831.719701 174.086365 Z M 831.719701 174.086365" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.438451 197.117615 L 839.297826 197.180115 C 839.297826 197.180115 839.641576 194.35199 837.016576 192.617615 C 848.454076 191.055115 845.485326 180.94574 855.063451 180.38324 C 856.922826 180.66449 850.063451 184.66449 850.063451 184.66449 C 850.063451 184.66449 844.282201 188.75824 846.922826 190.82074 C 849.000951 192.47699 849.922826 189.82074 850.204076 187.82074 C 850.485326 185.82074 859.500951 184.523865 858.219701 178.805115 C 856.079076 174.086365 843.485326 181.961365 843.485326 181.961365 L 834.500951 181.898865 C 833.938451 180.898865 831.469701 176.85199 828.907201 176.836365 C 825.875951 176.94574 823.735326 181.961365 823.735326 181.961365 L 803.422826 181.961365 C 803.422826 181.961365 802.719701 187.16449 813.032201 188.16449 C 815.344701 191.19574 817.141576 192.03949 819.157201 192.82074 C 817.813451 193.94574 817.438451 195.305115 817.438451 197.117615 Z M 817.438451 197.117615" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.016576 192.680115 L 837.219701 192.617615" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.688451 181.94574 C 810.688451 181.94574 812.141576 190.461365 818.860326 192.47699" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="url(#j)" stroke="#000" stroke-width=".944" d="M 809.563451 181.836365 C 810.235326 178.586365 811.469701 178.03949 812.813451 173.88324 C 813.032201 169.85199 809.563451 170.305115 810.563451 167.72699 C 812.360326 164.930115 811.469701 162.242615 808.110326 160.117615 C 808.782201 163.805115 803.735326 167.273865 803.735326 170.305115 C 803.735326 173.32074 806.313451 172.648865 805.969701 177.242615 C 806.204076 179.930115 805.297826 179.25824 805.079076 181.836365 Z M 809.563451 181.836365" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.500951 161.66449 C 831.500951 163.086365 830.344701 164.22699 828.922826 164.22699 C 827.500951 164.22699 826.344701 163.086365 826.344701 161.66449 C 826.344701 160.22699 827.500951 159.086365 828.922826 159.086365 C 830.344701 159.086365 831.500951 160.22699 831.500951 161.66449 Z M 831.500951 161.66449" transform="matrix(.25 0 0 .25 -.946 51.193)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.798325 181.827985 L 828.892075 161.12486 L 843.048325 181.87486" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.7202 174.09361 C 831.7202 175.640485 830.4702 176.890485 828.923325 176.890485 C 827.37645 176.890485 826.12645 175.640485 826.12645 174.09361 C 826.12645 172.546735 827.37645 171.296735 828.923325 171.296735 C 830.4702 171.296735 831.7202 172.546735 831.7202 174.09361 Z M 831.7202 174.09361" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.43895 197.12486 L 839.298325 197.18736 C 839.298325 197.18736 839.642075 194.34361 837.017075 192.62486 C 848.454575 191.046735 845.485825 180.952985 855.06395 180.37486 C 856.923325 180.671735 850.06395 184.671735 850.06395 184.671735 C 850.06395 184.671735 844.2827 188.74986 846.923325 190.827985 C 849.00145 192.46861 849.923325 189.827985 850.204575 187.81236 C 850.485825 185.81236 859.50145 184.53111 858.2202 178.81236 C 856.079575 174.077985 843.485825 181.952985 843.485825 181.952985 L 834.50145 181.90611 C 833.93895 180.890485 831.4702 176.859235 828.9077 176.827985 C 825.87645 176.952985 823.735825 181.952985 823.735825 181.952985 L 803.423325 181.952985 C 803.423325 181.952985 802.7202 187.171735 813.0327 188.171735 C 815.3452 191.202985 817.142075 192.046735 819.1577 192.827985 C 817.81395 193.952985 817.43895 195.296735 817.43895 197.12486 Z M 817.43895 197.12486" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.017075 192.68736 L 837.2202 192.62486" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.673325 181.952985 C 810.673325 181.952985 812.142075 190.46861 818.860825 192.484235" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="url(#k)" stroke="#000" stroke-width=".944" d="M 809.56395 181.84361 C 810.235825 178.59361 811.4702 178.03111 812.81395 173.890485 C 813.0327 169.859235 809.56395 170.296735 810.56395 167.734235 C 812.360825 164.921735 811.4702 162.234235 808.110825 160.109235 C 808.7827 163.81236 803.735825 167.28111 803.735825 170.296735 C 803.735825 173.327985 806.31395 172.65611 805.9702 177.24986 C 806.204575 179.93736 805.298325 179.265485 805.079575 181.84361 Z M 809.56395 181.84361" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.50145 161.65611 C 831.50145 163.077985 830.3452 164.234235 828.923325 164.234235 C 827.50145 164.234235 826.3452 163.077985 826.3452 161.65611 C 826.3452 160.234235 827.50145 159.077985 828.923325 159.077985 C 830.3452 159.077985 831.50145 160.234235 831.50145 161.65611 Z M 831.50145 161.65611" transform="matrix(.25 0 0 .25 36.57 .168)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.798575 181.82886 L 828.892325 161.125735 L 843.048575 181.875735" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.72045 174.094485 C 831.72045 175.64136 830.47045 176.89136 828.923575 176.89136 C 827.3767 176.89136 826.1267 175.64136 826.1267 174.094485 C 826.1267 172.54761 827.3767 171.29761 828.923575 171.29761 C 830.47045 171.29761 831.72045 172.54761 831.72045 174.094485 Z M 831.72045 174.094485" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.4392 197.125735 L 839.298575 197.188235 C 839.298575 197.188235 839.642325 194.344485 837.017325 192.625735 C 848.454825 191.04761 845.486075 180.95386 855.0642 180.375735 C 856.923575 180.67261 850.0642 184.67261 850.0642 184.67261 C 850.0642 184.67261 844.28295 188.750735 846.923575 190.82886 C 849.0017 192.469485 849.923575 189.82886 850.204825 187.813235 C 850.486075 185.813235 859.5017 184.531985 858.22045 178.813235 C 856.079825 174.07886 843.486075 181.95386 843.486075 181.95386 L 834.5017 181.906985 C 833.9392 180.89136 831.47045 176.86011 828.90795 176.82886 C 825.8767 176.938235 823.736075 181.95386 823.736075 181.95386 L 803.423575 181.95386 C 803.423575 181.95386 802.72045 187.17261 813.03295 188.17261 C 815.34545 191.20386 817.142325 192.04761 819.15795 192.82886 C 817.8142 193.95386 817.4392 195.29761 817.4392 197.125735 Z M 817.4392 197.125735" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.017325 192.688235 L 837.22045 192.625735" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.673575 181.95386 C 810.673575 181.95386 812.142325 190.469485 818.861075 192.48511" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="url(#l)" stroke="#000" stroke-width=".944" d="M 809.5642 181.844485 C 810.236075 178.594485 811.47045 178.031985 812.8142 173.89136 C 813.03295 169.86011 809.5642 170.29761 810.5642 167.73511 C 812.361075 164.92261 811.47045 162.23511 808.111075 160.11011 C 808.78295 163.813235 803.736075 167.281985 803.736075 170.29761 C 803.736075 173.32886 806.3142 172.656985 805.97045 177.250735 C 806.204825 179.938235 805.298575 179.26636 805.079825 181.844485 Z M 809.5642 181.844485" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.5017 161.656985 C 831.5017 163.07886 830.34545 164.23511 828.923575 164.23511 C 827.5017 164.23511 826.34545 163.07886 826.34545 161.656985 C 826.34545 160.23511 827.5017 159.07886 828.923575 159.07886 C 830.34545 159.07886 831.5017 160.23511 831.5017 161.656985 Z M 831.5017 161.656985" transform="matrix(.25 0 0 .25 36.234 10.305)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.7937 181.82336 L 828.88745 161.120235 L 843.059325 181.870235" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.715575 174.088985 C 831.715575 175.63586 830.465575 176.88586 828.9187 176.88586 C 827.371825 176.88586 826.121825 175.63586 826.121825 174.088985 C 826.121825 172.54211 827.371825 171.29211 828.9187 171.29211 C 830.465575 171.29211 831.715575 172.54211 831.715575 174.088985 Z M 831.715575 174.088985" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.44995 197.120235 L 839.2937 197.182735 C 839.2937 197.182735 839.63745 194.338985 837.01245 192.620235 C 848.465575 191.04211 845.4812 180.94836 855.07495 180.38586 C 856.934325 180.66711 850.059325 184.66711 850.059325 184.66711 C 850.059325 184.66711 844.278075 188.745235 846.9187 190.82336 C 849.01245 192.463985 849.9187 189.82336 850.19995 187.82336 C 850.496825 185.82336 859.51245 184.526485 858.215575 178.807735 C 856.07495 174.088985 843.4812 181.94836 843.4812 181.94836 L 834.496825 181.901485 C 833.934325 180.901485 831.465575 176.85461 828.903075 176.838985 C 825.871825 176.94836 823.7312 181.94836 823.7312 181.94836 L 803.4187 181.94836 C 803.4187 181.94836 802.7312 187.16711 813.028075 188.16711 C 815.340575 191.19836 817.13745 192.04211 819.153075 192.82336 C 817.809325 193.94836 817.434325 195.307735 817.44995 197.120235 Z M 817.44995 197.120235" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.01245 192.682735 L 837.215575 192.620235" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.684325 181.94836 C 810.684325 181.94836 812.13745 190.463985 818.8562 192.47961" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="url(#m)" stroke="#000" stroke-width=".944" d="M 809.559325 181.838985 C 810.2312 178.588985 811.465575 178.026485 812.809325 173.88586 C 813.028075 169.85461 809.559325 170.307735 810.57495 167.72961 C 812.3562 164.932735 811.465575 162.245235 808.1062 160.10461 C 808.778075 163.807735 803.7312 167.276485 803.7312 170.307735 C 803.7312 173.32336 806.309325 172.651485 805.9812 177.245235 C 806.19995 179.932735 805.309325 179.26086 805.07495 181.838985 Z M 809.559325 181.838985" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.496825 161.651485 C 831.496825 163.07336 830.340575 164.22961 828.9187 164.22961 C 827.496825 164.22961 826.340575 163.07336 826.340575 161.651485 C 826.340575 160.22961 827.496825 159.07336 828.9187 159.07336 C 830.340575 159.07336 831.496825 160.22961 831.496825 161.651485 Z M 831.496825 161.651485" transform="matrix(.25 0 0 .25 35.786 20.665)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.793325 181.834615 L 828.887075 161.13149 L 843.05895 181.865865" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.7152 174.084615 C 831.7152 175.63149 830.4652 176.897115 828.918325 176.897115 C 827.37145 176.897115 826.12145 175.63149 826.12145 174.084615 C 826.12145 172.53774 827.37145 171.28774 828.918325 171.28774 C 830.4652 171.28774 831.7152 172.53774 831.7152 174.084615 Z M 831.7152 174.084615" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.449575 197.115865 L 839.293325 197.178365 C 839.293325 197.178365 839.637075 194.35024 837.012075 192.615865 C 848.4652 191.053365 845.480825 180.959615 855.074575 180.38149 C 856.93395 180.66274 850.05895 184.678365 850.05895 184.678365 C 850.05895 184.678365 844.2777 188.75649 846.918325 190.81899 C 849.012075 192.47524 849.918325 189.81899 850.199575 187.81899 C 850.49645 185.81899 859.512075 184.522115 858.2152 178.803365 C 856.074575 174.084615 843.480825 181.959615 843.480825 181.959615 L 834.49645 181.897115 C 833.93395 180.897115 831.4652 176.85024 828.9027 176.834615 C 825.87145 176.94399 823.74645 181.959615 823.74645 181.959615 L 803.418325 181.959615 C 803.418325 181.959615 802.730825 187.16274 813.0277 188.16274 C 815.3402 191.19399 817.137075 192.03774 819.168325 192.834615 C 817.80895 193.94399 817.43395 195.303365 817.449575 197.115865 Z M 817.449575 197.115865" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.012075 192.678365 L 837.2152 192.615865" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.68395 181.959615 C 810.68395 181.959615 812.137075 190.459615 818.855825 192.47524" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="url(#n)" stroke="#000" stroke-width=".944" d="M 809.55895 181.834615 C 810.230825 178.60024 811.4652 178.03774 812.80895 173.88149 C 813.0277 169.85024 809.55895 170.303365 810.574575 167.72524 C 812.355825 164.928365 811.4652 162.240865 808.105825 160.115865 C 808.7777 163.803365 803.730825 167.28774 803.730825 170.303365 C 803.730825 173.334615 806.30895 172.66274 805.980825 177.25649 C 806.199575 179.94399 805.30895 179.272115 805.074575 181.834615 Z M 809.55895 181.834615" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.49645 161.66274 C 831.49645 163.084615 830.3402 164.22524 828.918325 164.22524 C 827.49645 164.22524 826.3402 163.084615 826.3402 161.66274 C 826.3402 160.240865 827.49645 159.084615 828.918325 159.084615 C 830.3402 159.084615 831.49645 160.240865 831.49645 161.66274 Z M 831.49645 161.66274" transform="matrix(.25 0 0 .25 36.29 30.858)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.7882 181.83024 L 828.88195 161.127115 L 843.053825 181.877115" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.7257 174.095865 C 831.7257 175.64274 830.460075 176.89274 828.9132 176.89274 C 827.366325 176.89274 826.116325 175.64274 826.116325 174.095865 C 826.116325 172.54899 827.366325 171.283365 828.9132 171.283365 C 830.460075 171.283365 831.7257 172.54899 831.7257 174.095865 Z M 831.7257 174.095865" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.44445 197.11149 L 839.2882 197.17399 C 839.2882 197.17399 839.63195 194.345865 837.00695 192.627115 C 848.460075 191.04899 845.491325 180.95524 855.06945 180.377115 C 856.928825 180.658365 850.06945 184.67399 850.06945 184.67399 C 850.06945 184.67399 844.2882 188.752115 846.9132 190.83024 C 849.00695 192.470865 849.9132 189.83024 850.210075 187.814615 C 850.491325 185.814615 859.50695 184.533365 858.2257 178.79899 C 856.06945 174.08024 843.4757 181.95524 843.4757 181.95524 L 834.50695 181.908365 C 833.94445 180.89274 831.460075 176.845865 828.9132 176.83024 C 825.88195 176.939615 823.741325 181.95524 823.741325 181.95524 L 803.428825 181.95524 C 803.428825 181.95524 802.7257 187.17399 813.022575 188.17399 C 815.3507 191.189615 817.147575 192.04899 819.1632 192.83024 C 817.81945 193.939615 817.44445 195.29899 817.44445 197.11149 Z M 817.44445 197.11149" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.022575 192.689615 L 837.210075 192.627115" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.678825 181.95524 C 810.678825 181.95524 812.13195 190.470865 818.8507 192.48649" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="url(#o)" stroke="#000" stroke-width=".944" d="M 809.553825 181.845865 C 810.2257 178.595865 811.460075 178.033365 812.803825 173.89274 C 813.0382 169.86149 809.553825 170.29899 810.56945 167.720865 C 812.366325 164.92399 811.460075 162.23649 808.1007 160.11149 C 808.772575 163.814615 803.741325 167.283365 803.741325 170.29899 C 803.741325 173.33024 806.31945 172.658365 805.9757 177.252115 C 806.19445 179.939615 805.303825 179.26774 805.085075 181.845865 Z M 809.553825 181.845865" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.491325 161.658365 C 831.491325 163.08024 830.335075 164.23649 828.9132 164.23649 C 827.491325 164.23649 826.3507 163.08024 826.3507 161.658365 C 826.3507 160.23649 827.491325 159.08024 828.9132 159.08024 C 830.335075 159.08024 831.491325 160.23649 831.491325 161.658365 Z M 831.491325 161.658365" transform="matrix(.25 0 0 .25 36.178 41.05)"/>
  <path fill="none" stroke="#ffc72c" stroke-width="1.887" d="M 814.798575 181.83624 L 828.892325 161.133115 L 843.048575 181.86749" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.72045 174.08624 C 831.72045 175.633115 830.47045 176.883115 828.923575 176.883115 C 827.3767 176.883115 826.1267 175.633115 826.1267 174.08624 C 826.1267 172.539365 827.3767 171.289365 828.923575 171.289365 C 830.47045 171.289365 831.72045 172.539365 831.72045 174.08624 Z M 831.72045 174.08624" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 817.4392 197.11749 L 839.298575 197.17999 C 839.298575 197.17999 839.642325 194.351865 837.017325 192.61749 C 848.454825 191.05499 845.486075 180.945615 855.0642 180.383115 C 856.923575 180.664365 850.0642 184.664365 850.0642 184.664365 C 850.0642 184.664365 844.28295 188.758115 846.923575 190.820615 C 849.0017 192.476865 849.923575 189.820615 850.204825 187.820615 C 850.486075 185.820615 859.5017 184.52374 858.22045 178.80499 C 856.079825 174.08624 843.486075 181.96124 843.486075 181.96124 L 834.5017 181.89874 C 833.9392 180.89874 831.47045 176.851865 828.90795 176.83624 C 825.8767 176.945615 823.736075 181.96124 823.736075 181.96124 L 803.423575 181.96124 C 803.423575 181.96124 802.72045 187.164365 813.03295 188.164365 C 815.34545 191.195615 817.142325 192.039365 819.15795 192.820615 C 817.8142 193.945615 817.4392 195.30499 817.4392 197.11749 Z M 817.4392 197.11749" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 819.017325 192.67999 L 837.22045 192.61749" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 810.673575 181.945615 C 810.673575 181.945615 812.142325 190.46124 818.861075 192.476865" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="url(#p)" stroke="#000" stroke-width=".944" d="M 809.5642 181.83624 C 810.236075 178.58624 811.47045 178.039365 812.8142 173.883115 C 813.03295 169.851865 809.5642 170.30499 810.5642 167.726865 C 812.361075 164.92999 811.47045 162.24249 808.111075 160.11749 C 808.78295 163.80499 803.736075 167.27374 803.736075 170.30499 C 803.736075 173.33624 806.3142 172.64874 805.97045 177.24249 C 806.204825 179.92999 805.298575 179.258115 805.079825 181.83624 Z M 809.5642 181.83624" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 831.5017 161.664365 C 831.5017 163.08624 830.34545 164.226865 828.923575 164.226865 C 827.5017 164.226865 826.34545 163.08624 826.34545 161.664365 C 826.34545 160.226865 827.5017 159.08624 828.923575 159.08624 C 830.34545 159.08624 831.5017 160.226865 831.5017 161.664365 Z M 831.5017 161.664365" transform="matrix(.25 0 0 .25 36.234 51.299)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 915.84375 406.3125 C 915.84375 406.3125 921.546875 419.46875 928.046875 411.390625 C 934.53125 403.3125 932.15625 399.828125 932.15625 399.828125 L 917.578125 391.90625 L 913.3125 400.921875 Z M 915.84375 406.3125" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 928.359375 404.578125 C 928.359375 404.578125 929.3125 404.734375 930.09375 403.3125 C 930.890625 401.875 928.359375 401.25 927.25 399.65625 L 925.984375 402.203125 Z M 928.359375 404.578125" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 895.40625 401.09375 L 882.578125 408.0625 C 882.578125 408.0625 876.25 409.328125 875.765625 408.0625 C 875.296875 406.796875 875.921875 405.6875 879.25 405.515625 C 882.578125 405.359375 891.609375 397.125 891.609375 397.125 Z M 895.40625 401.09375" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 895.5625 173.15625 C 895.5625 173.15625 895.890625 176.15625 896.046875 177.75 C 896.203125 179.328125 893.515625 182.65625 893.34375 182.5 C 893.1875 182.34375 891.921875 182.65625 892.078125 183.609375 C 892.234375 184.546875 894.140625 184.875 894.140625 184.875 C 894.140625 184.875 893.34375 188.203125 894.140625 188.359375 C 894.9375 188.515625 892.078125 192.625 894.140625 193.734375 C 896.203125 194.84375 899.6875 196.28125 901.265625 195.953125 C 902.859375 195.640625 901.265625 202.140625 901.265625 202.140625 L 896.828125 211.640625 L 921.234375 209.109375 L 916.15625 201.03125 C 916.15625 201.03125 913.78125 199.4375 914.421875 194.84375 C 915.046875 190.25 914.09375 169.5 914.09375 169.5 L 896.671875 167.125 Z M 895.5625 173.15625" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 891.125 209.265625 C 891.125 209.265625 883.21875 212.90625 883.53125 222.734375 C 881.46875 232.234375 880.359375 241.734375 880.359375 241.734375 C 880.359375 241.734375 871.015625 252.34375 868.15625 256.15625 C 865.3125 259.953125 861.03125 267.71875 859.453125 269.765625 C 857.875 271.828125 851.6875 278.640625 851.84375 281.171875 C 852 283.71875 850.421875 294.953125 856.59375 296.21875 C 858.1875 296.859375 863.25 283.234375 863.25 283.234375 C 863.25 283.234375 863.5625 277.375 861.828125 276.265625 C 860.078125 275.15625 865.625 271.359375 865.625 271.359375 C 865.625 271.359375 876.25 263.59375 878.625 261.6875 C 881 259.796875 887.484375 252.5 887.484375 252.5 Z M 891.125 209.265625" transform="scale(.25)"/>
  <path fill="#fff" stroke="#000" stroke-width=".944" d="M 900.15625 201.8125 C 900.15625 201.8125 902.21875 207.359375 906.8125 206.40625 C 911.40625 205.46875 916.796875 201.1875 916.796875 201.1875 C 916.796875 201.1875 921.0625 201.03125 921.703125 201.65625 C 922.34375 202.296875 933.265625 212.90625 932.953125 216.234375 C 932.640625 219.5625 927.875 218.609375 926.140625 220.828125 C 924.390625 223.046875 921.546875 228.59375 922.34375 232.703125 C 923.125 236.828125 925.5 242.21875 925.1875 244.265625 C 924.875 246.328125 923.125 246.96875 923.125 248.078125 C 923.125 249.1875 924.5625 251.078125 924.5625 253.140625 C 924.5625 255.203125 922.65625 258.203125 922.96875 260.265625 C 923.28125 262.328125 923.453125 268.34375 923.453125 268.34375 L 922.96875 296.234375 C 922.96875 296.234375 924.5625 297.171875 924.71875 298.765625 C 924.875 300.34375 935.484375 346.4375 935.484375 346.4375 C 935.484375 346.4375 935.015625 347.859375 933.90625 347.703125 C 932.796875 347.546875 938.171875 354.828125 938.328125 356.890625 C 938.5 358.953125 943.875 375.109375 943.71875 377.328125 C 943.5625 379.546875 942.765625 384.453125 942.296875 384.609375 C 941.828125 384.765625 945.78125 394.75 945.140625 396.328125 C 944.515625 397.921875 938.015625 397.765625 938.015625 397.765625 L 936.28125 397.4375 C 936.28125 397.4375 936.4375 399.5 935.171875 399.65625 C 933.90625 399.828125 924.5625 399.1875 924.5625 399.1875 C 924.5625 399.1875 921.859375 403.3125 920.28125 403.140625 C 918.6875 402.984375 916.640625 400.140625 916.15625 400.609375 C 915.6875 401.09375 917.578125 403.78125 917.109375 404.578125 C 916.640625 405.359375 908.5625 407.109375 906.96875 403.3125 C 905.390625 399.5 907.921875 400.453125 907.453125 399.65625 C 906.96875 398.875 903.328125 396.8125 902.21875 397.4375 C 901.109375 398.078125 905.078125 399.03125 904.90625 400.609375 C 904.75 402.203125 901.421875 404.578125 900.15625 404.578125 C 898.890625 404.578125 895.890625 398.71875 891.453125 399.34375 C 887.015625 399.984375 884.15625 401.09375 884.15625 401.09375 C 884.15625 401.09375 878.9375 403.3125 876.71875 402.828125 C 874.5 402.359375 873.546875 400.609375 873.546875 399.65625 C 873.546875 398.71875 875.140625 394.59375 874.96875 393.328125 C 874.8125 392.0625 873.546875 390.796875 873.546875 388.890625 C 873.546875 386.984375 877.1875 380.5 877.1875 380.5 L 877.03125 351.34375 C 877.03125 351.34375 873.703125 351.34375 873.546875 349.296875 C 873.390625 347.234375 878.625 303.203125 879.40625 300.34375 C 880.203125 297.5 882.265625 287.359375 882.265625 287.359375 C 882.265625 287.359375 879.890625 288.46875 879.734375 287.359375 C 879.5625 286.25 886.859375 261.0625 886.859375 261.0625 C 886.859375 261.0625 888.125 248.546875 888.125 245.21875 C 888.125 241.890625 887.484375 237.296875 887.484375 237.296875 C 887.484375 237.296875 880.984375 234.546875 880.84375 230.328125 C 880.234375 223.625 887.015625 219.875 887.8125 217.65625 C 888.59375 215.4375 890.96875 208.796875 890.96875 208.796875 C 890.96875 208.796875 894.453125 202.765625 900.15625 201.8125 Z M 900.15625 201.8125" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width=".944" d="M 897.78125 402.984375 C 897.78125 402.984375 882.421875 411.0625 880.203125 411.703125 C 877.984375 412.328125 876.5625 409 878.78125 408.375 C 881 407.734375 884.484375 407.421875 884.484375 407.421875 C 884.484375 407.421875 879.25 403.3125 879.40625 403.140625 C 879.5625 402.984375 886.53125 400.453125 886.703125 400.453125 C 886.859375 400.453125 888.921875 404.578125 890.34375 404.25 C 891.765625 403.9375 895.890625 400.765625 895.890625 400.765625 C 895.890625 400.765625 898.109375 403.3125 897.78125 402.984375 Z M 897.78125 402.984375" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 919.4375 407.421875 C 920.890625 409.296875 921.453125 412.546875 924.78125 410.640625 C 928.109375 408.75 923.078125 405.21875 923.078125 405.21875 Z M 919.4375 407.421875" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 925.5 407.109375 C 925.5 407.109375 927.09375 408.375 928.515625 406.953125 C 929.9375 405.515625 925.65625 402.515625 925.65625 402.515625 L 923.609375 404.890625 Z M 925.5 407.109375" transform="scale(.25)"/>
  <path fill="#ffc6b5" stroke="#000" stroke-width=".944" d="M 932.3125 216.953125 C 932.46875 216.953125 937.546875 232.640625 938.171875 236.609375 C 938.8125 240.5625 940.875 256.40625 940.078125 258.625 C 939.28125 260.84375 931.203125 271.453125 930.25 274.140625 C 929.3125 276.828125 923.609375 287.125 923.609375 287.125 C 923.609375 287.125 922.171875 297.265625 921.546875 297.75 C 920.90625 298.21875 923.1875 300.703125 922.96875 301.546875 C 922.65625 302.484375 918.375 306.9375 916.484375 306.453125 C 914.578125 305.984375 911.5625 303.765625 911.40625 301.703125 C 911.25 299.640625 911.5625 292.828125 912.984375 291.09375 C 914.421875 289.34375 921.859375 271.765625 922.34375 270.65625 C 922.8125 269.546875 929.15625 255.765625 929.3125 253.390625 C 929.46875 251.015625 927.34375 245.515625 925.09375 243.5 C 920.109375 228.828125 922.0625 219.984375 932.3125 216.953125 Z M 932.3125 216.953125" transform="scale(.25)"/>
  <path fill="#9c5100" stroke="#000" stroke-width=".944" d="M 895.09375 172.203125 C 895.09375 172.203125 898.578125 172.671875 900.484375 171.5625 C 902.375 170.453125 904.59375 169.984375 906.1875 172.203125 C 907.765625 174.421875 908.875 174.265625 908.875 174.265625 C 908.875 174.265625 906.5 180.125 908.875 180.75 C 911.25 181.390625 912.359375 181.390625 912.515625 182.171875 C 912.671875 182.96875 910.453125 184.71875 911.09375 185.5 C 911.71875 186.296875 912.828125 187.25 912.984375 187.875 C 913.15625 188.515625 911.5625 191.203125 912.046875 191.84375 C 912.515625 192.46875 913.9375 195.015625 914.890625 195.015625 C 915.84375 195.015625 915.203125 198.96875 918.0625 198.015625 C 920.90625 197.0625 920.75 194.53125 920.75 194.53125 C 920.75 194.53125 923.765625 194.0625 924.546875 191.359375 C 925.34375 188.671875 927.25 188.03125 927.25 188.03125 C 927.25 188.03125 931.046875 185.984375 925.984375 182.8125 C 925.984375 160.640625 911.40625 163.015625 911.40625 163.015625 C 911.40625 163.015625 909.671875 159.046875 906.8125 159.53125 C 903.96875 160 903.796875 163.328125 901.75 163.015625 C 899.6875 162.6875 899.203125 161.265625 899.046875 161.421875 C 898.890625 161.59375 897.15625 164.75 897.15625 165.546875 C 897.15625 166.34375 891.609375 164.4375 892.078125 168.5625 C 892.5625 172.671875 895.25 172.515625 895.09375 172.203125 Z M 895.09375 172.203125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 920.75 208 C 920.75 208 906.65625 217.03125 906.96875 220.1875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 923.921875 209.578125 C 923.921875 209.578125 920.75 213.0625 920.59375 213.0625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 928.203125 213.390625 C 928.203125 213.390625 921.234375 219.078125 922.34375 222.734375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 895.25 207.203125 C 895.25 207.203125 893.1875 211.484375 893.671875 212.90625 C 894.140625 214.328125 897.625 219.71875 898.109375 223.046875 C 898.578125 226.375 898.109375 228.75 898.109375 228.75" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 891.609375 215.125 C 891.609375 215.125 892.234375 220.1875 893.1875 221.140625 C 894.140625 222.09375 896.671875 226.375 897 228.109375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 890.34375 236.1875 C 890.34375 236.1875 894.453125 238.25 898.265625 230.328125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 904.4375 224.78125 C 904.28125 224.78125 901.421875 232.546875 906.5 235.40625 C 911.5625 238.25 915.375 237.9375 917.578125 237.140625 C 919.796875 236.34375 922.171875 234.921875 922.171875 234.921875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 902.21875 234.296875 C 902.21875 234.296875 902.53125 244.75 917.90625 255.046875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 902.859375 244.109375 C 902.859375 244.109375 902.703125 253.296875 908.71875 257.421875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 899.6875 233.34375 C 899.6875 233.34375 895.09375 246.96875 891.453125 248.390625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 898.265625 243.3125 C 898.265625 243.3125 898.109375 253.140625 896.828125 256.625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 895.5625 259 C 895.5625 259 898.734375 262.96875 902.21875 262.640625 C 905.703125 262.328125 907.125 258.203125 909.5 258.84375 C 911.890625 259.484375 914.09375 261.375 919.640625 260.90625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 911.71875 265.015625 C 911.71875 265.015625 911.71875 273.09375 913.15625 273.890625 C 914.578125 274.6875 913.9375 282.125 913.9375 282.125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 892.40625 261.21875 C 892.40625 261.21875 892.234375 268.828125 891.296875 271.515625 C 890.34375 274.203125 888.4375 278.796875 888.75 282.765625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 882.734375 287.203125 C 883.53125 286.875 886.21875 284.5 886.21875 284.5" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 887.640625 286.25 C 887.640625 286.25 880.84375 315.390625 882.734375 332.8125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 889.234375 287.671875 C 889.234375 287.671875 885.75 309.53125 887.328125 313.65625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 887.484375 287.03125 C 887.640625 287.03125 900.796875 287.984375 900.796875 287.984375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 902.703125 286.25 C 902.703125 286.25 906.34375 288.140625 911.40625 287.828125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 901.109375 296.859375 C 901.109375 296.859375 900.484375 334.25 899.53125 342.484375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 920.59375 305.40625 C 920.59375 305.40625 924.71875 338.203125 927.09375 341.21875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 912.671875 309.53125 C 912.671875 309.53125 915.203125 338.515625 916.640625 341.046875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 876.71875 351.1875 C 876.71875 351.1875 881.625 349.609375 886.0625 344.859375 C 891.125 351.671875 898.890625 345.171875 898.890625 345.171875 C 898.890625 345.171875 910.9375 353.40625 916.3125 344.21875 C 924.5625 349.609375 928.671875 343.421875 928.671875 343.421875 C 928.671875 343.421875 931.6875 348.015625 933.90625 347.546875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 920.75 350.71875 C 920.75 350.71875 926.9375 379.703125 936.125 387.9375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 888.921875 347.703125 C 888.921875 347.703125 889.703125 371.9375 891.125 389.046875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 887.8125 375.109375 C 887.8125 375.109375 887.015625 390.953125 886.0625 392.0625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 878.453125 393.640625 C 878.453125 393.640625 880.203125 400.453125 888.921875 394.125 C 897.625 387.78125 897.78125 396.5 898.109375 397.4375 C 898.421875 398.390625 899.84375 405.203125 903.171875 399.5" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 910.140625 390.15625 C 910.140625 390.15625 908.71875 404.25 921.234375 393.953125 C 933.75 383.65625 935.796875 393.796875 936.125 396.96875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 910.296875 166.8125 C 910.296875 166.8125 909.1875 173.78125 916.640625 173.15625 C 915.6875 176.953125 918.53125 178.21875 918.53125 178.21875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 924.078125 182.5 C 924.234375 182.5 927.40625 184.546875 923.921875 187.09375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 913.3125 188.03125 C 913.3125 188.03125 914.890625 189.9375 916.640625 189.46875 C 918.375 188.984375 921.234375 191.203125 921.234375 191.203125 C 921.234375 191.203125 923.609375 192 923.921875 191.53125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 903.640625 194.0625 C 903.640625 194.0625 909.03125 196.125 912.515625 187.5625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 893.671875 185.03125 L 896.828125 185.1875" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 858.515625 284.921875 L 858.765625 291.859375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width=".944" d="M 852.015625 284.40625 C 852.015625 284.40625 856.46875 291.859375 856.09375 295.8125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-linejoin="round" stroke-width=".944" d="M 894.140625 188.203125 L 897 188.203125 L 894.453125 189.3125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-linejoin="round" stroke-width=".944" d="M 911.671875 299.96875 C 911.671875 299.96875 915 299.609375 914.890625 305.171875 C 917.015625 298.265625 921.328125 298.109375 921.328125 298.109375" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-linejoin="round" stroke-width="1.699" d="M 898.109375 179.328125 C 898.578125 179.328125 900.3125 178.6875 900.640625 179.171875 C 900.953125 179.640625 898.734375 179.953125 898.109375 179.328125 Z M 898.109375 179.328125" transform="scale(.25)"/>
  <path fill="#ffc72c" stroke="#000" stroke-width="1.887" d="M 900.296875 458.578125 C 967.203125 456.71875 1010.40625 408.40625 1009.9375 407.9375 C 1009.46875 407.484375 1018.765625 394.46875 1026.203125 396.328125 C 1033.625 398.1875 1044.3125 420.015625 1057.328125 424.671875 C 1063.828125 434.890625 1055.46875 444.171875 1052.671875 446.03125 C 1049.890625 447.890625 1037.34375 453 1035.484375 445.578125 C 1033.625 438.140625 1029.90625 439.53125 1029.90625 439.53125 C 1029.90625 439.53125 970.453125 497.609375 901.6875 495.28125 C 830.609375 495.75 772.078125 439.53125 772.078125 439.53125 L 766.96875 445.109375 C 766.96875 445.109375 761.390625 451.140625 758.609375 450.6875 C 755.8125 450.21875 743.734375 442.3125 742.8125 434.421875 C 741.875 426.53125 750.25 421.40625 750.25 421.40625 C 750.25 421.40625 770.6875 405.625 773.015625 397.25 C 777.65625 392.609375 786.484375 400.515625 786.484375 400.515625 C 786.484375 400.515625 840.375 462.296875 900.296875 458.578125 Z M 900.296875 458.578125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width="1.887" d="M 748.859375 422.640625 C 748.859375 422.640625 754.390625 421.171875 756.40625 423.46875 C 758.4375 425.78125 772.34375 439.40625 772.34375 439.40625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width="1.887" d="M 761.484375 429.09375 L 755.953125 433.234375 C 755.953125 433.234375 769.859375 436 766.734375 445.125" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width="1.887" d="M 1052.34375 422.1875 C 1052.34375 422.1875 1049.578125 420.703125 1045.25 423.9375 C 1040.921875 427.15625 1029.953125 439.40625 1029.953125 439.40625" transform="scale(.25)"/>
  <path fill="none" stroke="#000" stroke-width="1.887" d="M 1039.90625 428.734375 L 1045.890625 433.328125 C 1045.890625 433.328125 1033.359375 434.15625 1036.125 446.046875" transform="scale(.25)"/>
  <path d="M 204.914062 109.347656 C 204.824219 109.613281 204.585938 109.34375 204.386719 109.394531 C 203.863281 109.414062 203.429688 109.757812 202.980469 109.988281 C 201.445312 110.882812 199.910156 111.777344 198.378906 112.671875 C 198.222656 112.621094 198.261719 112.496094 198.296875 112.371094 C 198.707031 110.453125 199.132812 108.542969 199.527344 106.625 C 199.59375 106.289062 199.699219 105.871094 199.378906 105.628906 C 199.238281 105.53125 199.453125 105.34375 199.539062 105.515625 C 200.324219 106.074219 201.109375 106.632812 201.894531 107.195312 C 201.816406 107.414062 201.683594 107.257812 201.550781 107.167969 C 201.226562 106.855469 200.832031 107.144531 200.84375 107.542969 C 200.5625 108.804688 200.289062 110.070312 200.011719 111.332031 C 201.054688 110.714844 202.105469 110.113281 203.140625 109.480469 C 203.507812 109.316406 203.777344 108.820312 203.394531 108.515625 C 203.25 108.429688 203.011719 108.195312 203.25 108.160156 C 203.804688 108.558594 204.359375 108.953125 204.914062 109.347656 Z M 204.914062 109.347656"/>
  <path d="M 206.570312 117.34375 C 206.527344 117.488281 206.453125 117.519531 206.339844 117.417969 C 205.414062 116.914062 204.492188 116.40625 203.570312 115.902344 C 203.613281 115.761719 203.6875 115.726562 203.800781 115.832031 C 204.050781 116 204.457031 116.066406 204.640625 115.769531 C 205.007812 115.179688 205.316406 114.558594 205.65625 113.953125 C 206.117188 113.097656 206.597656 112.253906 207.046875 111.390625 C 207.257812 111.085938 207.019531 110.710938 206.722656 110.578125 C 206.621094 110.527344 206.472656 110.484375 206.597656 110.363281 C 206.660156 110.289062 206.792969 110.449219 206.882812 110.46875 C 207.765625 110.953125 208.652344 111.4375 209.535156 111.921875 C 209.492188 112.0625 209.417969 112.09375 209.304688 111.992188 C 209.054688 111.824219 208.644531 111.757812 208.460938 112.054688 C 208.09375 112.640625 207.785156 113.261719 207.445312 113.867188 C 206.984375 114.722656 206.503906 115.566406 206.054688 116.429688 C 205.839844 116.738281 206.078125 117.117188 206.382812 117.246094 C 206.445312 117.277344 206.507812 117.3125 206.570312 117.34375 Z M 206.570312 117.34375"/>
  <path d="M 217.222656 114.847656 C 216.96875 115.550781 216.71875 116.253906 216.46875 116.957031 C 216.160156 116.925781 216.367188 116.53125 216.269531 116.324219 C 216.160156 115.371094 215.460938 114.484375 214.503906 114.28125 C 213.773438 114.082031 213.003906 114.460938 212.578125 115.054688 C 211.929688 115.9375 211.535156 117.015625 211.480469 118.113281 C 211.441406 118.734375 211.730469 119.375 212.273438 119.703125 C 212.703125 119.984375 213.226562 120.097656 213.738281 120.085938 C 213.917969 119.554688 214.132812 119.03125 214.285156 118.488281 C 214.347656 118.136719 213.980469 117.925781 213.691406 117.84375 C 213.644531 117.742188 213.757812 117.617188 213.878906 117.726562 C 214.796875 118.054688 215.714844 118.382812 216.636719 118.710938 C 216.605469 119.046875 216.21875 118.683594 216.007812 118.816406 C 215.667969 118.949219 215.636719 119.355469 215.507812 119.652344 C 215.398438 119.96875 215.285156 120.28125 215.171875 120.597656 C 213.9375 120.683594 212.664062 120.421875 211.585938 119.800781 C 210.539062 119.140625 209.832031 117.867188 210.039062 116.621094 C 210.199219 115.550781 210.863281 114.550781 211.847656 114.078125 C 212.753906 113.597656 213.859375 113.597656 214.796875 113.980469 C 215.3125 114.152344 215.753906 114.472656 216.152344 114.832031 C 216.394531 115.15625 216.859375 115.160156 217.058594 114.789062 C 217.113281 114.808594 217.167969 114.828125 217.222656 114.847656"/>
  <path d="M 221.710938 122.050781 C 221.703125 122.164062 221.691406 122.265625 221.554688 122.203125 C 220.503906 122.042969 219.449219 121.882812 218.398438 121.722656 C 218.402344 121.613281 218.414062 121.507812 218.550781 121.570312 C 218.824219 121.628906 219.191406 121.621094 219.324219 121.328125 C 219.457031 120.90625 219.484375 120.457031 219.558594 120.023438 C 219.742188 118.808594 219.933594 117.597656 220.105469 116.382812 C 220.171875 116.121094 220.085938 115.824219 219.8125 115.722656 C 219.660156 115.648438 219.488281 115.636719 219.324219 115.609375 C 219.328125 115.5 219.339844 115.394531 219.476562 115.457031 C 220.527344 115.617188 221.582031 115.777344 222.636719 115.9375 C 222.628906 116.050781 222.617188 116.152344 222.484375 116.089844 C 222.207031 116.03125 221.839844 116.039062 221.703125 116.332031 C 221.570312 116.753906 221.542969 117.203125 221.46875 117.636719 C 221.285156 118.851562 221.09375 120.0625 220.921875 121.277344 C 220.855469 121.539062 220.945312 121.84375 221.21875 121.941406 C 221.371094 122.011719 221.542969 122.023438 221.710938 122.050781 Z M 221.710938 122.050781"/>
  <path d="M 230.238281 119.976562 C 230.195312 120.726562 230.15625 121.472656 230.113281 122.21875 C 228.234375 122.316406 226.355469 122.410156 224.476562 122.507812 C 224.441406 122.371094 224.480469 122.296875 224.628906 122.324219 C 224.910156 122.332031 225.277344 122.210938 225.3125 121.886719 C 225.339844 121.332031 225.273438 120.773438 225.257812 120.21875 C 225.195312 119.105469 225.152344 117.996094 225.078125 116.886719 C 225.109375 116.554688 224.8125 116.308594 224.5 116.320312 C 224.398438 116.269531 224.128906 116.425781 224.15625 116.242188 C 224.113281 116.097656 224.316406 116.175781 224.402344 116.144531 C 225.449219 116.09375 226.492188 116.039062 227.539062 115.984375 C 227.574219 116.125 227.535156 116.195312 227.386719 116.167969 C 227.097656 116.164062 226.71875 116.234375 226.640625 116.5625 C 226.59375 117.042969 226.660156 117.53125 226.675781 118.011719 C 226.738281 119.167969 226.78125 120.324219 226.859375 121.480469 C 226.835938 121.785156 227.078125 122.050781 227.390625 121.992188 C 227.847656 121.96875 228.316406 121.988281 228.761719 121.875 C 229.390625 121.707031 229.730469 121.085938 229.902344 120.507812 C 230.015625 120.351562 229.921875 119.960938 230.171875 119.980469 C 230.195312 119.980469 230.214844 119.980469 230.238281 119.976562"/>
  <path d="M 234.632812 114.554688 C 234.484375 114.558594 234.550781 114.746094 234.511719 114.847656 C 234.261719 116.71875 234.023438 118.589844 233.765625 120.457031 C 233.703125 120.871094 233.601562 121.351562 233.191406 121.5625 C 233.042969 121.617188 233.175781 121.851562 233.3125 121.722656 C 233.941406 121.53125 234.566406 121.34375 235.195312 121.15625 C 235.171875 120.839844 234.808594 121.128906 234.609375 121.070312 C 234.175781 121.070312 234.113281 120.546875 234.171875 120.21875 C 234.199219 119.996094 234.222656 119.777344 234.25 119.554688 C 234.96875 119.339844 235.6875 119.121094 236.40625 118.90625 C 236.679688 119.253906 236.984375 119.578125 237.234375 119.945312 C 237.421875 120.296875 236.964844 120.457031 236.703125 120.527344 C 236.578125 120.539062 236.6875 120.796875 236.800781 120.675781 C 237.742188 120.394531 238.6875 120.109375 239.632812 119.828125 C 239.625 119.507812 239.214844 119.808594 239.039062 119.59375 C 238.511719 119.195312 238.128906 118.640625 237.691406 118.148438 C 236.671875 116.953125 235.652344 115.753906 234.632812 114.554688 Z M 234.617188 116.796875 C 235.128906 117.40625 235.636719 118.015625 236.148438 118.625 C 235.535156 118.808594 234.925781 118.988281 234.3125 119.171875 C 234.414062 118.378906 234.515625 117.589844 234.617188 116.796875 Z M 234.617188 116.796875"/>
  <path d="M 244.273438 110.34375 C 244.546875 110.851562 244.816406 111.355469 245.089844 111.859375 C 244.835938 112.078125 244.703125 111.574219 244.476562 111.496094 C 244.097656 111.179688 243.53125 111.117188 243.109375 111.386719 C 242.9375 111.476562 242.769531 111.570312 242.597656 111.660156 C 243.417969 113.179688 244.226562 114.703125 245.050781 116.214844 C 245.179688 116.601562 245.65625 116.675781 245.964844 116.457031 C 246.082031 116.371094 246.191406 116.320312 246.230469 116.503906 C 245.8125 116.746094 245.359375 116.972656 244.929688 117.210938 C 244.386719 117.5 243.84375 117.792969 243.300781 118.085938 C 243.171875 117.953125 243.257812 117.882812 243.402344 117.832031 C 243.789062 117.691406 243.9375 117.222656 243.679688 116.894531 C 242.875 115.386719 242.0625 113.886719 241.257812 112.382812 C 240.808594 112.632812 240.234375 112.855469 240.121094 113.414062 C 239.992188 113.769531 240.179688 114.199219 240.179688 114.5 C 240.042969 114.640625 240.007812 114.445312 239.941406 114.34375 C 239.710938 113.910156 239.476562 113.480469 239.246094 113.046875 C 240.921875 112.148438 242.597656 111.246094 244.273438 110.34375 Z M 244.273438 110.34375"/>
  <path d="M 248.085938 108.085938 C 248.605469 108.773438 249.125 109.460938 249.644531 110.148438 C 250.007812 109.921875 250.308594 109.511719 250.199219 109.0625 C 250.242188 108.710938 249.742188 108.316406 249.914062 108.070312 C 250.027344 108 250.09375 108.261719 250.1875 108.332031 C 250.78125 109.117188 251.375 109.902344 251.96875 110.6875 C 251.734375 110.953125 251.566406 110.414062 251.320312 110.355469 C 250.972656 110.109375 250.488281 109.988281 250.117188 110.257812 C 249.839844 110.347656 249.851562 110.5 250.039062 110.671875 C 250.457031 111.210938 250.84375 111.773438 251.289062 112.292969 C 251.5625 112.554688 251.902344 112.28125 252.132812 112.09375 C 252.730469 111.6875 253.253906 111.054688 253.222656 110.292969 C 253.261719 109.960938 253.054688 109.589844 253.058594 109.308594 C 253.191406 109.132812 253.226562 109.40625 253.304688 109.496094 C 253.585938 110.003906 253.867188 110.511719 254.148438 111.019531 C 252.683594 112.121094 251.222656 113.222656 249.761719 114.328125 C 249.570312 114.191406 249.757812 114.113281 249.878906 114.019531 C 250.285156 113.765625 250.15625 113.25 249.855469 112.964844 C 248.917969 111.730469 247.996094 110.484375 247.050781 109.257812 C 246.804688 108.929688 246.359375 109.105469 246.113281 109.332031 C 246.011719 109.476562 245.851562 109.1875 246.042969 109.167969 C 247.421875 108.128906 248.800781 107.085938 250.179688 106.046875 C 250.558594 106.546875 250.933594 107.046875 251.3125 107.546875 C 251.050781 107.8125 250.816406 107.222656 250.519531 107.191406 C 249.976562 106.90625 249.328125 107.136719 248.878906 107.492188 C 248.613281 107.683594 248.351562 107.886719 248.085938 108.085938"/>
</svg>
