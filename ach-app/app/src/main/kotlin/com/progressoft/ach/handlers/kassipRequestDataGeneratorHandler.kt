package com.progressoft.ach.handlers

import com.progressoft.ach.entities.ATSKassipTimetableReq
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import org.springframework.stereotype.Component
import java.sql.Timestamp
import java.util.UUID

@Component
class KassipRequestDataGeneratorHandler : WfChangeHandler<ATSKassipTimetableReq>() {

    override fun handle(context: WfChangeHandlerContext<ATSKassipTimetableReq>?) {
        val entity = context?.entity ?: return
        if (!entity.requestId.isNullOrEmpty()) {
            context.setEnabled(false, "requestId", "creationDate")
            return
        }
        entity.requestId = UUID.randomUUID().toString().substring(0, 16)
        entity.creationDate = Timestamp(System.currentTimeMillis())

        context.setEnabled(false, "requestId", "creationDate")
    }
}