package com.progressoft.ach.reportgenerator

import com.progressoft.ach.entities.ATSIncomingTransactionReport
import com.progressoft.ach.entities.ATSPRT_Participant
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import java.text.SimpleDateFormat

@Component
class GenerateIncomingTransactionReport(@Value("\${reporting.base.url}") reportingBaseUrl: String?,
                                        restTemplate: RestTemplate
) :
    AbstractReportGenerator<ATSIncomingTransactionReport?>(restTemplate, reportingBaseUrl!!)  {

    override val errorMsgLabel: String
        get() = "incoming.transaction.report.failed.generation"
    override fun getReportType(entity: ATSIncomingTransactionReport?): String? {
        return entity?.reportFormat
    }

    override fun getReportName(entity: ATSIncomingTransactionReport?)  = "incoming_transaction_report"

    override fun getRequestContent(entity: ATSIncomingTransactionReport?): MutableMap<String, String?> {
        val formatter = SimpleDateFormat("yyyy-MM-dd")
        val parameters: MutableMap<String, String?> = HashMap()
        parameters["sessionSequence"] = if (entity?.sessionSeq == null) null else entity.sessionSeq!!.sessionSeq
        parameters["sessionDate"] = if (entity?.sessionDate == null) null else formatter.format(entity.sessionDate)
        parameters["participantCode"] =
            if (entity?.participants.isNullOrEmpty()) null else getParticipantsCode(entity?.participants!!)
        return parameters
    }


    private fun getParticipantsCode(participants: MutableList<ATSPRT_Participant>): String {
        return participants.joinToString(",") { it.code }
    }

}