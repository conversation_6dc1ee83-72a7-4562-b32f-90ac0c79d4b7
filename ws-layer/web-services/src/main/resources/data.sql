
INSERT INTO COMMEndPOINTS (ID, Z_ORG_ID, Z_TENANT_ID, NAME, DIRECTION, SYSTEM, URI, DEADLETTERURI, ACCEPTEDMESSAGETYPES, ACCEPTEDTENANTS, REJECTEDMESSAGETYPES, RE<PERSON>ECTEDTENANTS) VALUES (1, 0, 'SY<PERSON><PERSON>', 'Default In Endpoint', 'In', 'Ach', 'AchAmq:queue:ach_in', 'AchAmq:queue:ach_in_dlq', '', '', '', '');
INSERT INTO COMMEndPOINTS (ID, Z_ORG_ID, Z_TENANT_ID, NAME, DIRECTION, SYSTEM, URI, DEADLETTERURI, ACCEPTEDMESSAGETYPES, ACCEPTEDTENANTS, REJECTEDMESSAGETYPES, REJECTEDTENANTS) VALUES (2, 0, 'SYSTEM', 'Default Out Endpoint', 'Out', 'Psys', 'PsysAmq:queue:psys_in', '', '', '', '', '');
INSERT INTO ATSPRT_INSTITUTIONS (ID,CODE,DESC<PERSON>PTION,NAME) VALUES
                                                               (1,'1','Central Bank','Central Bank'),
                                                               (2,'2','Commercial Bank','Commercial Bank'),
                                                               (3,'3','Clearing System','Clearing System'),
                                                               (4,'4','Governmental Agency','Governmental Agency'),
                                                               (5,'5','Central Securities Depository','CSD'),
                                                               (6,'6','RTGS System','RTGS'),
                                                               (7,'7','Payment Service Provider','PSP'),
                                                               (8,'8','Biller','Biller');

INSERT INTO ATSPRT_LimitsProfile (ID,Z_ARCHIVE_ON,Z_ARCHIVE_QUEUED,Z_ARCHIVE_STATUS,Z_ASSIGNED_GROUP,Z_ASSIGNED_USER,Z_CREATED_BY,Z_CREATION_DATE,Z_DELETED_BY,Z_DELETED_FLAG,Z_DELETED_ON,Z_EDITABLE,Z_LOCKED_BY,Z_LOCKED_UNTIL,Z_ORG_ID,Z_TENANT_ID,Z_UPDATED_BY,Z_UPDATING_DATE,Z_WORKFLOW_ID,Z_WS_TOKEN,Z_DRAFT_STATUS,CODE,DESCRIPTION,NAME,CBATCHCOUNT,CTRANSPERBATCH,CTXLIMIT,DBATCHCOUNT,DTRANSPERBATCH,DTXLIMIT,LIMITSPER,Z_DRAFT_ID,Z_STATUS_ID) VALUES (1,NULL,NULL,NULL,NULL,NULL,'SERVICE_USER',TIMESTAMP'2021-10-13 07:39:04.212',NULL,0,NULL,0,NULL,NULL,2921218077316,'ATS','SERVICE_USER',TIMESTAMP'2021-10-13 07:39:04.391',2921218078843,NULL,'COMMITED','1','No Limits','Default',0,100,0,0,100,0,'Business Day',NULL,100025410);

INSERT INTO ATSPRT_TYPES (ID,CODE,DESCRIPTION,NAME) VALUES (1,'1','Direct Participant','Direct');
INSERT INTO ATSPRT_TYPES (ID,CODE,DESCRIPTION,NAME) VALUES (2,'2','Indirect Participant','Indirect');
INSERT INTO ATSPRT_TYPES (ID,CODE,DESCRIPTION,NAME) VALUES (3,'3','Subdirect Participant','Subdirect');
INSERT INTO ATSPRT_TYPES (ID,CODE,DESCRIPTION,NAME) VALUES (4,'4','Technical Participant','Technical');


INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (1, 0, 'SYSTEM', 'QNBAQAQAXXX', 'Kuwait National Bank', 'QNB', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'QNBM', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (2, 0, 'SYSTEM', 'CBQAQAQAXXX', 'Commercial Bank Of Qatar', 'CBQ', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'CBQM', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (3, 0, 'SYSTEM', 'DOHBQAQAXXX', 'Doha Bank', 'DOHA', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'DOHA', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (8, 0, 'SYSTEM', 'MSHQQAQAXXX', 'Mashreq Bank', 'MSHB', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'MSHB', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (16, 0, 'SYSTEM', 'QCBCQAQXXXX', 'Qatar Central Bank', 'QCB', 0, 0, 0, 0, 0, 1, 1, 1, 35, 'QCBM', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (30, 0, 'SYSTEM', 'QIDBQAQAXXX', 'Qatar Development Bank', 'QIDB', 0, 0, 0, 0, 0, 2, 1, 1, 35, 'QIDB', 'MX', '001', 1);
INSERT INTO ATSPRT_PARTICIPANTS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, CHRGEXCLUSIONFLAG, DEFAULTFLAG, RECEIVESUSPFLAG, SENDSUSPFLAG, TERMINATEFLAG, INSTITUTIONTYPEID, LIMITSPROFILEID, PARTICIPANTTYPEID, ACCOUNTNUMBERLENGTH, PREFIX, FORMAT, HEADOFFICEBRANCHCODE, FUTUREDATEDPAYMENTALLOWED) VALUES (999, 0, 'SYSTEM', 'QCHCQAQXXXX', 'Qatar Clearing House', 'QCH', 0, 0, 0, 0, 0, 3, 1, 1, 35, 'QCHM', 'MX', '001', 1);


INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (1, 0, 'SYSTEM', 'QNB Account', '1', '***********************', ********, 0, 95, -********, 0, 95, 1, 414, 1);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (2, 0, 'SYSTEM', 'CBK Account', '2', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 2);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (3, 0, 'SYSTEM', 'DOH Account', '3', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 3);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (4, 0, 'SYSTEM', 'MSH Account', '4', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 4);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (5, 0, 'SYSTEM', 'QNB Account', '100', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 5);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (6, 0, 'SYSTEM', 'UNB Account', '5', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 6);
INSERT INTO ATSACC_ACCOUNTS (ID, Z_ORG_ID, Z_TENANT_ID, ACCNAME, ACCNUMBER, IBAN, CREDITCAP, CREDITSUSPFLAG, CREDITWATERMARK, DEBITCAP, DEBITSUSPFLAG, DEBITWATERMARK, DEFAULTACCFLAG, CURRENCYID, PARTICIPANTID) VALUES (7, 0, 'SYSTEM', 'ICB Account', '200', '***********************' ,********, 0, 95, -********, 0, 95, 1, 414, 999);

INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (1, 0, 'SYSTEM', 1, 1, 1, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (2, 0, 'SYSTEM', 1, 1, 2, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (3, 0, 'SYSTEM', 1, 1, 3, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (4, 0, 'SYSTEM', 1, 1, 4, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (5, 0, 'SYSTEM', 1, 1, 5, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (6, 0, 'SYSTEM', 1, 1, 6, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (7, 0, 'SYSTEM', 1, 1, 7, 1);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (8, 0, 'SYSTEM', 1, 1, 1, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (9, 0, 'SYSTEM', 1, 1, 2, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (10, 0, 'SYSTEM', 1, 1, 3, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (11, 0, 'SYSTEM', 1, 1, 4, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (12, 0, 'SYSTEM', 1, 1, 5, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (13, 0, 'SYSTEM', 1, 1, 6, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (14, 0, 'SYSTEM', 1, 1, 7, 2);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (15, 0, 'SYSTEM', 1, 1, 1, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (16, 0, 'SYSTEM', 1, 1, 2, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (17, 0, 'SYSTEM', 1, 1, 3, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (18, 0, 'SYSTEM', 1, 1, 4, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (19, 0, 'SYSTEM', 1, 1, 5, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (20, 0, 'SYSTEM', 1, 1, 6, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (21, 0, 'SYSTEM', 1, 1, 7, 3);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (22, 0, 'SYSTEM', 1, 1, 1, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (23, 0, 'SYSTEM', 1, 1, 2, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (24, 0, 'SYSTEM', 1, 1, 3, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (25, 0, 'SYSTEM', 1, 1, 4, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (26, 0, 'SYSTEM', 1, 1, 5, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (27, 0, 'SYSTEM', 1, 1, 6, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (28, 0, 'SYSTEM', 1, 1, 7, 8);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (29, 0, 'SYSTEM', 1, 1, 1, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (30, 0, 'SYSTEM', 1, 1, 2, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (31, 0, 'SYSTEM', 1, 1, 3, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (32, 0, 'SYSTEM', 1, 1, 4, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (33, 0, 'SYSTEM', 1, 1, 5, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (34, 0, 'SYSTEM', 1, 1, 6, 19);
INSERT INTO ATSPRT_MESSAGES (ID, Z_ORG_ID, Z_TENANT_ID, INWARDFLAG, OUTWARDFLAG, MSGTYPEID, REFPARTICIPANTID) VALUES (35, 0, 'SYSTEM', 1, 1, 7, 19);

INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (1, 0, 'SYSTEM', '1', 'BONU', 'BonusPayment', '1');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (2, 0, 'SYSTEM', '2', 'CASH', 'CashManagementTransfer', '2');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (3, 0, 'SYSTEM', '3', 'CBLK', 'Card Bulk Clearing', '3');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (4, 0, 'SYSTEM', '4', 'CCRD', 'Credit Card Payment', '4');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (5, 0, 'SYSTEM', '5', 'CORT', 'TradeSettlementPayment', '5');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (6, 0, 'SYSTEM', '6', 'DCRD', 'Debit Card Payment', '6');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (7, 0, 'SYSTEM', '7', 'DIVI', 'Dividend', '7');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (8, 0, 'SYSTEM', '8', 'EPAY', 'Epayment', '8');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (9, 0, 'SYSTEM', '9', 'FCOL', 'Fee Collection', '9');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (10, 0, 'SYSTEM', '10', 'GOVT', 'GovernmentPayment', '10');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (11, 0, 'SYSTEM', '11', 'HEDG', 'Hedging', '11');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (12, 0, 'SYSTEM', '12', 'ICCP', 'Irrevocable Credit Card Payment', '12');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (13, 0, 'SYSTEM', '13', 'IDCP', 'Irrevocable Debit Card Payment', '13');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (14, 0, 'SYSTEM', '14', 'INTC', 'IntraCompanyPayment', '14');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (15, 0, 'SYSTEM', '15', 'INTE', 'Interest', '15');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (16, 0, 'SYSTEM', '16', 'LOAN', 'Loan', '16');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (17, 0, 'SYSTEM', '17', 'OTHR', 'OtherPayment', '17');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (18, 0, 'SYSTEM', '18', 'PENS', 'PensionPayment', '18');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (19, 0, 'SYSTEM', '19', 'SALA', 'SalaryPayment', '19');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (20, 0, 'SYSTEM', '20', 'SECU', 'Securities', '20');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (21, 0, 'SYSTEM', '21', 'SSBE', 'SocialSecurityBenefit', '21');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (22, 0, 'SYSTEM', '22', 'SUPP', 'SupplierPayment', '22');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (23, 0, 'SYSTEM', '23', 'TAXS', 'TaxPayment', '23');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (24, 0, 'SYSTEM', '24', 'TRAD', 'Trade', '24');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (25, 0, 'SYSTEM', '25', 'TREA', 'TreasuryPayment', '25');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (26, 0, 'SYSTEM', '26', 'VATX', 'ValueAddedTaxPayment', '26');
INSERT INTO ATSMSG_CTGPURPS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, MTCODE) VALUES (27, 0, 'SYSTEM', '27', 'WHLD', 'WithHolding', '27');

INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID) VALUES (1, 0, 'SYSTEM', 1);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID) VALUES (2, 0, 'SYSTEM', 2);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID) VALUES (3, 0, 'SYSTEM', 3);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID) VALUES (4, 0, 'SYSTEM', 8);
INSERT INTO ATSPRT_PURPS (ID, Z_ORG_ID, Z_TENANT_ID, PARTICIPANTID) VALUES (5, 0, 'SYSTEM', 19);

INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (1, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (2, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (3, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (4, 2);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (5, 2);

INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (1, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (2, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (3, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (4, 19);
INSERT INTO ATSPRT_PURPSCATEGORYPURPOSES (PRT_PURPS_ID, CATEGORYPURPOSES_ID) VALUES (5, 19);

INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE, CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID) VALUES (1, 0, 'SYSTEM',50,500,0.001,'Request Reply',2,634,6);
INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE, CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID) VALUES (2, 0, 'SYSTEM',50,5000,0.001,'Request Reply',19,634,6);
INSERT INTO ATSMSG_PURPPROFS (ID, Z_ORG_ID, Z_TENANT_ID, BATCHTXCOUNTLIMIT, MAXAMOUNT, MINAMOUNT, AUTOREPLYMODE, CATEGORYPURPOSEID, CURRENCYID, MSGTYPEID) VALUES (3, 0, 'SYSTEM',50,500,0.001,'Resilience',2,634,2);

INSERT INTO JFW_BROLES_VIEWS (ID, Z_ORG_ID, Z_TENANT_ID, Z_STATUS_ID , BUSINESS_ROLE_ID, VIEW_ID) VALUES (999999, 0, 'SYSTEM', (SELECT Z_STATUS_ID FROM JFW_BROLES_VIEWS WHERE ROWNUM = 1), (SELECT ID FROM JFW_BUSINESS_ROLES WHERE NAME = 'ATSMAKER'), 1204);
INSERT INTO JFW_BROLES_VIEWS (ID, Z_ORG_ID, Z_TENANT_ID, Z_STATUS_ID , BUSINESS_ROLE_ID, VIEW_ID) VALUES (999998, 0, 'SYSTEM', (SELECT Z_STATUS_ID FROM JFW_BROLES_VIEWS WHERE ROWNUM = 1), (SELECT ID FROM JFW_BUSINESS_ROLES WHERE NAME = 'ATSCHECKER'), 1204);

Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT) values (1, 1, 'MPC', 'SR', 'Status Report', 'pacs.002.001.02', 0);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT) values (2, 1, 'MPC', 'DB', 'Direct Debit', 'pacs.003.001.01',  1);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT) values (4, 1, 'MPC', 'CN', 'Cancellation', 'pacs.006.001.01',  0);
Insert into ATSMSG_TYPES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISPAYMENT) values (6, 1, 'MPC', 'CR', 'Direct Credit', 'pacs.008.001.01', 1);

INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (1, 0, 'SYSTEM', '1', 'Reply on credit message', 'Reply-Credit', 1, 6, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (2, 0, 'SYSTEM', '2', 'Reply on debit message', 'Reply-Debit', 1, 2, 2);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (3, 0, 'SYSTEM', '3', 'Cancellation on credit message', 'Cancellation-Credit', 4, 6, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (4, 0, 'SYSTEM', '4', 'Cancellation on debit message', 'Cancellation-Debit', 4, 2, 2);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (5, 0, 'SYSTEM', '5', 'Reply on credit cancellation message', 'Reply-Return-Credit', 1, 4, 6);
INSERT INTO ATSMSG_TYPESLINK (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, MESSAGEID, ORIGINALMESSAGEID, ROOTMESSAGEID) values (6, 0, 'SYSTEM', '6', 'Reply on debit cancellation message', 'Reply-Return-Debit', 1, 4, 2);


INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1, 0, 'SYSTEM', '1', 'Invalid account', 'Invalid account', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (2, 0, 'SYSTEM', '2', 'Account is closed/blocked', 'Account is closed/blocked', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (3, 0, 'SYSTEM', '3', 'Deceased account holder reached', 'Deceased account holder reached', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (4, 0, 'SYSTEM', '4', 'Dormant account', 'Dormant account', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (5, 0, 'SYSTEM', '5', 'Insufficient funds', 'Insufficient funds', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (6, 0, 'SYSTEM', '6', 'Duplicate transaction', 'Duplicate transaction', 0, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1121, 0, 'SYSTEM', '1121', 'Unreachable destination', 'Unreachable destination', 1, 0, 1);
INSERT INTO ATSMSG_REASONS (ID, Z_ORG_ID, Z_TENANT_ID, CODE, DESCRIPTION, NAME, ISSYSTEM, ISFORCED, INSTRUCTEDAWARE)
VALUES (1101, 0, 'SYSTEM', '1101', 'Technical error', 'Technical error', 1, 0, 0);

INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 3
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 4
FROM ATSMSG_REASONS REASONS
WHERE CODE = 1;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 2;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 2;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 3;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 2
FROM ATSMSG_REASONS REASONS
WHERE CODE = 3;
INSERT INTO ATSMSG_REASONSMESSAGESLINK
SELECT REASONS.ID, 1
FROM ATSMSG_REASONS REASONS
WHERE CODE = 4;
INSERT INTO ATSMSG_REASONSMESSAGESLINK SELECT REASONS.ID,2 FROM ATSMSG_REASONS REASONS WHERE CODE =4;
INSERT INTO ATSMSG_REASONSMESSAGESLINK SELECT REASONS.ID,1 FROM ATSMSG_REASONS REASONS WHERE CODE =5;
INSERT INTO ATSMSG_REASONSMESSAGESLINK SELECT REASONS.ID,2 FROM ATSMSG_REASONS REASONS WHERE CODE =5;
INSERT INTO ATSMSG_REASONSMESSAGESLINK SELECT REASONS.ID,1 FROM ATSMSG_REASONS REASONS WHERE CODE =6;
INSERT INTO ATSMSG_REASONSMESSAGESLINK SELECT REASONS.ID,2 FROM ATSMSG_REASONS REASONS WHERE CODE =6;

INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID,AUTHORITY_ID) VALUES (2921218077336, 1);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID,AUTHORITY_ID) VALUES (2921218077336, 6);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID,AUTHORITY_ID) VALUES (2921218078230, 1);
INSERT INTO JFW_BROLES_GENERIC_AUTH (BROLE_ID,AUTHORITY_ID) VALUES (2921218078230, 6);

INSERT INTO JFW_CURRENCIES (ID, Z_ORG_ID, Z_TENANT_ID,  STRING_ISO_CODE, DESCRIPTION, ACTIVE, NUMERIC_ISO_CODE) values (414, 0, 'SYSTEM', 'KWD', 'Kuwaiti Dinar', 0, 414);
UPDATE JFW_CURRENCIES SET JFW_CURRENCIES.ACTIVE = 0 WHERE STRING_ISO_CODE NOT IN('USD', 'KWD','GBP','EUR');
UPDATE JFW_CURRENCIES SET JFW_CURRENCIES.ACTIVE = 1 WHERE STRING_ISO_CODE IN('USD', 'KWD','GBP','EUR');

INSERT INTO ATSCAPEXCEEDEDACTIONS (ID, Z_ORG_ID, Z_TENANT_ID, DEBTORPARTICIPANTID, CREDITORPARTICIPANTID, ACTION) VALUES (1, 0, 'SYSTEM', NULL, NULL, 'RJCT');

INSERT INTO ATSCONFIGS (ID, CONFIGKEY, CONFIGVALUE) VALUES (1, 'AccountValidationThreadSleepTime', '10000');

--------------------------------------------------------
--  Start of Sessions Data
--------------------------------------------------------
INSERT INTO ATSBDS_TEMPLATES (ID, Z_ORG_ID, Z_TENANT_ID, CODE, NAME, DESCRIPTION, PREPSTART, PREPSTOP, CURRENCYID) VALUES (1, 0, 'SYSTEM', '1', 'BDT', 'Business Day Template', 15, 1, 414);

Insert into ATSBDI_SESSIONS (ID,Z_CREATION_DATE, Z_ORG_ID,Z_TENANT_ID,CODE,DESCRIPTION,NAME,BUSINESSDT,CURRPERIOD,DETAILS,EXTENSIONDURATION,ISSPECIALCLEARING,OPENDT,PERIODEND,PERIODSTART,PRIORITYVALUE,READYFLAG,RETURNCREDITDURATION,RETURNDEBITDURATION,REVERSALCREDITDURATION,REVERSALDEBITDURATION,SESSIONSEQ,SETTRETRY,STARTSTAMP,WEIGHT,TEMPLATEID) values (1,TIMESTAMP'2021-10-31 00:00:00',1,'SYSTEM','1','Demo Session / 1','1',parsedatetime('31-10-2021','dd-MM-yyyy'),'2 - Exchange',null,0,0,parsedatetime('31-10-2021','dd-MM-yyyy'),parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.012','dd-MM-yyyy hh:mm:ss.SSS'),'NORM',1,5,5,1,2,'1',0,parsedatetime('31-10-2021 03:11:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,1);

Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (1,0, 'SYSTEM','1','Start of day period completed','Succeeded','Start',parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:11:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (2,0, 'SYSTEM','2',null,'Succeeded','Exchange',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (3,0, 'SYSTEM','3',null,null,'Cut-Off',parsedatetime('31-10-2021 09:13:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),0,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (4,0, 'SYSTEM','4',null,null,'Settlement Preparations',parsedatetime('31-10-2021 09:14:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 09:13:00.000','dd-MM-yyyy hh:mm:ss.SSS'),0,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (5,0, 'SYSTEM','5',null,null,'Settlement',parsedatetime('31-10-2021 09:15:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 09:14:00.000','dd-MM-yyyy hh:mm:ss.SSS'),0,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (6,0, 'SYSTEM','6',null,null,'Final Cut-Off',parsedatetime('31-10-2021 09:16:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 09:15:00.000','dd-MM-yyyy hh:mm:ss.SSS'),0,1);
Insert into ATSBDI_PERIODS (ID,Z_ORG_ID,Z_TENANT_ID,CODE,DETAILS,EXECUTIONSTATE,NAME,PERIODEND,PERIODSTART,READYFLAG,REFSESSIONID) values (7,0, 'SYSTEM','7',null,null,'Closure',parsedatetime('31-10-2021 09:17:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 09:16:00.000','dd-MM-yyyy hh:mm:ss.SSS'),0,1);

Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (1,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Credit',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),6,2,null);
Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (2,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Debit',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),2,2,null);
Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (3,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Credit Reply',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,2,6);
Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (4,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Debit Reply',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,2,2);
Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (5,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Credit Cancellation',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),4,2,6);
Insert into ATSBDI_WINDOWS (ID,Z_ORG_ID,Z_TENANT_ID,GRACEPERIOD,NAME,WINDOWEND,WINDOWSTART,MSGTYPEID,REFPERIODID,RELATEDMSGTYPEID) values (6,0, 'SYSTEM',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),'Debit Cancellation',parsedatetime('31-10-2021 09:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-10-2021 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),4,2,2);


INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013781, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, 100000140, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 4, 1);
INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013782, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, 100000140, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 5, 1);
INSERT INTO ATSBDI_PARTICIPANTS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID, REFWINDOWID)
VALUES(100013783, NULL, NULL, NULL, NULL, NULL, 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.725000', NULL, NULL, NULL, 0, NULL, NULL, 100000140, 'ATS', 'SERVICE_USER', TIMESTAMP '2024-09-22 16:04:59.819000', NULL, NULL, NULL, NULL, NULL, 6, 1);


--------------------------------------------------------
--  End of Sessions Data
--------------------------------------------------------

insert into ATSTEXTMESSAGES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
                             Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE,
                             Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE,
                             Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, FROMPARTICIPANT, MESSAGEBODY, TITLE, Z_DRAFT_ID,
                             Z_STATUS_ID, DIRECTION)
values (1,null,null,null,null,null,null,'2021-10-23 10:58:24',null,null,null,null,null,null,null,null,null,'2021-10-24 10:58:24',null,null,null,'DOHA','test message pull body','test message pull title',null,null,null);

insert into ATSTEXTMESSAGESTOPARTICIPANTS (TEXTMESSAGES_ID, TOPARTICIPANTS_ID, ID, Z_CREATED_BY, Z_CREATION_DATE,
                                           Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_ORG_ID, Z_TENANT_ID,
                                           Z_UPDATED_BY, Z_UPDATING_DATE, TYPE, RETRY_COUNT, STATUS)
values (1,1,1,null,'2021-10-23 10:58:24',null,null,null,null,null,null,'2021-10-24 10:58:24','Memo',0,'Pending');

INSERT INTO ATSPRT_BRANCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID, PARTICIPANTID)  VALUES (1,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'code',null ,'Description',null,null,3);
INSERT INTO ATSLKP_BATCHSOURCES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID) VALUES (1,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'code',null,'name',null,null);

INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID) VALUES (1,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'Accepted',null,'Accepted',null,null);
INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID) VALUES (2,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'In Process',null,'In Process',null,null);
INSERT INTO ATSSTATES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, CODE, DESCRIPTION, NAME, Z_DRAFT_ID, Z_STATUS_ID) VALUES (3,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'Rejected',null,'Rejected',null,null);

-- Debit Pacs 3
-- INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (1,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null ,1,'Admin',null ,1,1,'Admin',null,1,'Token','Pending','This is test data',1400,'3-20211109-124',3,parsedatetime('31-03-2019 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'Low',null ,1,1,1,1,634,1,3,1,3,2,7,1,1,2,1);
-- INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (2,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null ,1,'Admin',null ,1,1,'Admin',null,1,'Token','Pending','This is test data',1400,'3-20211109-118',3,parsedatetime('31-02-2019 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'Low',null ,1,1,1,1,634,1,1,1,3,2,7,1,1,2,1);
-- INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (3,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null ,1,'Admin',null ,1,1,'Admin',null,1,'Token','Pending','This is test data',1400,'3-20211109-120',3,parsedatetime('31-01-2019 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'Low',null ,1,1,1,1,634,1,1,1,3,2,7,1,1,2,1);


-- -- Credit Pacs 8
INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY,
Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT,
ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID,
INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (1,null,null,'CREATED',1,1,'Anas',
null,'Salem',1,null,1,'Admin',null,1,1,'Admin',parsedatetime('31-03-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'Token','Pending','This is test data',1400,'3-20211202-3',3,
parsedatetime('31-08-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'High',null,1,1,1,1,634,1,30,1,3,6,1,1,1,2,1);

-- INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (5,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null,1,'Admin',null,1,1,'Admin',null,1,'Token','Pending','This is test data',1400,'3-20210816-93',3,parsedatetime('31-02-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'High',null,1,1,1,1,634,1,1,1,3,6,1,1,1,2,1);
-- INSERT INTO ATSBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, AMOUNT, BATCHID, COUNT, CREATIONDT, ISIMMEDIATE, PRIORITY, SETTLEMENTDATE, Z_DRAFT_ID, Z_STATUS_ID, CATPURPID, COMPARTIID, CURRENCYID, INSTDBRANCHID, INSTDPARTIID, INSTGBRANCHID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (6,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null,1,'Admin',null,1,1,'Admin',null,1,'Token','Pending','This is test data',1400,'3-20210816-95',3,parsedatetime('31-01-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'High',null,1,1,1,1,634,1,1,1,3,6,1,1,1,2,1);

INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY,
Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID,
Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, BATCHID, COUNT, CREATIONDT, Z_DRAFT_ID,
Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID, ORGBATCHID, ORGMSGTYPEID,ORGSTATEID,PROCESSINGSTATUS,ISSPLITTED) VALUES
(1,parsedatetime('31-03-2019 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),parsedatetime('31-03-2019 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),
'CREATED',1,1,'Anas',null,'Salem',1,null,1,'Admin',
null,1,1,'Admin',
parsedatetime('31-03-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,'Token','Pending','This is test data','999B549017305x',3,
parsedatetime('31-03-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,1,3,3,999,1,1,1,1,1,1,1,6,2,'Accepted',0);

---- Cancellation pacs 6
--INSERT INTO ATSRBATCHES(ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, ADDITIONALINFO, BATCHID, COUNT, CREATIONDT, Z_DRAFT_ID, Z_STATUS_ID, COMPARTIID, INSTDPARTIID, INSTGPARTIID, MSGTYPEID, REASONID, SESSIONID, SOURCEID, STATEID, WINDOWID) VALUES (1,null,null,'CREATED',1,1,'Anas',null,'Salem',1,null,1,'Admin',null,1,1,'Admin',null,1,'Token','Pending','This is test data','3-20211111-5',3,parsedatetime('31-09-2020 03:12:00.000','dd-MM-yyyy hh:mm:ss.SSS'),1,1,1,1,3,4,1,1,1,1,1);

INSERT INTO COMMMESSAGEATT( ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER,
Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL,
Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE,
ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID,
REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (1,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,
null,null,null,STRINGTOUTF8('<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.02"><pacs.002.001.02><GrpHdr><MsgId>999B549017305x</MsgId><CreDtTm>2021-11-05T01:26:22</CreDtTm><InstgAgt><FinInstnId><ClrSysMmbId><Id>999</Id></ClrSysMmbId></FinInstnId></InstgAgt><InstdAgt><FinInstnId><ClrSysMmbId><Id>3</Id></ClrSysMmbId></FinInstnId></InstdAgt></GrpHdr><OrgnlGrpInfAndSts><OrgnlMsgId>3-20211202-3</OrgnlMsgId><OrgnlMsgNmId>pacs.008.001.01</OrgnlMsgNmId><GrpSts>ACTC</GrpSts><StsRsnInf><StsRsn><Prtry>2535</Prtry></StsRsn></StsRsnInf></OrgnlGrpInfAndSts></pacs.002.001.02></Document>'),
null,null,null,null,null,null,null,'999B549017305x',null,'999B549017305x',null,null,null);


INSERT INTO ATSReportRecord (
    ID,
    Z_ARCHIVE_ON,
    Z_ARCHIVE_QUEUED,
    Z_ARCHIVE_STATUS,
    Z_ASSIGNED_GROUP,
    Z_ASSIGNED_USER,
    Z_CREATED_BY,
    Z_CREATION_DATE,
    Z_DELETED_BY,
    Z_DELETED_FLAG,
    Z_DELETED_ON,
    Z_EDITABLE,
    Z_LOCKED_BY,
    Z_LOCKED_UNTIL,
    Z_ORG_ID,
    Z_TENANT_ID,
    Z_UPDATED_BY,
    Z_UPDATING_DATE,
    Z_WORKFLOW_ID,
    Z_WS_TOKEN,
    Z_DRAFT_STATUS,
    Z_DRAFT_ID,
    Z_STATUS_ID,
    COMMAGENTID,
    CURRENCYID,
    SESSIONID,
    SETTAGENTID,
    REPORTCOMMUNICATIONAUTOISPULLED,
    REPORTSETTLEMENTAUTOISPULLED,
    ROWCOUNT,
    WINDOWSETTLEMENTRETRYNUMBER

) VALUES (
             1,
             CURRENT_TIMESTAMP,
             CURRENT_TIMESTAMP,
             'Archived',
             1001,
             1002,
             'admin',
             CURRENT_TIMESTAMP,
             NULL,
             0,
             NULL,
             1,
             'admin',
             CURRENT_TIMESTAMP + INTERVAL '1' DAY,
             1010,
             'ATS',
             'SYSTEM',
             CURRENT_TIMESTAMP,
             1020,
             'token123',
             'Draft',
             1030,
             1040,
             1,
             634,
             4,
             1,
             0,
             0,
             0,
             0
         );