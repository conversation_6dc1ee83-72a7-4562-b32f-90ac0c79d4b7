package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.Objects;

@Entity

@Table(name = "ATSSplitRBatch")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSSplitRBatch extends JFWEntity implements Serializable {

    public ATSSplitRBatch() {}

    public static final String ID = "id";
    @Id
    @SequenceGenerator(name = "SEQ_ATSSplitRBatch", sequenceName = "SEQ_ATSSplitRBatch", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="SEQ_ATSSplitRBatch")
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String BATCH_ID = "batchId";
    @Column(name = "BATCHID", nullable = true, length = 35)
    private String batchId;

    public String getBatchId() {
        return this.batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public static final String ORG_BATCH_ID = "orgBatchId";
    @Column(name = "ORGBATCHID", length = 35)
    private String orgBatchId;

    public String getOrgBatchId() {
        return this.orgBatchId;
    }

    public void setOrgBatchId(String orgBatchId) {
        this.orgBatchId = orgBatchId;
    }

    public static final String IS_PROCESSED = "isProcessed";
    @Column(name = "ISPROCESSED")
    private boolean isProcessed;

    public boolean isProcessed() {
        return isProcessed;
    }

    public void setProcessed(boolean processed) {
        isProcessed = processed;
    }

    public static final String IS_FAILED = "isFailed";
    @Column(name = "ISFAILED")
    private boolean isFailed;

    public boolean isFailed() {
        return isFailed;
    }

    public void setFailed(boolean failed) {
        isFailed = failed;
    }

    public static final String FAILED_TRANSACTION_ID = "failedTransactionId";
    @Column(name = "FAILEDTRANSACTIONID", length = 255, nullable = true)
    private String failedTransactionId;

    public String getFailedTransactionId() {
        return this.failedTransactionId;
    }

    public void setFailedTransactionId(String failedTransactionId) {
        this.failedTransactionId = failedTransactionId;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSSplitRBatch{" +
                "id=" + id +
                ", batchId='" + batchId + '\'' +
                ", orgBatchId='" + orgBatchId + '\'' +
                ", isProcessed=" + isProcessed +
                ", isFailed=" + isFailed +
                ", failedTransactionId='" + failedTransactionId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ATSSplitRBatch that)) return false;
        return id == that.id && isProcessed == that.isProcessed && isFailed == that.isFailed && Objects.equals(batchId, that.batchId) && Objects.equals(orgBatchId, that.orgBatchId) && Objects.equals(failedTransactionId, that.failedTransactionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, batchId, orgBatchId, isProcessed, isFailed, failedTransactionId);
    }
}