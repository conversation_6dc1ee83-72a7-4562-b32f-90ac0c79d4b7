package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSRTransactions",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"TXID", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "RTransactions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRTransaction extends JFWEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public ATSRTransaction() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "TXID", nullable = true, length = 35)
    private String txId;

    public String getTxId() {
        return this.txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }

    @Column(name = "AMOUNT", nullable = true, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal amount;

    public java.math.BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(java.math.BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "ADDITIONALINFO", nullable = true, length = 4000)
    private String additionalInfo;

    public String getAdditionalInfo() {
        return this.additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Column(name = "ORGADDITIONALINFO", nullable = true, length = 4000)
    private String orgAdditionalInfo;

    public String getOrgAdditionalInfo() {
        return this.orgAdditionalInfo;
    }

    public void setOrgAdditionalInfo(String orgAdditionalInfo) {
        this.orgAdditionalInfo = orgAdditionalInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "BATCHID", nullable = false)
    private ATSRBatch batch;

    public ATSRBatch getBatch() {
        return this.batch;
    }

    public void setBatch(ATSRBatch batch) {
        this.batch = batch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "MSGTYPEID", nullable = true)
    private ATSMSG_Type msgType;

    public ATSMSG_Type getMsgType() {
        return this.msgType;
    }

    public void setMsgType(ATSMSG_Type msgType) {
        this.msgType = msgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGMSGTYPEID", nullable = true)
    private ATSMSG_Type orgMsgType;

    public ATSMSG_Type getOrgMsgType() {
        return this.orgMsgType;
    }

    public void setOrgMsgType(ATSMSG_Type orgMsgType) {
        this.orgMsgType = orgMsgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGTXID", nullable = true)
    private ATSTransaction orgTx;

    public ATSTransaction getOrgTx() {
        return this.orgTx;
    }

    public void setOrgTx(ATSTransaction orgTx) {
        this.orgTx = orgTx;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGRTXID", nullable = true)
    private ATSRTransaction orgRTx;

    public ATSRTransaction getOrgRTx() {
        return this.orgRTx;
    }

    public void setOrgRTx(ATSRTransaction orgRTx) {
        this.orgRTx = orgRTx;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGPARTIID", nullable = true)
    private ATSPRT_Participant instgParti;

    public ATSPRT_Participant getInstgParti() {
        return this.instgParti;
    }

    public void setInstgParti(ATSPRT_Participant instgParti) {
        this.instgParti = instgParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDPARTIID", nullable = true)
    private ATSPRT_Participant instdParti;

    public ATSPRT_Participant getInstdParti() {
        return this.instdParti;
    }

    public void setInstdParti(ATSPRT_Participant instdParti) {
        this.instdParti = instdParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "DBTRPARTIID", nullable = true)
    private ATSPRT_Participant dbtrParti;

    public ATSPRT_Participant getDbtrParti() {
        return this.dbtrParti;
    }

    public void setDbtrParti(ATSPRT_Participant dbtrParti) {
        this.dbtrParti = dbtrParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CRDTRPARTIID", nullable = true)
    private ATSPRT_Participant crdtrParti;

    public ATSPRT_Participant getCrdtrParti() {
        return this.crdtrParti;
    }

    public void setCrdtrParti(ATSPRT_Participant crdtrParti) {
        this.crdtrParti = crdtrParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "STATEID", nullable = true)
    private ATSState state;

    public ATSState getState() {
        return this.state;
    }

    public void setState(ATSState state) {
        this.state = state;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REASONID", nullable = true)
    private ATSMSG_Reason reason;

    public ATSMSG_Reason getReason() {
        return this.reason;
    }

    public void setReason(ATSMSG_Reason reason) {
        this.reason = reason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGSTATEID", nullable = true)
    private ATSState orgState;

    public ATSState getOrgState() {
        return this.orgState;
    }

    public void setOrgState(ATSState orgState) {
        this.orgState = orgState;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGREASONID", nullable = true)
    private ATSMSG_Reason orgReason;

    public ATSMSG_Reason getOrgReason() {
        return this.orgReason;
    }

    public void setOrgReason(ATSMSG_Reason orgReason) {
        this.orgReason = orgReason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "WINDOWID", nullable = true)
    private ATSBDI_Window window;

    public ATSBDI_Window getWindow() {
        return this.window;
    }

    public void setWindow(ATSBDI_Window window) {
        this.window = window;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "orgRTx")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRTransaction> orgRTxRTransactions;

    public List<ATSRTransaction> getOrgRTxRTransactions() {
        return this.orgRTxRTransactions;
    }

    public void setOrgRTxRTransactions(List<ATSRTransaction> orgRTxRTransactions) {
        this.orgRTxRTransactions = orgRTxRTransactions;
    }

    @Override
    public String toString() {
        return "ATSRTransaction [id= " + getId() + ", txId= " + getTxId() + ", amount= " + getAmount() + ", additionalInfo= " + getAdditionalInfo() + ", orgAdditionalInfo= " + getOrgAdditionalInfo() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getTxId() == null) ? 0 : getTxId().hashCode());
        result = prime * result + ((getAdditionalInfo() == null) ? 0 : getAdditionalInfo().hashCode());
        result = prime * result + ((getOrgAdditionalInfo() == null) ? 0 : getOrgAdditionalInfo().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSRTransaction other = (ATSRTransaction) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}
