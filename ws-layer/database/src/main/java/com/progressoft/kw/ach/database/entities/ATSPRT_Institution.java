package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name = "ATSPRT_Institutions",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"CODE", "Z_TENANT_ID"}),
                        @UniqueConstraint(columnNames = {"NAME", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "PRT_Institutions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_Institution extends JFWLookableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSPRT_Institution() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "ATSPRT_Institution [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSPRT_Institution other = (ATSPRT_Institution) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}