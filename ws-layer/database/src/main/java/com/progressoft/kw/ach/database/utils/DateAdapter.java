package com.progressoft.kw.ach.database.utils;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateAdapter extends XmlAdapter<String, Date> {

	private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

	@Override
	public String marshal(Date date) {
		return dateFormat.format(date);
	}

	@Override
	public Date unmarshal(String dateString) throws Exception {
		return new Date(dateFormat.parse(dateString).getTime());
	}

}
