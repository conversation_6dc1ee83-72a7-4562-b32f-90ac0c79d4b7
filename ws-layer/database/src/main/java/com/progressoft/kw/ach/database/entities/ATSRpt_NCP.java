package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSRpt_NCP")
@XmlRootElement(name = "Rpt_NCP")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_NCP extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSRpt_NCP() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String SETTLEMENT_DT = "settlementDt";
    @Column(name = "SETTLEMENTDT", nullable = false, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDt;

    public java.util.Date getSettlementDt() {
        return this.settlementDt;
    }

    public void setSettlementDt(java.util.Date settlementDt) {
        this.settlementDt = settlementDt;
    }

    public static final String TOTAL_CREDIT = "totalCredit";
    @Column(name = "TOTALCREDIT", nullable = false, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal totalCredit;

    public java.math.BigDecimal getTotalCredit() {
        return this.totalCredit;
    }

    public void setTotalCredit(java.math.BigDecimal totalCredit) {
        this.totalCredit = totalCredit;
    }

    public static final String TOTAL_DEBIT = "totalDebit";
    @Column(name = "TOTALDEBIT", nullable = false, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal totalDebit;

    public java.math.BigDecimal getTotalDebit() {
        return this.totalDebit;
    }

    public void setTotalDebit(java.math.BigDecimal totalDebit) {
        this.totalDebit = totalDebit;
    }

    public static final String PARTICIPATED_SYSTEMS = "participatedSystems";
    @Column(name = "PARTICIPATEDSYSTEMS", nullable = true, length = 200)
    private String participatedSystems;

    public String getParticipatedSystems() {
        return this.participatedSystems;
    }

    public void setParticipatedSystems(String participatedSystems) {
        this.participatedSystems = participatedSystems;
    }

    public static final String FIRST_AGENT_ID = "firstAgentId";
    @Column(name = "FIRSTAGENTID", nullable = true, length = 255)
    private String firstAgentId;

    public String getFirstAgentId() {
        return this.firstAgentId;
    }

    public void setFirstAgentId(String firstAgentId) {
        this.firstAgentId = firstAgentId;
    }

    public static final String FIRST_AGENT_SHORT_NAME = "firstAgentShortName";
    @Column(name = "FIRSTAGENTSHORTNAME", nullable = true, length = 255)
    private String firstAgentShortName;

    public String getFirstAgentShortName() {
        return this.firstAgentShortName;
    }

    public void setFirstAgentShortName(String firstAgentShortName) {
        this.firstAgentShortName = firstAgentShortName;
    }

    public static final String FIRST_AGENT_FULL_NAME = "firstAgentFullName";
    @Column(name = "FIRSTAGENTFULLNAME", nullable = true, length = 255)
    private String firstAgentFullName;

    public String getFirstAgentFullName() {
        return this.firstAgentFullName;
    }

    public void setFirstAgentFullName(String firstAgentFullName) {
        this.firstAgentFullName = firstAgentFullName;
    }

    public static final String FIRST_AGENT_ACCOUNT_NUMBER = "firstAgentAccountNumber";
    @Column(name = "FIRSTAGENTACCOUNTNUMBER", nullable = true, length = 255)
    private String firstAgentAccountNumber;

    public String getFirstAgentAccountNumber() {
        return this.firstAgentAccountNumber;
    }

    public void setFirstAgentAccountNumber(String firstAgentAccountNumber) {
        this.firstAgentAccountNumber = firstAgentAccountNumber;
    }

    public static final String SECOND_AGENT_ID = "secondAgentId";
    @Column(name = "SECONDAGENTID", nullable = true, length = 255)
    private String secondAgentId;

    public String getSecondAgentId() {
        return this.secondAgentId;
    }

    public void setSecondAgentId(String secondAgentId) {
        this.secondAgentId = secondAgentId;
    }

    public static final String SECOND_AGENT_SHORT_NAME = "secondAgentShortName";
    @Column(name = "SECONDAGENTSHORTNAME", nullable = true, length = 255)
    private String secondAgentShortName;

    public String getSecondAgentShortName() {
        return this.secondAgentShortName;
    }

    public void setSecondAgentShortName(String secondAgentShortName) {
        this.secondAgentShortName = secondAgentShortName;
    }

    public static final String SECOND_AGENT_FULL_NAME = "secondAgentFullName";
    @Column(name = "SECONDAGENTFULLNAME", nullable = true, length = 255)
    private String secondAgentFullName;

    public String getSecondAgentFullName() {
        return this.secondAgentFullName;
    }

    public void setSecondAgentFullName(String secondAgentFullName) {
        this.secondAgentFullName = secondAgentFullName;
    }

    public static final String SECOND_AGENT_ACCOUNT_NUMBER = "secondAgentAccountNumber";
    @Column(name = "SECONDAGENTACCOUNTNUMBER", nullable = true, length = 255)
    private String secondAgentAccountNumber;

    public String getSecondAgentAccountNumber() {
        return this.secondAgentAccountNumber;
    }

    public void setSecondAgentAccountNumber(String secondAgentAccountNumber) {
        this.secondAgentAccountNumber = secondAgentAccountNumber;
    }

    public static final String WINDOW_CYCLE_NUMBER = "windowCycleNumber";
    @Column(name = "WINDOWCYCLENUMBER", nullable = true, length = 10)
    private long windowCycleNumber;

    public long getWindowCycleNumber() {
        return this.windowCycleNumber;
    }

    public void setWindowCycleNumber(long windowCycleNumber) {
        this.windowCycleNumber = windowCycleNumber;
    }

    public static final String WINDOW_PRE_SETTLEMENT_DATE = "windowPreSettlementDate";
    @Column(name = "WINDOWPRESETTLEMENTDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date windowPreSettlementDate;

    public java.util.Date getWindowPreSettlementDate() {
        return this.windowPreSettlementDate;
    }

    public void setWindowPreSettlementDate(java.util.Date windowPreSettlementDate) {
        this.windowPreSettlementDate = windowPreSettlementDate;
    }

    public static final String WINDOW_SETTLEMENT_START_DATE = "windowSettlementStartDate";
    @Column(name = "WINDOWSETTLEMENTSTARTDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date windowSettlementStartDate;

    public java.util.Date getWindowSettlementStartDate() {
        return this.windowSettlementStartDate;
    }

    public void setWindowSettlementStartDate(java.util.Date windowSettlementStartDate) {
        this.windowSettlementStartDate = windowSettlementStartDate;
    }

    public static final String WINDOW_SETTLEMENT_END_DATE = "windowSettlementEndDate";
    @Column(name = "WINDOWSETTLEMENTENDDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date windowSettlementEndDate;

    public java.util.Date getWindowSettlementEndDate() {
        return this.windowSettlementEndDate;
    }

    public void setWindowSettlementEndDate(java.util.Date windowSettlementEndDate) {
        this.windowSettlementEndDate = windowSettlementEndDate;
    }

    public static final String WINDOW_STATUS = "windowStatus";
    @Column(name = "WINDOWSTATUS", nullable = true, length = 255)
    private String windowStatus;

    public String getWindowStatus() {
        return this.windowStatus;
    }

    public void setWindowStatus(String windowStatus) {
        this.windowStatus = windowStatus;
    }

    public static final String WINDOW_SETTLEMENT_RETRY_NUMBER = "windowSettlementRetryNumber";
    @Column(name = "WINDOWSETTLEMENTRETRYNUMBER", nullable = true, length = 10)
    private long windowSettlementRetryNumber;

    public long getWindowSettlementRetryNumber() {
        return this.windowSettlementRetryNumber;
    }

    public void setWindowSettlementRetryNumber(long windowSettlementRetryNumber) {
        this.windowSettlementRetryNumber = windowSettlementRetryNumber;
    }

    public static final String WINDOW_TEMPLATE_NAME = "windowTemplateName";
    @Column(name = "WINDOWTEMPLATENAME", nullable = true, length = 255)
    private String windowTemplateName;

    public String getWindowTemplateName() {
        return this.windowTemplateName;
    }

    public void setWindowTemplateName(String windowTemplateName) {
        this.windowTemplateName = windowTemplateName;
    }

    public static final String COMM_AGENT = "commAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMMAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant commAgent;

    public ATSPRT_Participant getCommAgent() {
        return this.commAgent;
    }

    public void setCommAgent(ATSPRT_Participant commAgent) {
        this.commAgent = commAgent;
    }

    public static final String SETT_AGENT = "settAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SETTAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant settAgent;

    public ATSPRT_Participant getSettAgent() {
        return this.settAgent;
    }

    public void setSettAgent(ATSPRT_Participant settAgent) {
        this.settAgent = settAgent;
    }

    public static final String SESSION = "session";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    public static final String REF_N_C_P_RPT_N_C_P_DETAILS = "refNCPRpt_NCP_Details";
    @OneToMany(fetch = FetchType.LAZY,mappedBy = "refNCP")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRpt_NCP_Detail> refNCPRpt_NCP_Details;

    public List<ATSRpt_NCP_Detail> getRefNCPRptNCPDetails() {
        return this.refNCPRpt_NCP_Details;
    }

    public void setRefNCPRptNCPDetails(List<ATSRpt_NCP_Detail> refNCPRpt_NCP_Details) {
        this.refNCPRpt_NCP_Details = refNCPRpt_NCP_Details;
    }

    public static final String IS_PULLED = "isPulled";
    @Column(name = "ISPULLED", nullable = true, length = 1)
    private boolean isPulled;

    public boolean isPulled() {
        return isPulled;
    }

    public void setPulled(boolean pulled) {
        isPulled = pulled;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSRpt_NCP [id= " + getId() + ", settlementDt= " + getSettlementDt() + ", totalCredit= " + getTotalCredit() + ", totalDebit= " + getTotalDebit() + ", participatedSystems= " + getParticipatedSystems() + ", firstAgentId= " + getFirstAgentId() + ", firstAgentShortName= " + getFirstAgentShortName() + ", firstAgentFullName= " + getFirstAgentFullName() + ", firstAgentAccountNumber= " + getFirstAgentAccountNumber() + ", secondAgentId= " + getSecondAgentId() + ", secondAgentShortName= " + getSecondAgentShortName() + ", secondAgentFullName= " + getSecondAgentFullName() + ", secondAgentAccountNumber= " + getSecondAgentAccountNumber() + ", windowCycleNumber= " + getWindowCycleNumber() + ", windowPreSettlementDate= " + getWindowPreSettlementDate() + ", windowSettlementStartDate= " + getWindowSettlementStartDate() + ", windowSettlementEndDate= " + getWindowSettlementEndDate() + ", windowStatus= " + getWindowStatus() + ", windowSettlementRetryNumber= " + getWindowSettlementRetryNumber() + ", windowTemplateName= " + getWindowTemplateName() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
        result = prime * result + ((getParticipatedSystems() == null) ? 0 : getParticipatedSystems().hashCode());
        result = prime * result + ((getFirstAgentId() == null) ? 0 : getFirstAgentId().hashCode());
        result = prime * result + ((getFirstAgentShortName() == null) ? 0 : getFirstAgentShortName().hashCode());
        result = prime * result + ((getFirstAgentFullName() == null) ? 0 : getFirstAgentFullName().hashCode());
        result = prime * result + ((getFirstAgentAccountNumber() == null) ? 0 : getFirstAgentAccountNumber().hashCode());
        result = prime * result + ((getSecondAgentId() == null) ? 0 : getSecondAgentId().hashCode());
        result = prime * result + ((getSecondAgentShortName() == null) ? 0 : getSecondAgentShortName().hashCode());
        result = prime * result + ((getSecondAgentFullName() == null) ? 0 : getSecondAgentFullName().hashCode());
        result = prime * result + ((getSecondAgentAccountNumber() == null) ? 0 : getSecondAgentAccountNumber().hashCode());
        int WindowCycleNumber = new Long("null".equals(getWindowCycleNumber() + "") ? 0 : getWindowCycleNumber()).intValue();
        result = prime * result + (int) (WindowCycleNumber ^ WindowCycleNumber >>> 32);
        result = prime * result + ((getWindowPreSettlementDate() == null) ? 0 : getWindowPreSettlementDate().hashCode());
        result = prime * result + ((getWindowSettlementStartDate() == null) ? 0 : getWindowSettlementStartDate().hashCode());
        result = prime * result + ((getWindowSettlementEndDate() == null) ? 0 : getWindowSettlementEndDate().hashCode());
        result = prime * result + ((getWindowStatus() == null) ? 0 : getWindowStatus().hashCode());
        int WindowSettlementRetryNumber = new Long("null".equals(getWindowSettlementRetryNumber() + "") ? 0 : getWindowSettlementRetryNumber()).intValue();
        result = prime * result + (int) (WindowSettlementRetryNumber ^ WindowSettlementRetryNumber >>> 32);
        result = prime * result + ((getWindowTemplateName() == null) ? 0 : getWindowTemplateName().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSRpt_NCP other = (ATSRpt_NCP) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}