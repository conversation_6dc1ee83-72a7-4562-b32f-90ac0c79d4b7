package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "ATSCommunicatedAuditTrails")
@XmlRootElement(name = "CommunicatedAuditTrails")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSCommunicatedAuditTrail extends JFWEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "audit_trail_seq")
    @SequenceGenerator(
            name = "audit_trail_seq",
            sequenceName = "SEQ_ATSCOMMUNICATEDAUDITTRAILS",
            allocationSize = 1
    )
    private Long id = 0L;

    @Column(name = "MESSAGEREFERENCE")
    private String messageReference = "";

    @Column(name = "PARTICIPANT")
    private String participant;


    @Column(name = "MESSAGETYPE")
    private String messageType;

    @Column(name = "ERRORCODE")
    private String errorCode;

    @Column(name = "ERRORMESSAGE")
    private String errorMessage;

    @Column(name = "HASVALIDSIGNATURE")
    private boolean hasValidSignature = false;

    @Column(name = "STATUS")
    private String status = "";

    public Long getId() {
        return id;
    }

    public String getMessageReference() {
        return messageReference;
    }

    public void setMessageReference(String messageReference) {
        this.messageReference = messageReference;
    }

    public String getParticipant() {
        return participant;
    }

    public void setParticipant(String participant) {
        this.participant = participant;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public boolean isHasValidSignature() {
        return hasValidSignature;
    }

    public void setHasValidSignature(boolean hasValidSignature) {
        this.hasValidSignature = hasValidSignature;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public void injectTenantId() {
    }

    @Override
    public String toString() {
        return "ATSCommunicatedAuditTrail{" +
                "id=" + id +
                ", messageReference='" + messageReference + '\'' +
                ", participant=" + participant +
                ", messageType=" + messageType +
                ", errorCode='" + errorCode + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", hasValidSignature=" + hasValidSignature +
                ", status='" + status + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ATSCommunicatedAuditTrail that = (ATSCommunicatedAuditTrail) o;
        return hasValidSignature == that.hasValidSignature && Objects.equals(id, that.id) && Objects.equals(messageReference, that.messageReference) && Objects.equals(participant, that.participant) && Objects.equals(messageType, that.messageType) && Objects.equals(errorCode, that.errorCode) && Objects.equals(errorMessage, that.errorMessage) && Objects.equals(status, that.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, messageReference, participant, messageType, errorCode, errorMessage, hasValidSignature, status);
    }
}

