package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.kw.ach.database.utils.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSBDI_Windows",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"REFPERIODID","NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="BDI_Windows")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_Window extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_Window(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWSTART", nullable=false, length=34)
private java.sql.Timestamp windowStart;
public java.sql.Timestamp getWindowStart(){
return this.windowStart;
}
public void setWindowStart(java.sql.Timestamp windowStart){
this.windowStart = windowStart;
}

@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWEND", nullable=false, length=34)
private java.sql.Timestamp windowEnd;
public java.sql.Timestamp getWindowEnd(){
return this.windowEnd;
}
public void setWindowEnd(java.sql.Timestamp windowEnd){
this.windowEnd = windowEnd;
}

@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="GRACEPERIOD", nullable=true, length=34)
private java.sql.Timestamp gracePeriod;
public java.sql.Timestamp getGracePeriod(){
return this.gracePeriod;
}
public void setGracePeriod(java.sql.Timestamp gracePeriod){
this.gracePeriod = gracePeriod;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPERIODID", nullable=true)
private ATSBDI_Period refPeriod;
public ATSBDI_Period getRefPeriod(){
return this.refPeriod;
}
public void setRefPeriod(ATSBDI_Period refPeriod){
this.refPeriod = refPeriod;
}

@OneToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

@OneToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="RELATEDMSGTYPEID", nullable=true)
private ATSMSG_Type relatedMsgType;
public ATSMSG_Type getRelatedMsgType(){
return this.relatedMsgType;
}
public void setRelatedMsgType(ATSMSG_Type relatedMsgType){
this.relatedMsgType = relatedMsgType;
}

@OneToMany(fetch = FetchType.LAZY,mappedBy = "refWindow")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSBDI_Participant> refWindowBDI_Participants;
public List<ATSBDI_Participant> getRefWindowBDIParticipants(){
return this.refWindowBDI_Participants;
}
public void setRefWindowBDIParticipants(List<ATSBDI_Participant> refWindowBDI_Participants){
this.refWindowBDI_Participants = refWindowBDI_Participants;
}

@Override
public String toString() {
return "ATSBDI_Window [id= " + getId() + ", name= " + getName() + ", windowStart= " + getWindowStart() + ", windowEnd= " + getWindowEnd() + ", gracePeriod= " + getGracePeriod() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_Window other = (ATSBDI_Window) obj;
return this.hashCode() == other.hashCode();}
}


}