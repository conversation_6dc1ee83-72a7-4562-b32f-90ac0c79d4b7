package com.progressoft.kw.ach.database.entities;

import jakarta.persistence.*;

@Entity
@Table(name = "JFW_OS_CURRENTSTEP")
public class JfwOsCurrentStep {
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private Long id;

    @Column(name = "ACTION_ID")
    private Long actionId;

    @Column(name = "Z_ARCHIVE_STATUS")
    private String zArchiveStatus;

    @Column(name = "CALLER")
    private String caller;

    @Column(name = "DUE_DATE")
    private java.sql.Timestamp dueDate;

    @Column(name = "FINISH_DATE")
    private java.sql.Timestamp finishDate;

    @Column(name = "OWNER")
    private String owner;

    @Column(name = "START_DATE")
    private java.sql.Timestamp startDate;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "STEP_ID")
    private Long stepId;

    @Column(name = "ENTRY_ID")
    private Long entryId;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getZArchiveStatus() {
        return this.zArchiveStatus;
    }

    public void setZArchiveStatus(String zArchiveStatus) {
        this.zArchiveStatus = zArchiveStatus;
    }

    public String getCaller() {
        return this.caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public java.sql.Timestamp getDueDate() {
        return this.dueDate;
    }

    public void setDueDate(java.sql.Timestamp dueDate) {
        this.dueDate = dueDate;
    }

    public java.sql.Timestamp getFinishDate() {
        return this.finishDate;
    }

    public void setFinishDate(java.sql.Timestamp finishDate) {
        this.finishDate = finishDate;
    }

    public String getOwner() {
        return this.owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public java.sql.Timestamp getStartDate() {
        return this.startDate;
    }

    public void setStartDate(java.sql.Timestamp startDate) {
        this.startDate = startDate;
    }

    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getStepId() {
        return this.stepId;
    }

    public void setStepId(Long stepId) {
        this.stepId = stepId;
    }

    public Long getEntryId() {
        return this.entryId;
    }

    public void setEntryId(Long entryId) {
        this.entryId = entryId;
    }
}
