package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;
import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSPRT_Participants",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"CODE", "Z_TENANT_ID"}),
                        @UniqueConstraint(columnNames = {"NAME", "Z_TENANT_ID"}),
                        @UniqueConstraint(columnNames = {"PREFIX", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "PRT_Participants")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE)
public class ATSPRT_Participant extends JFWLookableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSPRT_Participant() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "HEADOFFICEBRANCHCODE", nullable = false, length = 3)
    private String headOfficeBranchCode;

    public String getHeadOfficeBranchCode() {
        return this.headOfficeBranchCode;
    }

    public void setHeadOfficeBranchCode(String headOfficeBranchCode) {
        this.headOfficeBranchCode = headOfficeBranchCode;
    }

    @Column(name = "ACCOUNTNUMBERLENGTH", nullable = false, length = 2)
    private long accountNumberLength;

    public long getAccountNumberLength() {
        return this.accountNumberLength;
    }

    public void setAccountNumberLength(long accountNumberLength) {
        this.accountNumberLength = accountNumberLength;
    }

    @Column(name = "COUNTRY", nullable = true, length = 150)
    private String country;

    public String getCountry() {
        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Column(name = "CITY", nullable = true, length = 150)
    private String city;

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "PHONE", nullable = true, length = 32)
    private String phone;

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Column(name = "EMAIL", nullable = true, length = 128)
    private String email;

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "ADDRESS", nullable = true, length = 4000)
    private String address;

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    @Column(name = "SENDSUSPFLAG", nullable = true, length = 1)
    private boolean sendSuspFlag;

    public boolean getSendSuspFlag() {
        return this.sendSuspFlag;
    }

    public void setSendSuspFlag(boolean sendSuspFlag) {
        this.sendSuspFlag = sendSuspFlag;
    }

    @Column(name = "RECEIVESUSPFLAG", nullable = true, length = 1)
    private boolean receiveSuspFlag;

    public boolean getReceiveSuspFlag() {
        return this.receiveSuspFlag;
    }

    public void setReceiveSuspFlag(boolean receiveSuspFlag) {
        this.receiveSuspFlag = receiveSuspFlag;
    }

    @Column(name = "DEFAULTFLAG", nullable = true, length = 1)
    private boolean defaultFlag;

    public boolean getDefaultFlag() {
        return this.defaultFlag;
    }

    public void setDefaultFlag(boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
    }

    @Column(name = "TERMINATEFLAG", nullable = true, length = 1)
    private boolean terminateFlag;

    public boolean getTerminateFlag() {
        return this.terminateFlag;
    }

    public void setTerminateFlag(boolean terminateFlag) {
        this.terminateFlag = terminateFlag;
    }

    @Column(name = "CHRGEXCLUSIONFLAG", nullable = true, length = 1)
    private boolean chrgExclusionFlag;

    public boolean getChrgExclusionFlag() {
        return this.chrgExclusionFlag;
    }

    public void setChrgExclusionFlag(boolean chrgExclusionFlag) {
        this.chrgExclusionFlag = chrgExclusionFlag;
    }

    @Column(name = "FORMAT", nullable = false, length = 20)
    private String format;

    public String getFormat() {
        return this.format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    @Column(name = "PREFIX", nullable = false, length = 20)
    private String prefix;

    public String getPrefix() {
        return this.prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "PARTICIPANTTYPEID", nullable = false)
    private ATSPRT_Type participantType;

    public ATSPRT_Type getParticipantType() {
        return this.participantType;
    }

    public void setParticipantType(ATSPRT_Type participantType) {
        this.participantType = participantType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMMPARTID", nullable = true)
    private ATSPRT_Participant commPart;

    public ATSPRT_Participant getCommPart() {
        return this.commPart;
    }

    public void setCommPart(ATSPRT_Participant commPart) {
        this.commPart = commPart;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTITUTIONTYPEID", nullable = false)
    private ATSPRT_Institution institutionType;

    public ATSPRT_Institution getInstitutionType() {
        return this.institutionType;
    }

    public void setInstitutionType(ATSPRT_Institution institutionType) {
        this.institutionType = institutionType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "LIMITSPROFILEID", nullable = false)
    private ATSPRT_LimitsProfile limitsProfile;

    public ATSPRT_LimitsProfile getLimitsProfile() {
        return this.limitsProfile;
    }

    public void setLimitsProfile(ATSPRT_LimitsProfile limitsProfile) {
        this.limitsProfile = limitsProfile;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "refParticipant")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @Fetch(FetchMode.JOIN)
    private List<ATSPRT_SettParticipant> refParticipantPRT_SettParticipants;

    public List<ATSPRT_SettParticipant> getRefParticipantPRTSettParticipants() {
        return this.refParticipantPRT_SettParticipants;
    }

    @Column(name = "FUTUREDATEDPAYMENTALLOWED")
    private boolean futureDatedPaymentAllowed;
    public boolean isFutureDatedPaymentAllowed() {
        return futureDatedPaymentAllowed;
    }
    public void setFutureDatedPaymentAllowed(boolean futureDatedPaymentAllowed) {
        this.futureDatedPaymentAllowed = futureDatedPaymentAllowed;
    }


    public void setRefParticipantPRTSettParticipants(List<ATSPRT_SettParticipant> refParticipantPRT_SettParticipants) {
        this.refParticipantPRT_SettParticipants = refParticipantPRT_SettParticipants;
    }

    @Override
    public String toString() {
        return "ATSPRT_Participant [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", headOfficeBranchCode= " + getHeadOfficeBranchCode() + ", accountNumberLength= " + getAccountNumberLength() + ", country= " + getCountry() + ", city= " + getCity() + ", phone= " + getPhone() + ", email= " + getEmail() + ", address= " + getAddress() + ", sendSuspFlag= " + getSendSuspFlag() + ", receiveSuspFlag= " + getReceiveSuspFlag() + ", defaultFlag= " + getDefaultFlag() + ", terminateFlag= " + getTerminateFlag() + ", chrgExclusionFlag= " + getChrgExclusionFlag() + ", format= " + getFormat() + ", prefix= " + getPrefix() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getHeadOfficeBranchCode() == null) ? 0 : getHeadOfficeBranchCode().hashCode());
        int AccountNumberLength = new Long("null".equals(getAccountNumberLength() + "") ? 0 : getAccountNumberLength()).intValue();
        result = prime * result + (int) (AccountNumberLength ^ AccountNumberLength >>> 32);
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getPrefix() == null) ? 0 : getPrefix().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSPRT_Participant other = (ATSPRT_Participant) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}