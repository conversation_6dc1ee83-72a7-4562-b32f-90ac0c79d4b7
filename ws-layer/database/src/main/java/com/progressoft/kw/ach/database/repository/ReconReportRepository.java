package com.progressoft.kw.ach.database.repository;


import com.progressoft.kw.ach.database.entities.ATSRpt_Recon;
import jakarta.transaction.Transactional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ReconReportRepository extends JpaRepository<ATSRpt_Recon, Long> {
    @Transactional
    @Query("SELECT n FROM ATSRpt_Recon n JOIN ATSPRT_Participant p ON n.settAgent.id = p.id WHERE p.code = :code and n.isPulled = false")
    List<ATSRpt_Recon> findBySettAgentCode(@Param("code") String code);

    @Transactional
    @Query(value = "SELECT * FROM ATSRpt_Recon " +
            "WHERE reportId = :reportId " +
            "AND isPulled = 0 " +
            "FETCH FIRST 1 ROW ONLY", nativeQuery = true)
    Optional<ATSRpt_Recon> findReportByReportId(@Param("reportId") String reportId);
}
