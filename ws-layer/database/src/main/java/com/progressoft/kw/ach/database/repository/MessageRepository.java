package com.progressoft.kw.ach.database.repository;

import com.progressoft.communication.entity.COMMMessage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface MessageRepository extends JpaRepository<COMMMessage, Long> {
    COMMMessage findByMessageIdAndDirection(String batchId , String direction);
    boolean existsByMessageId(String batchId);
    @Query(value = "SELECT MESSAGES_Sequence.NEXTVAL FROM DUAL", nativeQuery = true)
    Long getMessageSequenceValue();
}
