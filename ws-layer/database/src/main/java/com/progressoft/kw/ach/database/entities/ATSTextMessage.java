package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSTextMessages")
@XmlRootElement(name="TextMessages")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSTextMessage extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSTextMessage(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String FROM_PARTICIPANT = "fromParticipant";
@Column(name="FROMPARTICIPANT", nullable=false, length=4000)
private String fromParticipant;
public String getFromParticipant(){
return this.fromParticipant;
}
public void setFromParticipant(String fromParticipant){
this.fromParticipant = fromParticipant;
}

public static final String TITLE = "title";
@Column(name="TITLE", nullable=false, length=500)
private String title;
public String getTitle(){
return this.title;
}
public void setTitle(String title){
this.title = title;
}

public static final String TEXT_MESSAGE_ID = "textMessageId";
@Column(name="TEXTMESSAGEID", nullable=false, length=35)
private String textMessageId;
public String getTextMessageId(){
return this.textMessageId;
}
public void setTextMessageId(String textMessageId){
this.textMessageId = textMessageId;
}

public static final String DIRECTION = "direction";
@Column(name="DIRECTION", nullable=true, length=255)
private String direction;
public String getDirection(){
return this.direction;
}
public void setDirection(String direction){
this.direction = direction;
}

public static final String MESSAGE_TYPE = "messageType";
@Column(name="MESSAGETYPE", nullable=false, length=30)
private String messageType;
public String getMessageType(){
return this.messageType;
}
public void setMessageType(String messageType){
this.messageType = messageType;
}

public static final String MESSAGE_BODY = "messageBody";
@Column(name="MESSAGEBODY", nullable=false, length=4000)
private String messageBody;
public String getMessageBody(){
return this.messageBody;
}
public void setMessageBody(String messageBody){
this.messageBody = messageBody;
}

public static final String TO_PARTICIPANTS = "toParticipants";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSTextMessagesToparticipants", joinColumns = @JoinColumn(name = "TEXTMESSAGES_ID"), inverseJoinColumns = @JoinColumn(name = "TOPARTICIPANTS_ID"))
private List<ATSPRT_Participant> toParticipants = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getToParticipants(){
return this.toParticipants;
}
public void setToParticipants(List<ATSPRT_Participant> toParticipants){
this.toParticipants = toParticipants;
}

public static final String REF_TEXT_MESSAGE_REF_TEXT_MESSAGES = "refTextMessageRefTextMessages";
@OneToMany(fetch = FetchType.LAZY,mappedBy = "refTextMessage")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSRefTextMessage> refTextMessageRefTextMessages;
public List<ATSRefTextMessage> getRefTextMessageRefTextMessages(){
return this.refTextMessageRefTextMessages;
}
public void setRefTextMessageRefTextMessages(List<ATSRefTextMessage> refTextMessageRefTextMessages){
this.refTextMessageRefTextMessages = refTextMessageRefTextMessages;
}

    @Override
    public void injectTenantId() {

    }

@Override
public String toString() {
return "ATSTextMessage [id= " + getId() + ", fromParticipant= " + getFromParticipant() + ", title= " + getTitle() + ", textMessageId= " + getTextMessageId() + ", direction= " + getDirection() + ", messageType= " + getMessageType() + ", messageBody= " + getMessageBody() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getFromParticipant() == null) ? 0 : getFromParticipant().hashCode());
result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
result = prime * result + ((getTextMessageId() == null) ? 0 : getTextMessageId().hashCode());
result = prime * result + ((getDirection() == null) ? 0 : getDirection().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSTextMessage other = (ATSTextMessage) obj;
return this.hashCode() == other.hashCode();}
}


}