package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSACC_Accounts",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"ACCNUMBER", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "ACC_Accounts")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE)
public class ATSACC_Account extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSACC_Account() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "ACCNUMBER", nullable = false, length = 20)
    private String accNumber;

    public String getAccNumber() {
        return this.accNumber;
    }

    public void setAccNumber(String accNumber) {
        this.accNumber = accNumber;
    }

    @Column(name = "ACCNAME", nullable = true, length = 255)
    private String accName;

    public String getAccName() {
        return this.accName;
    }

    public void setAccName(String accName) {
        this.accName = accName;
    }

    @Column(name = "ACCREFERENCE", nullable = true, length = 255)
    private String accReference;

    public String getAccReference() {
        return this.accReference;
    }

    public void setAccReference(String accReference) {
        this.accReference = accReference;
    }

    @Column(name = "IBAN", nullable = false)
    private String IBAN;
    public String getIBAN() {
        return this.IBAN;
    }
    public void setIBAN(String IBAN) {
        this.IBAN = IBAN;
    }

    @Column(name = "DEBITCAP", nullable = false, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal debitCap;

    public java.math.BigDecimal getDebitCap() {
        return this.debitCap;
    }

    public void setDebitCap(java.math.BigDecimal debitCap) {
        this.debitCap = debitCap;
    }

    @Column(name = "CREDITCAP", nullable = false, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal creditCap;

    public java.math.BigDecimal getCreditCap() {
        return this.creditCap;
    }

    public void setCreditCap(java.math.BigDecimal creditCap) {
        this.creditCap = creditCap;
    }

    @Column(name = "DEBITWATERMARK", nullable = false, length = 2)
    private long debitWatermark;

    public long getDebitWatermark() {
        return this.debitWatermark;
    }

    public void setDebitWatermark(long debitWatermark) {
        this.debitWatermark = debitWatermark;
    }

    @Column(name = "CREDITWATERMARK", nullable = false, length = 2)
    private long creditWatermark;

    public long getCreditWatermark() {
        return this.creditWatermark;
    }

    public void setCreditWatermark(long creditWatermark) {
        this.creditWatermark = creditWatermark;
    }

    @Column(name = "DEBITSUSPFLAG", nullable = true, length = 1)
    private boolean debitSuspFlag;

    public boolean getDebitSuspFlag() {
        return this.debitSuspFlag;
    }

    public void setDebitSuspFlag(boolean debitSuspFlag) {
        this.debitSuspFlag = debitSuspFlag;
    }

    @Column(name = "CREDITSUSPFLAG", nullable = true, length = 1)
    private boolean creditSuspFlag;

    public boolean getCreditSuspFlag() {
        return this.creditSuspFlag;
    }

    public void setCreditSuspFlag(boolean creditSuspFlag) {
        this.creditSuspFlag = creditSuspFlag;
    }

    @Column(name = "DEFAULTACCFLAG", nullable = true, length = 1)
    private boolean defaultAccFlag;

    public boolean getDefaultAccFlag() {
        return this.defaultAccFlag;
    }

    public void setDefaultAccFlag(boolean defaultAccFlag) {
        this.defaultAccFlag = defaultAccFlag;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "PARTICIPANTID", nullable = false)
    private ATSPRT_Participant participant;

    public ATSPRT_Participant getParticipant() {
        return this.participant;
    }

    public void setParticipant(ATSPRT_Participant participant) {
        this.participant = participant;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ACCPARENTID", nullable = true)
    private ATSACC_Account accParent;

    public ATSACC_Account getAccParent() {
        return this.accParent;
    }

    public void setAccParent(ATSACC_Account accParent) {
        this.accParent = accParent;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "accParent")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSACC_Account> accParentACC_Accounts;

    public List<ATSACC_Account> getAccParentACCAccounts() {
        return this.accParentACC_Accounts;
    }

    public void setAccParentACCAccounts(List<ATSACC_Account> accParentACC_Accounts) {
        this.accParentACC_Accounts = accParentACC_Accounts;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "refAccount")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSACC_Balance> refAccountACC_Balances;

    public List<ATSACC_Balance> getRefAccountACCBalances() {
        return this.refAccountACC_Balances;
    }

    public void setRefAccountACCBalances(List<ATSACC_Balance> refAccountACC_Balances) {
        this.refAccountACC_Balances = refAccountACC_Balances;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "refPartAcc")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSACC_Suspension> refPartAccACC_Suspensions;

    public List<ATSACC_Suspension> getRefPartAccACCSuspensions() {
        return this.refPartAccACC_Suspensions;
    }

    public void setRefPartAccACCSuspensions(List<ATSACC_Suspension> refPartAccACC_Suspensions) {
        this.refPartAccACC_Suspensions = refPartAccACC_Suspensions;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "account")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSUnwindForecastR> accountUnwindForecastR;

    public List<ATSUnwindForecastR> getAccountUnwindForecastR() {
        return this.accountUnwindForecastR;
    }

    public void setAccountUnwindForecastR(List<ATSUnwindForecastR> accountUnwindForecastR) {
        this.accountUnwindForecastR = accountUnwindForecastR;
    }

    @Override
    public String toString() {
        return "ATSACC_Account [id=" + id + ", accNumber='" + accNumber + ", accName='" + accName + ", accReference='" + accReference + ", IBAN='" + IBAN + ", debitCap=" + debitCap + ", creditCap=" + creditCap + ", debitWatermark=" + debitWatermark + ", creditWatermark=" + creditWatermark + ", debitSuspFlag=" + debitSuspFlag + ", creditSuspFlag=" + creditSuspFlag + ", defaultAccFlag=" + defaultAccFlag + ", participant=" + participant + ", accParent=" + accParent + ", currency=" + currency + ", accParentACC_Accounts=" + accParentACC_Accounts + ", refAccountACC_Balances=" + refAccountACC_Balances + ", refPartAccACC_Suspensions=" + refPartAccACC_Suspensions + ", accountUnwindForecastR=" + accountUnwindForecastR + ']';
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getAccNumber() == null) ? 0 : getAccNumber().hashCode());
        result = prime * result + ((getAccName() == null) ? 0 : getAccName().hashCode());
        result = prime * result + ((getAccReference() == null) ? 0 : getAccReference().hashCode());
        int DebitWatermark = new Long("null".equals(getDebitWatermark() + "") ? 0 : getDebitWatermark()).intValue();
        result = prime * result + (int) (DebitWatermark ^ DebitWatermark >>> 32);
        int CreditWatermark = new Long("null".equals(getCreditWatermark() + "") ? 0 : getCreditWatermark()).intValue();
        result = prime * result + (int) (CreditWatermark ^ CreditWatermark >>> 32);
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSACC_Account other = (ATSACC_Account) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}