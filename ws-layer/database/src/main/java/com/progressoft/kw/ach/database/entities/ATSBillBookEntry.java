package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name = "ATSBillBookEntries")
@XmlRootElement(name = "BillBookEntries")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBillBookEntry extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSBillBookEntry() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "SETTLEMENTDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDate;

    public java.util.Date getSettlementDate() {
        return this.settlementDate;
    }

    public void setSettlementDate(java.util.Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    @Column(name = "TXID", nullable = true, length = 35)
    private String txId;

    public String getTxId() {
        return this.txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }

    @Column(name = "AMOUNT", nullable = true, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal amount;

    public java.math.BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(java.math.BigDecimal amount) {
        this.amount = amount;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "MSGTYPEID", nullable = true)
    private ATSMSG_Type msgType;

    public ATSMSG_Type getMsgType() {
        return this.msgType;
    }

    public void setMsgType(ATSMSG_Type msgType) {
        this.msgType = msgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "DBTRACNTID", nullable = true)
    private ATSACC_Account dbtrAcnt;

    public ATSACC_Account getDbtrAcnt() {
        return this.dbtrAcnt;
    }

    public void setDbtrAcnt(ATSACC_Account dbtrAcnt) {
        this.dbtrAcnt = dbtrAcnt;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CRDTRACNTID", nullable = true)
    private ATSACC_Account crdtrAcnt;

    public ATSACC_Account getCrdtrAcnt() {
        return this.crdtrAcnt;
    }

    public void setCrdtrAcnt(ATSACC_Account crdtrAcnt) {
        this.crdtrAcnt = crdtrAcnt;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "ATSBillBookEntry [id= " + getId() + ", settlementDate= " + getSettlementDate() + ", txId= " + getTxId() + ", amount= " + getAmount() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
        result = prime * result + ((getTxId() == null) ? 0 : getTxId().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSBillBookEntry other = (ATSBillBookEntry) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}