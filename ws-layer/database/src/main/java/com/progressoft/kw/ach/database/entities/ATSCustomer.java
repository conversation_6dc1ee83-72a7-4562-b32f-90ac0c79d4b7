package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name = "ATSCustomers")
@XmlRootElement(name = "Customers")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSCustomer extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSCustomer() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "TYPE", nullable = true, length = 10)
    private String type;

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "NAME", nullable = true, length = 105)
    private String name;

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "ACCOUNT", nullable = true, length = 35)
    private String account;

    public String getAccount() {
        return this.account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    @Column(name = "IBAN", nullable = true, length = 35)
    private String iban;

    public String getIban() {
        return this.iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }

    @Column(name = "IDTYPE", nullable = true, length = 35)
    private String idType;

    public String getIdType() {
        return this.idType;
    }

    public void setIdType(String idType) {
        this.idType = idType;
    }

    @Column(name = "IDVALUE", nullable = true, length = 35)
    private String idValue;

    public String getIdValue() {
        return this.idValue;
    }

    public void setIdValue(String idValue) {
        this.idValue = idValue;
    }

    @Column(name = "IDISSUINGCOUNTRY", nullable = true, length = 35)
    private String idIssuingCountry;

    public String getIdIssuingCountry() {
        return this.idIssuingCountry;
    }

    public void setIdIssuingCountry(String idIssuingCountry) {
        this.idIssuingCountry = idIssuingCountry;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "TRANSACTIONID", nullable = true)
    private ATSTransaction transaction;

    public ATSTransaction getTransaction() {
        return this.transaction;
    }

    public void setTransaction(ATSTransaction transaction) {
        this.transaction = transaction;
    }

    @Override
    public String toString() {
        return "ATSCustomer [id= " + getId() + ", type= " + getType() + ", name= " + getName() + ", account= " + getAccount() + ", iban= " + getIban() + ", idType= " + getIdType() + ", idValue= " + getIdValue() + ", idIssuingCountry= " + getIdIssuingCountry() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getAccount() == null) ? 0 : getAccount().hashCode());
        result = prime * result + ((getIban() == null) ? 0 : getIban().hashCode());
        result = prime * result + ((getIdType() == null) ? 0 : getIdType().hashCode());
        result = prime * result + ((getIdValue() == null) ? 0 : getIdValue().hashCode());
        result = prime * result + ((getIdIssuingCountry() == null) ? 0 : getIdIssuingCountry().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSCustomer other = (ATSCustomer) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}
