package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import jakarta.persistence.Entity;
import jakarta.persistence.NamedQueries;
import jakarta.persistence.NamedQuery;
import jakarta.persistence.Table;

import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name = "JFW_SECURITY_ADT_CHNG_GRP")
@NamedQueries(@NamedQuery(name = "securityAuditForUser", query = "select sacg from ATSSecurityAUditChangeGroup sacg where sacg.id in (select max(b.id) from ATSSecurityAUditChangeGroup b where b.createdBy = :createdBy and b.actionName=:actionName) "))
@Searchable({
        @Searchable.SearchableField(fieldPath = "createdBy",label = "Created By", searchType = SearchType.EQUAL_STRING),
        @Searchable.SearchableField(fieldPath = "viewName",label = "View Name", searchType = SearchType.EQUAL_STRING),
        @Searchable.SearchableField(fieldPath = "actionName",label = "Action Name", searchType = SearchType.LIKE),
        @Searchable.SearchableField(fieldPath = "creationDate",label = "Creation Date", searchType = SearchType.BETWEEN_DATE)
})
public class ATSSecurityAUditChangeGroup extends ChangeGroup implements Serializable {

    private static final long serialVersionUID = -5152030967267906445L;


    @Override
    public Set<? extends ChangeItem> getChangeItems() {
        return Set.of();
    }

    @Override
    public void injectTenantId() {

    }
}
