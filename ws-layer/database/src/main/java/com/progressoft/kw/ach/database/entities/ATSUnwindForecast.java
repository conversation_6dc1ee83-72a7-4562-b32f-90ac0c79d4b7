package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSUnwindForecast")
@XmlRootElement(name="UnwindForecast")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSUnwindForecast extends JF<PERSON>Entity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSUnwindForecast(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="TXID", nullable=true, length=1024)
private String txId;
public String getTxId(){
return this.txId;
}
public void setTxId(String txId){
this.txId = txId;
}

@Column(name="RTXID", nullable=true, length=1024)
private String rtxId;
public String getRtxId(){
return this.rtxId;
}
public void setRtxId(String rtxId){
this.rtxId = rtxId;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=false)
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDI_Session refSession){
this.refSession = refSession;
}

@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSUnwindForecastParticipant", joinColumns = @JoinColumn(name = "UNWINDFORECAST_ID"), inverseJoinColumns = @JoinColumn(name = "PARTICIPANT_ID"))
private List<ATSPRT_Participant> participant = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getParticipant(){
return this.participant;
}
public void setParticipant(List<ATSPRT_Participant> participant){
this.participant = participant;
}

@OneToMany(fetch = FetchType.LAZY,mappedBy = "refQuery")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSUnwindForecastR> refQueryUnwindForecastR;
public List<ATSUnwindForecastR> getRefQueryUnwindForecastR(){
return this.refQueryUnwindForecastR;
}
public void setRefQueryUnwindForecastR(List<ATSUnwindForecastR> refQueryUnwindForecastR){
this.refQueryUnwindForecastR = refQueryUnwindForecastR;
}

@Override
public String toString() {
return "ATSUnwindForecast [id= " + getId() + ", txId= " + getTxId() + ", rtxId= " + getRtxId() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSUnwindForecast other = (ATSUnwindForecast) obj;
return this.hashCode() == other.hashCode();}
}


}