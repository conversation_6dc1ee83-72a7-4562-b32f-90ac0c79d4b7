package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.attachments.AttachmentItem;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;

@Entity
@Table(name = "ATSCommunicatedAuditTrailAtt")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSCommunicatedAuditTrailAtt extends AttachmentItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSCommunicatedAuditTrailAtt(){/*Default Constructor*/}


    @Override
    public void injectTenantId() {

    }

}