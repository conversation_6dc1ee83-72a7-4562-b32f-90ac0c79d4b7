package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSOUTRBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface RBatchOutRepository extends JpaRepository<ATSOUTRBatch, Long> {
    @Query("SELECT b FROM ATSOUTRBatch b " +
            "JOIN b.state s " +
            "WHERE b.batchId = :batchId " +
            "AND s.code IN :stateCode ")
    ATSOUTRBatch findByBatchIdAndState(
            @Param("batchId") String batchId,
            @Param("stateCode") String stateCode);
}
