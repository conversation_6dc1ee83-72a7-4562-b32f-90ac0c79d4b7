package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSMSG_Reasons",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="MSG_Reasons")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMSG_Reason extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMSG_Reason(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="ISSYSTEM", nullable=true, length=1)
private boolean isSystem;
public boolean getIsSystem(){
return this.isSystem;
}
public void setIsSystem(boolean isSystem){
this.isSystem = isSystem;
}

@Column(name="ISFORCED", nullable=true, length=1)
private boolean isForced;
public boolean getIsForced(){
return this.isForced;
}
public void setIsForced(boolean isForced){
this.isForced = isForced;
}

@Column(name="INSTRUCTEDAWARE", nullable=true, length=1)
private boolean instructedAware;
public boolean getInstructedAware(){
return this.instructedAware;
}
public void setInstructedAware(boolean instructedAware){
this.instructedAware = instructedAware;
}

@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSMSG_ReasonsMessageslink", joinColumns = @JoinColumn(name = "MSG_REASONS_ID"), inverseJoinColumns = @JoinColumn(name = "MESSAGESLINK_ID"))
private List<ATSMSG_TypesLink> messagesLink = new ArrayList<ATSMSG_TypesLink>();
public List<ATSMSG_TypesLink> getMessagesLink(){
	return this.messagesLink;
}
public void setMessagesLink(List<ATSMSG_TypesLink> messagesLink){
		this.messagesLink = messagesLink;
	}

@Override
public String toString() {
return "ATSMSG_Reason [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", isSystem= " + getIsSystem() + ", isForced= " + getIsForced() + ", instructedAware= " + getInstructedAware() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMSG_Reason other = (ATSMSG_Reason) obj;
return this.hashCode() == other.hashCode();}
}


}