package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSACC_Account;
import com.progressoft.kw.ach.database.entities.ATSACC_Balance;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Date;
import java.util.List;

public interface BalanceRepository extends JpaRepository<ATSACC_Balance, Long> {
    List<ATSACC_Balance> findByRefAccountAndSettlementDtBetween(ATSACC_Account account, Date settlementDateFrom, Date settlementDateTo);

    @Query("SELECT b FROM ATSACC_Balance b JOIN b.refAccount a JOIN a.participant p WHERE p.code = :participantCode ORDER BY b.id DESC")
    List<ATSACC_Balance> getByParticipantCode(@Param("participantCode") String participantCode);

    @Query(nativeQuery = true, value = """ 
    SELECT b.* FROM ATSACC_Balances b JOIN ATSACC_Accounts ra ON b.REFACCOUNTID = ra.ID JOIN ATSBDI_Sessions s ON b.REFSESSIONID = s.ID JOIN ATSPRT_Participants p ON ra.PARTICIPANTID = p.ID 
    WHERE REGEXP_SUBSTR(s.CURRPERIOD, '[^- ]+', 1, 2) IN ('Start', 'Exchange', 'Cut')AND p.CODE = :participantCode""")
    List<ATSACC_Balance> findByParticipantCode(@Param("participantCode") String participantCode);


    @Query(nativeQuery = true, value = """
            SELECT b.* FROM ATSACC_Balances b JOIN ATSBDI_Sessions s ON b.REFSESSIONID = s.ID
            WHERE REGEXP_SUBSTR(s.CURRPERIOD, '[^- ]+', 1, 2) IN ('Start', 'Exchange', 'Cut') AND REFACCOUNTID = :participantId""")
    List<ATSACC_Balance> getBalancesByAccountIdWithUnsettledSession(@Param("participantId") Long participantId);

}
