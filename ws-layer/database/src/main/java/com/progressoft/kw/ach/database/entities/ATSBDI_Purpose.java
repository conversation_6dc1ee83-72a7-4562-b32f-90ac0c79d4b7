package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSBDI_Purposes")
@XmlRootElement(name="BDI_Purposes")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_Purpose extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_Purpose(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFWINDOWID", nullable=true)
private ATSBDI_Window refWindow;
public ATSBDI_Window getRefWindow(){
return this.refWindow;
}
public void setRefWindow(ATSBDI_Window refWindow){
this.refWindow = refWindow;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CATEGORYPURPOSEID", nullable=false)
private ATSMSG_CtgPurp categoryPurpose;
public ATSMSG_CtgPurp getCategoryPurpose(){
return this.categoryPurpose;
}
public void setCategoryPurpose(ATSMSG_CtgPurp categoryPurpose){
this.categoryPurpose = categoryPurpose;
}

@Override
public String toString() {
return "ATSBDI_Purpose [id= " + getId() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_Purpose other = (ATSBDI_Purpose) obj;
return this.hashCode() == other.hashCode();}
}


}