package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSReportRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface ReportRepository extends JpaRepository<ATSReportRecord, Long> {
    @Query("SELECT r.reportCommunicationAutoIsPulled AND r.reportSettlementAutoIsPulled FROM ATSReportRecord r WHERE r.id = :reportId")
    Boolean isReportAlreadyPulled(@Param("reportId") Long reportId);

    @Modifying
    @Query("UPDATE ATSReportRecord r SET r.reportCommunicationAutoIsPulled = true, r.reportSettlementAutoIsPulled=true WHERE r.id = :reportId")
    int updateReportPulledStatus(@Param("reportId") Long reportId);
}
