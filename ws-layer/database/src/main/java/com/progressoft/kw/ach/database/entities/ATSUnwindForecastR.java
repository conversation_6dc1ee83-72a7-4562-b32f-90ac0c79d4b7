package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSUnwindForecastR")
@XmlRootElement(name="UnwindForecastR")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSUnwindForecastR extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSUnwindForecastR(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="CURRENTBALANCE", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal currentBalance;
public java.math.BigDecimal getCurrentBalance(){
return this.currentBalance;
}
public void setCurrentBalance(java.math.BigDecimal currentBalance){
this.currentBalance = currentBalance;
}

@Column(name="TTLUNWINDDBTX", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal ttlUnwindDbTx;
public java.math.BigDecimal getTtlUnwindDbTx(){
return this.ttlUnwindDbTx;
}
public void setTtlUnwindDbTx(java.math.BigDecimal ttlUnwindDbTx){
this.ttlUnwindDbTx = ttlUnwindDbTx;
}

@Column(name="TTLUNWINDCRTX", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal ttlUnwindCrTx;
public java.math.BigDecimal getTtlUnwindCrTx(){
return this.ttlUnwindCrTx;
}
public void setTtlUnwindCrTx(java.math.BigDecimal ttlUnwindCrTx){
this.ttlUnwindCrTx = ttlUnwindCrTx;
}

@Column(name="TTLUNWINDDBRTX", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal ttlUnwindDbRtx;
public java.math.BigDecimal getTtlUnwindDbRtx(){
return this.ttlUnwindDbRtx;
}
public void setTtlUnwindDbRtx(java.math.BigDecimal ttlUnwindDbRtx){
this.ttlUnwindDbRtx = ttlUnwindDbRtx;
}

@Column(name="TTLUNWINDCRRTX", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal ttlUnwindCrRtx;
public java.math.BigDecimal getTtlUnwindCrRtx(){
return this.ttlUnwindCrRtx;
}
public void setTtlUnwindCrRtx(java.math.BigDecimal ttlUnwindCrRtx){
this.ttlUnwindCrRtx = ttlUnwindCrRtx;
}

@Column(name="FORECASTEDBALANCE", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal forecastedBalance;
public java.math.BigDecimal getForecastedBalance(){
return this.forecastedBalance;
}
public void setForecastedBalance(java.math.BigDecimal forecastedBalance){
this.forecastedBalance = forecastedBalance;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFQUERYID", nullable=false)
private ATSUnwindForecast refQuery;
public ATSUnwindForecast getRefQuery(){
return this.refQuery;
}
public void setRefQuery(ATSUnwindForecast refQuery){
this.refQuery = refQuery;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=true)
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="ACCOUNTID", nullable=true)
private ATSACC_Account account;

    public ATSACC_Account getAccount() {
        return this.account;
    }

    public void setAccount(ATSACC_Account account) {
        this.account = account;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "ATSUnwindForecastR [id= " + getId() + ", currentBalance= " + getCurrentBalance() + ", ttlUnwindDbTx= " + getTtlUnwindDbTx() + ", ttlUnwindCrTx= " + getTtlUnwindCrTx() + ", ttlUnwindDbRtx= " + getTtlUnwindDbRtx() + ", ttlUnwindCrRtx= " + getTtlUnwindCrRtx() + ", forecastedBalance= " + getForecastedBalance() + "]";
    }

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSUnwindForecastR other = (ATSUnwindForecastR) obj;
return this.hashCode() == other.hashCode();}
}


}