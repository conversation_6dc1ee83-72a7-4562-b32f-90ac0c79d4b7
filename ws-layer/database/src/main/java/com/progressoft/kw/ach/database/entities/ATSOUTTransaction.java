package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity
@Table(name = "ATSTransactionsOut",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"TXID", "Z_TENANT_ID"})})
@XmlRootElement(name = "Transactions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSOUTTransaction extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSOUTTransaction() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "TXID", nullable = true, length = 35)
    private String txId;

    public String getTxId() {
        return this.txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }

    @Column(name = "INSTID", nullable = true, length = 35)
    private String instId;

    public String getInstId() {
        return this.instId;
    }

    public void setInstId(String instId) {
        this.instId = instId;
    }

    @Column(name = "E2EID", nullable = true, length = 35)
    private String e2eId;

    public String getE2eId() {
        return this.e2eId;
    }

    public void setE2eId(String e2eId) {
        this.e2eId = e2eId;
    }

    @Column(name = "AMOUNT", nullable = true, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal amount;

    public java.math.BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(java.math.BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "ADDITIONALINFO", nullable = true, length = 4000)
    private String additionalInfo;

    public String getAdditionalInfo() {
        return this.additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Column(name = "HASHCODEINFO", nullable = true, length = 1)
    private long hashCodeInfo;

    public long getHashCodeInfo() {
        return this.hashCodeInfo;
    }

    public void setHashCodeInfo(long hashCodeInfo) {
        this.hashCodeInfo = hashCodeInfo;
    }

    @Column(name = "ISCHARGED", nullable = true, length = 1)
    private boolean isCharged;

    public boolean getIsCharged() {
        return this.isCharged;
    }

    public void setIsCharged(boolean isCharged) {
        this.isCharged = isCharged;
    }

    @Column(name = "ISDUPLICATE", nullable = true, length = 1)
    private boolean isDuplicate;

    public boolean getIsDuplicate() {
        return this.isDuplicate;
    }

    public void setIsDuplicate(boolean isDuplicate) {
        this.isDuplicate = isDuplicate;
    }

    @Column(name = "MANDATEID", nullable = true, length = 35)
    private String mandateId;

    public String getMandateId() {
        return this.mandateId;
    }

    public void setMandateId(String mandateId) {
        this.mandateId = mandateId;
    }

    @Column(name = "MANDATECOLDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date mandateColDate;

    public java.util.Date getMandateColDate() {
        return this.mandateColDate;
    }

    public void setMandateColDate(java.util.Date mandateColDate) {
        this.mandateColDate = mandateColDate;
    }

    @Column(name = "COLLECTIONDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date collectionDate;

    public java.util.Date getCollectionDate() {
        return this.collectionDate;
    }

    public void setCollectionDate(java.util.Date collectionDate) {
        this.collectionDate = collectionDate;
    }

    @Column(name = "PAYMENTSEQUENCE", nullable = true, length = 1)
    private long paymentSequence;

    public long getPaymentSequence() {
        return this.paymentSequence;
    }

    public void setPaymentSequence(long paymentSequence) {
        this.paymentSequence = paymentSequence;
    }

    @Column(name = "REMITTANCE", nullable = true, length = 4000)
    private String remittance;

    public String getRemittance() {
        return this.remittance;
    }

    public void setRemittance(String remittance) {
        this.remittance = remittance;
    }

    @Column(name = "SOURCE_TRANSACTION_ID", nullable = false, length = 100)
    private String sourceTransactionId;

    public String getSourceTransactionId() {
        return sourceTransactionId;
    }

    public void setSourceTransactionId(String sourceTransactionId) {
        this.sourceTransactionId = sourceTransactionId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "BATCHID", nullable = false)
    private ATSOUTBatch batch;

    public ATSOUTBatch getBatch() {
        return this.batch;
    }

    public void setBatch(ATSOUTBatch batch) {
        this.batch = batch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "MSGTYPEID", nullable = true)
    private ATSMSG_Type msgType;

    public ATSMSG_Type getMsgType() {
        return this.msgType;
    }

    public void setMsgType(ATSMSG_Type msgType) {
        this.msgType = msgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGPARTIID", nullable = true)
    private ATSPRT_Participant instgParti;

    public ATSPRT_Participant getInstgParti() {
        return this.instgParti;
    }

    public void setInstgParti(ATSPRT_Participant instgParti) {
        this.instgParti = instgParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDPARTIID", nullable = true)
    private ATSPRT_Participant instdParti;

    public ATSPRT_Participant getInstdParti() {
        return this.instdParti;
    }

    public void setInstdParti(ATSPRT_Participant instdParti) {
        this.instdParti = instdParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "DBTRPARTIID", nullable = true)
    private ATSPRT_Participant dbtrParti;

    public ATSPRT_Participant getDbtrParti() {
        return this.dbtrParti;
    }

    public void setDbtrParti(ATSPRT_Participant dbtrParti) {
        this.dbtrParti = dbtrParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CRDTRPARTIID", nullable = true)
    private ATSPRT_Participant crdtrParti;

    public ATSPRT_Participant getCrdtrParti() {
        return this.crdtrParti;
    }

    public void setCrdtrParti(ATSPRT_Participant crdtrParti) {
        this.crdtrParti = crdtrParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGBRANCHID", nullable = true)
    private ATSPRT_Branch instgBranch;

    public ATSPRT_Branch getInstgBranch() {
        return this.instgBranch;
    }

    public void setInstgBranch(ATSPRT_Branch instgBranch) {
        this.instgBranch = instgBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDBRANCHID", nullable = true)
    private ATSPRT_Branch instdBranch;

    public ATSPRT_Branch getInstdBranch() {
        return this.instdBranch;
    }

    public void setInstdBranch(ATSPRT_Branch instdBranch) {
        this.instdBranch = instdBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "DBTRBRANCHID", nullable = true)
    private ATSPRT_Branch dbtrBranch;

    public ATSPRT_Branch getDbtrBranch() {
        return this.dbtrBranch;
    }

    public void setDbtrBranch(ATSPRT_Branch dbtrBranch) {
        this.dbtrBranch = dbtrBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CRDTRBRANCHID", nullable = true)
    private ATSPRT_Branch crdtrBranch;

    public ATSPRT_Branch getCrdtrBranch() {
        return this.crdtrBranch;
    }

    public void setCrdtrBranch(ATSPRT_Branch crdtrBranch) {
        this.crdtrBranch = crdtrBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CATPURPID", nullable = true)
    private ATSMSG_CtgPurp catPurp;

    public ATSMSG_CtgPurp getCatPurp() {
        return this.catPurp;
    }

    public void setCatPurp(ATSMSG_CtgPurp catPurp) {
        this.catPurp = catPurp;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "TXPURPID", nullable = true)
    private ATSMSG_TransPurp txPurp;

    public ATSMSG_TransPurp getTxPurp() {
        return this.txPurp;
    }

    public void setTxPurp(ATSMSG_TransPurp txPurp) {
        this.txPurp = txPurp;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "STATEID", nullable = true)
    private ATSState state;

    public ATSState getState() {
        return this.state;
    }

    public void setState(ATSState state) {
        this.state = state;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REASONID", nullable = true)
    private ATSMSG_Reason reason;

    public ATSMSG_Reason getReason() {
        return this.reason;
    }

    public void setReason(ATSMSG_Reason reason) {
        this.reason = reason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "WINDOWID", nullable = true)
    private ATSBDI_Window window;

    public ATSBDI_Window getWindow() {
        return this.window;
    }

    public void setWindow(ATSBDI_Window window) {
        this.window = window;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSCustomer> transactionCustomers;

    public List<ATSCustomer> getTransactionCustomers() {
        return this.transactionCustomers;
    }

    public void setTransactionCustomers(List<ATSCustomer> transactionCustomers) {
        this.transactionCustomers = transactionCustomers;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRTransaction> orgTxRTransactions;

    public List<ATSRTransaction> getOrgTxRTransactions() {
        return this.orgTxRTransactions;
    }

    public void setOrgTxRTransactions(List<ATSRTransaction> orgTxRTransactions) {
        this.orgTxRTransactions = orgTxRTransactions;
    }

    @Override
    public String toString() {
        return "ATSOUTTransaction [id= " + getId() + ", txId= " + getTxId() + ", instId= " + getInstId() + ", e2eId= " + getE2eId() + ", amount= " + getAmount() + ", additionalInfo= " + getAdditionalInfo() + ", hashCodeInfo= " + getHashCodeInfo() + ", isCharged= " + getIsCharged() + ", isDuplicate= " + getIsDuplicate() + ", mandateId= " + getMandateId() + ", mandateColDate= " + getMandateColDate() + ", collectionDate= " + getCollectionDate() + ", paymentSequence= " + getPaymentSequence() + ", remittance= " + getRemittance() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getTxId() == null) ? 0 : getTxId().hashCode());
        result = prime * result + ((getInstId() == null) ? 0 : getInstId().hashCode());
        result = prime * result + ((getE2eId() == null) ? 0 : getE2eId().hashCode());
        result = prime * result + ((getAdditionalInfo() == null) ? 0 : getAdditionalInfo().hashCode());
        int HashCodeInfo = new Long("null".equals(getHashCodeInfo() + "") ? 0 : getHashCodeInfo()).intValue();
        result = prime * result + (int) (HashCodeInfo ^ HashCodeInfo >>> 32);
        result = prime * result + ((getMandateId() == null) ? 0 : getMandateId().hashCode());
        result = prime * result + ((getMandateColDate() == null) ? 0 : getMandateColDate().hashCode());
        result = prime * result + ((getCollectionDate() == null) ? 0 : getCollectionDate().hashCode());
        int PaymentSequence = new Long("null".equals(getPaymentSequence() + "") ? 0 : getPaymentSequence()).intValue();
        result = prime * result + (int) (PaymentSequence ^ PaymentSequence >>> 32);
        result = prime * result + ((getRemittance() == null) ? 0 : getRemittance().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSOUTTransaction other = (ATSOUTTransaction) obj;
            return this.hashCode() == other.hashCode();
        }
    }

}