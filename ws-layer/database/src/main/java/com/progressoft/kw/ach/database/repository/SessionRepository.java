package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSBDI_Session;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Date;
import java.util.List;
import java.util.Optional;

public interface SessionRepository extends JpaRepository<ATSBDI_Session, Long> {
    List<ATSBDI_Session> getByBusinessDt(Date businessDt);

    Optional<ATSBDI_Session> findBySessionSeq(String sessionSeq);
    
    @Query("SELECT s FROM ATSBDI_Session s WHERE s.currPeriod LIKE '%Exchange%'")
    List<ATSBDI_Session> findSessionsWithExchangePeriod();
}
