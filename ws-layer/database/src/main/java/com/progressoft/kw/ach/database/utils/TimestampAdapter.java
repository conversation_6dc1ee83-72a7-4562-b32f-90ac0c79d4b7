package com.progressoft.kw.ach.database.utils;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.sql.Timestamp;
import java.util.GregorianCalendar;

/**
 *
 *
 */
public class TimestampAdapter extends XmlAdapter<XMLGregorianCalendar, Timestamp> {

	@Override
	public XMLGregorianCalendar marshal(Timestamp timestamp) throws Exception {
		GregorianCalendar c = new GregorianCalendar();
		c.setTime(timestamp);
		return DatatypeFactory.newInstance().newXMLGregorianCalendar(c);

	}

	@Override
	public Timestamp unmarshal(XMLGregorianCalendar calendar) throws Exception {
		return new Timestamp(calendar.toGregorianCalendar().getTime().getTime());
	}

}