package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.kw.ach.database.utils.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSBDI_Periods")
@XmlRootElement(name="BDI_Periods")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_Period extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_Period(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="CODE", nullable=false, length=19)
private String code;
public String getCode(){
return this.code;
}
public void setCode(String code){
this.code = code;
}

@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODSTART", nullable=false, length=34)
private java.sql.Timestamp periodStart;
public java.sql.Timestamp getPeriodStart(){
return this.periodStart;
}
public void setPeriodStart(java.sql.Timestamp periodStart){
this.periodStart = periodStart;
}

@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODEND", nullable=false, length=34)
private java.sql.Timestamp periodEnd;
public java.sql.Timestamp getPeriodEnd(){
return this.periodEnd;
}
public void setPeriodEnd(java.sql.Timestamp periodEnd){
this.periodEnd = periodEnd;
}

@Column(name="READYFLAG", nullable=false, length=1)
private boolean readyFlag;
public boolean getReadyFlag(){
return this.readyFlag;
}
public void setReadyFlag(boolean readyFlag){
this.readyFlag = readyFlag;
}

@Column(name="EXECUTIONSTATE", nullable=true, length=100)
private String executionState;
public String getExecutionState(){
return this.executionState;
}
public void setExecutionState(String executionState){
this.executionState = executionState;
}

@Column(name="DETAILS", nullable=true, length=4000)
private String details;
public String getDetails(){
return this.details;
}
public void setDetails(String details){
this.details = details;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=true)
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDI_Session refSession){
this.refSession = refSession;
}

@OneToMany(fetch = FetchType.LAZY,mappedBy = "refPeriod")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSBDI_Window> refPeriodBDI_Windows;
public List<ATSBDI_Window> getRefPeriodBDIWindows(){
return this.refPeriodBDI_Windows;
}
public void setRefPeriodBDIWindows(List<ATSBDI_Window> refPeriodBDI_Windows){
this.refPeriodBDI_Windows = refPeriodBDI_Windows;
}

@Override
public String toString() {
return "ATSBDI_Period [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", readyFlag= " + getReadyFlag() + ", executionState= " + getExecutionState() + ", details= " + getDetails() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getExecutionState() == null) ? 0 : getExecutionState().hashCode());
result = prime * result + ((getDetails() == null) ? 0 : getDetails().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_Period other = (ATSBDI_Period) obj;
return this.hashCode() == other.hashCode();}
}


}