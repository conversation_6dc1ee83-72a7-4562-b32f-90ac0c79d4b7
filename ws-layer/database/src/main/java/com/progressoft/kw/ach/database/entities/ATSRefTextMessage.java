package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRefTextMessages")
@XmlRootElement(name="RefTextMessages")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRefTextMessage extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRefTextMessage(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String STATUS = "status";
@Column(name="STATUS", nullable=false, length=500)
private String status;
public String getStatus(){
return this.status;
}
public void setStatus(String status){
this.status = status;
}

public static final String MESSAGE_TYPE = "messageType";
@Column(name="MESSAGETYPE", nullable=false, length=30)
private String messageType;
public String getMessageType(){
return this.messageType;
}
public void setMessageType(String messageType){
this.messageType = messageType;
}

public static final String RETRY_COUNT = "retryCount";
@Column(name="RETRYCOUNT", nullable=false, length=30)
private long retryCount;
public long getRetryCount(){
return this.retryCount;
}
public void setRetryCount(long retryCount){
this.retryCount = retryCount;
}

public static final String REF_PARTICIPANT = "refParticipant";
@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTICIPANTID", nullable=false)
private ATSPRT_Participant refParticipant;
public ATSPRT_Participant getRefParticipant(){
return this.refParticipant;
}
public void setRefParticipant(ATSPRT_Participant refParticipant){
this.refParticipant = refParticipant;
}

public static final String REF_TEXT_MESSAGE = "refTextMessage";
@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFTEXTMESSAGEID", nullable=false)
private ATSTextMessage refTextMessage;
public ATSTextMessage getRefTextMessage(){
return this.refTextMessage;
}
public void setRefTextMessage(ATSTextMessage refTextMessage){
this.refTextMessage = refTextMessage;
}

    @Override
    public void injectTenantId() {

    }

@Override
public String toString() {
return "ATSRefTextMessage [id= " + getId() + ", status= " + getStatus() + ", messageType= " + getMessageType() + ", retryCount= " + getRetryCount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
int RetryCount= new Long("null".equals(getRetryCount() + "") ? 0 : getRetryCount()).intValue();
result = prime * result + (int) (RetryCount ^ RetryCount >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRefTextMessage other = (ATSRefTextMessage) obj;
return this.hashCode() == other.hashCode();}
}


}