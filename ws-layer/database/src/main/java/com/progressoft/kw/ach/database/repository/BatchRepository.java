package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSBatch;
import com.progressoft.kw.ach.database.entities.ATSPRT_Participant;
import com.progressoft.kw.ach.database.entities.ATSState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Date;
import java.util.List;

public interface BatchRepository extends JpaRepository<ATSBatch, Long> {
    List<ATSBatch> findAllByStateAndComPartiAndCreationDateIsBetween(ATSState state, ATSPRT_Participant comParti, Date settlementDate, Date settlementDate2);
}


