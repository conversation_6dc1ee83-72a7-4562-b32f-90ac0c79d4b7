package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSRpt_NCP;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface NCPReportRepository extends JpaRepository<ATSRpt_NCP, Long> {

    @Query("SELECT n FROM ATSRpt_NCP n JOIN ATSPRT_Participant p ON n.settAgent.id = p.id WHERE p.code = :code AND n.isPulled = false ORDER BY n.id DESC")
    List<ATSRpt_NCP> findByAgentCode(@Param("code") String code);

    @Modifying
    @Query("UPDATE ATSRpt_NCP n SET n.isPulled = true WHERE n.id = :reportId")
    int updateRptNcpPulledStatus(@Param("reportId") Long reportId);

    @Query("SELECT n.isPulled FROM ATSRpt_NCP n WHERE n.id = :reportId")
    Boolean isNcpAlreadyPulled(@Param("reportId") Long reportId);
}
