package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.kw.ach.database.utils.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSRBatches",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"BATCHID", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "RBatches")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRBatch extends JFWEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public ATSRBatch() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "BATCHID", nullable = true, length = 35)
    private String batchId;

    public String getBatchId() {
        return this.batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    @Column(name = "COUNT", nullable = true, length = 10)
    private long count;

    public long getCount() {
        return this.count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    @XmlJavaTypeAdapter(TimestampAdapter.class)
    @Column(name = "CREATIONDT", nullable = true, length = 34)
    private java.sql.Timestamp creationDt;

    public java.sql.Timestamp getCreationDt() {
        return this.creationDt;
    }

    public void setCreationDt(java.sql.Timestamp creationDt) {
        this.creationDt = creationDt;
    }

    @Column(name = "ADDITIONALINFO", nullable = true, length = 4000)
    private String additionalInfo;

    public String getAdditionalInfo() {
        return this.additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Column(name = "ORGADDITIONALINFO", nullable = true, length = 4000)
    private String orgAdditionalInfo;

    public String getOrgAdditionalInfo() {
        return this.orgAdditionalInfo;
    }

    public void setOrgAdditionalInfo(String orgAdditionalInfo) {
        this.orgAdditionalInfo = orgAdditionalInfo;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "MSGTYPEID", nullable = true)
    private ATSMSG_Type msgType;

    public ATSMSG_Type getMsgType() {
        return this.msgType;
    }

    public void setMsgType(ATSMSG_Type msgType) {
        this.msgType = msgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGMSGTYPEID", nullable = true)
    private ATSMSG_Type orgMsgType;

    public ATSMSG_Type getOrgMsgType() {
        return this.orgMsgType;
    }

    public void setOrgMsgType(ATSMSG_Type orgMsgType) {
        this.orgMsgType = orgMsgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SOURCEID", nullable = true)
    private ATSLKP_BatchSource source;

    public ATSLKP_BatchSource getSource() {
        return this.source;
    }

    public void setSource(ATSLKP_BatchSource source) {
        this.source = source;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGBATCHID", nullable = true)
    private ATSBatch orgBatch;

    public ATSBatch getOrgBatch() {
        return this.orgBatch;
    }

    public void setOrgBatch(ATSBatch orgBatch) {
        this.orgBatch = orgBatch;
    }

    @OneToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="PENDINGPAYMENTID", nullable = true)
    private ATSPendingPayment pendingPayment;
    public ATSPendingPayment getPendingPayment() {
        return pendingPayment;
    }
    public void setPendingPayment(ATSPendingPayment pendingPayment) {
        this.pendingPayment = pendingPayment;
    }

    @Column(name = "ISPULLED")
    private Boolean isPulled;

    public Boolean getIsPulled() {
        return isPulled;
    }

    public void setIsPulled(Boolean isPulled) {
        this.isPulled = isPulled;
    }

    @Column(name = "RcvdForInstdAgent")
    private Boolean isRcvdForInstdAgent;

    public Boolean getIsRcvdForInstdAgent() {
        return isRcvdForInstdAgent;
    }

    public void setIsRcvdForInstdAgent(Boolean isRcvdForInstdAgent) {
        this.isRcvdForInstdAgent = isRcvdForInstdAgent;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGRBATCHID", nullable = true)
    private ATSRBatch orgRBatch;

    public ATSRBatch getOrgRBatch() {
        return this.orgRBatch;
    }

    public void setOrgRBatch(ATSRBatch orgRBatch) {
        this.orgRBatch = orgRBatch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGPARTIID", nullable = true)
    private ATSPRT_Participant instgParti;

    public ATSPRT_Participant getInstgParti() {
        return this.instgParti;
    }

    public void setInstgParti(ATSPRT_Participant instgParti) {
        this.instgParti = instgParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDPARTIID", nullable = true)
    private ATSPRT_Participant instdParti;

    public ATSPRT_Participant getInstdParti() {
        return this.instdParti;
    }

    public void setInstdParti(ATSPRT_Participant instdParti) {
        this.instdParti = instdParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMPARTIID", nullable = true)
    private ATSPRT_Participant comParti;

    public ATSPRT_Participant getComParti() {
        return this.comParti;
    }

    public void setComParti(ATSPRT_Participant comParti) {
        this.comParti = comParti;
    }

    public static final String PROCESSING_STATUS = "processingStatus";
    @Column(name="PROCESSINGSTATUS", nullable=true, length=255)
    private String processingStatus;
    public String getProcessingStatus(){
        return this.processingStatus;
    }
    public void setProcessingStatus(String processingStatus){
        this.processingStatus = processingStatus;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "STATEID", nullable = true)
    private ATSState state;

    public ATSState getState() {
        return this.state;
    }

    public void setState(ATSState state) {
        this.state = state;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REASONID", nullable = true)
    private ATSMSG_Reason reason;

    public ATSMSG_Reason getReason() {
        return this.reason;
    }

    public void setReason(ATSMSG_Reason reason) {
        this.reason = reason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGSTATEID", nullable = true)
    private ATSState orgState;

    public ATSState getOrgState() {
        return this.orgState;
    }

    public void setOrgState(ATSState orgState) {
        this.orgState = orgState;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "ORGREASONID", nullable = true)
    private ATSMSG_Reason orgReason;

    public ATSMSG_Reason getOrgReason() {
        return this.orgReason;
    }

    public void setOrgReason(ATSMSG_Reason orgReason) {
        this.orgReason = orgReason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "WINDOWID", nullable = true)
    private ATSBDI_Window window;

    public ATSBDI_Window getWindow() {
        return this.window;
    }

    public void setWindow(ATSBDI_Window window) {
        this.window = window;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "orgRBatch")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRBatch> orgRBatchRBatches;

    public List<ATSRBatch> getOrgRBatchRBatches() {
        return this.orgRBatchRBatches;
    }

    public void setOrgRBatchRBatches(List<ATSRBatch> orgRBatchRBatches) {
        this.orgRBatchRBatches = orgRBatchRBatches;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "batch")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRTransaction> batchRTransactions;

    public List<ATSRTransaction> getBatchRTransactions() {
        return this.batchRTransactions;
    }

    public void setBatchRTransactions(List<ATSRTransaction> batchRTransactions) {
        this.batchRTransactions = batchRTransactions;
    }

    public static final String IS_SPLITTED = "isSplitted";
    @Column(name="ISSPLITTED", nullable=true, length=1)
    private boolean isSplitted;
    public boolean getIsSplitted(){
        return this.isSplitted;
    }
    public void setIsSplitted(boolean isSplitted){
        this.isSplitted = isSplitted;
    }

    public static final String PULLED_DATE = "pulledDate";
    @XmlJavaTypeAdapter(com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter.class)
    @Column(name="PULLEDDATE", nullable=true, length=34)
    private java.sql.Timestamp pulledDate;
    public java.sql.Timestamp getPulledDate(){
        return this.pulledDate;
    }
    public void setPulledDate(java.sql.Timestamp pulledDate){
        this.pulledDate = pulledDate;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSRBatch [id= " + getId() + ", batchId= " + getBatchId() + ", count= " + getCount() + ", creationDt= " + getCreationDt() + ", additionalInfo= " + getAdditionalInfo() + ", orgAdditionalInfo= " + getOrgAdditionalInfo() + ", isSplitted= " + getIsSplitted() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
        int Count = new Long("null".equals(getCount() + "") ? 0 : getCount()).intValue();
        result = prime * result + (int) (Count ^ Count >>> 32);
        result = prime * result + ((getAdditionalInfo() == null) ? 0 : getAdditionalInfo().hashCode());
        result = prime * result + ((getOrgAdditionalInfo() == null) ? 0 : getOrgAdditionalInfo().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSRBatch other = (ATSRBatch) obj;
            return this.hashCode() == other.hashCode();
        }
    }

}
