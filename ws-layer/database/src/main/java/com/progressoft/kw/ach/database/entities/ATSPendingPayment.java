package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "ATSPENDINGPAYMENT")
@XmlRootElement(name = "Batches")
@XmlAccessorType(XmlAccessType.FIELD)
@Searchable({
        @Searchable.SearchableField(fieldPath = "SETTLEMENTDATE", label = "settlementdate_200000297", searchType = SearchType.BETWEEN_DATE),
        @Searchable.SearchableField(fieldPath = "messageId", label ="messageid_600000235", searchType = SearchType.LIKE),
        @Searchable.SearchableField(fieldPath = "Z_CREATION_DATE", label = "creationdate_200000557", searchType = SearchType.BETWEEN_DATE)
})
public class ATSPendingPayment extends JFWEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    public ATSPendingPayment() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String MESSAGE_ID = "messageId";
    @Column(name = "MESSAGEID", nullable = true, length = 50)
    private String messageId;

    public String getMessageId() {
        return this.messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    public static final String TYPE = "type";
    @Column(name = "TYPE", nullable = true, length = 50)
    private String type;

    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static final String SETTLEMENT_DATE = "settlementDate";
    @Column(name = "SETTLEMENTDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDate;

    public java.util.Date getSettlementDate() {
        return this.settlementDate;
    }

    public void setSettlementDate(java.util.Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    public static final String STATUS = "status";
    @Column(name="STATUS", nullable=true, length=255)
    private String status;
    public String getStatus(){
        return this.status;
    }
    public void setStatus(String status){
        this.status = status;
    }

    public static final String PARTICIPANT = "participant";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "PARTICIPANTID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant participant;

    public ATSPRT_Participant getParticipant() {
        return this.participant;
    }

    public void setParticipant(ATSPRT_Participant participant) {
        this.participant = participant;
    }

    public static final String MSG_TYPE = "msgType";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="MSGTYPEID", nullable=true)
    @WithValueProvider(jupiterValueProviderBean = "allMessageTypeProvider", keyProperty = "id")
    private ATSMSG_Type msgType;
    public ATSMSG_Type getMsgType(){
        return this.msgType;
    }
    public void setMsgType(ATSMSG_Type msgType){
        this.msgType = msgType;
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ATSPendingPayment that)) return false;
        return id == that.id && Objects.equals(messageId, that.messageId) && Objects.equals(type, that.type) && Objects.equals(settlementDate, that.settlementDate) && Objects.equals(status, that.status) && Objects.equals(participant, that.participant) && Objects.equals(msgType, that.msgType);
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getMessageId() == null) ? 0 : getMessageId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getParticipant() == null) ? 0 : getParticipant().hashCode());
        result = prime * result + ((getMsgType() == null) ? 0 : getMsgType().hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "ATSPendingPayment{" +
                "id=" + id +
                ", messageId='" + messageId + '\'' +
                ", type='" + type + '\'' +
                ", settlementDate=" + settlementDate +
                ", status='" + status + '\'' +
                ", participant=" + participant +
                ", msgType=" + msgType +
                '}';
    }
}

