package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity

@Table(name="ATSACC_Internal_Accounts")
@XmlRootElement(name="ACC_Internal_Accounts")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )

public class ATSACC_Internal_Account extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSACC_Internal_Account(){/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @SequenceGenerator(name = "SEQ_ATSACC_Internal_Accounts", sequenceName = "SEQ_ATSACC_Internal_Accounts", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="SEQ_ATSACC_Internal_Accounts")
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    public long getId(){
    return this.id;
    }
    public void setId(long id){
    this.id = id;
    }

    public static final String ACC_NUMBER = "accNumber";
    @Column(name="ACCNUMBER", nullable=false, length=255)
    private String accNumber;
    public String getAccNumber(){
    return this.accNumber;
    }
    public void setAccNumber(String accNumber){
    this.accNumber = accNumber;
    }

    public static final String ACC_NAME = "accName";
    @Column(name="ACCNAME", nullable=true, length=255)
    private String accName;
    public String getAccName(){
    return this.accName;
    }
    public void setAccName(String accName){
    this.accName = accName;
    }

    @Column(name="ISOLDACCOUNT", nullable=true)
    private boolean isOldAccount;
    public boolean getIsOldAccount() {
        return isOldAccount;
    }
    public void setIsOldAccount(boolean isOldAccount) {
        this.isOldAccount = isOldAccount;
    }

    @Override
    public void injectTenantId() {}

    @Override
    public String toString() {
        return "ATSACC_Internal_Account{" +
                "id=" + id +
                ", accNumber='" + accNumber + '\'' +
                ", accName='" + accName + '\'' +
                ", isOldAccount='" + isOldAccount + '\'' +
                '}';
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, accNumber, accName, isOldAccount);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
        return true;
        else if (obj == null)
        return false;
        else if (getClass() != obj.getClass())
        return false;
        else {
            ATSACC_Internal_Account other = (ATSACC_Internal_Account) obj;
        return this.hashCode() == other.hashCode();}
    }


}