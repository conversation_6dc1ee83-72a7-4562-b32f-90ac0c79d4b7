package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSRBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface RBatchRepository extends JpaRepository<ATSRBatch, Long> {
    @Query("SELECT b FROM ATSRBatch b " +
            "JOIN b.state s " +
            "WHERE b.batchId = :batchId " +
            "AND s.code IN :stateCode ")
    ATSRBatch findByBatchIdAndState(
            @Param("batchId") String batchId,
            @Param("stateCode") String stateCode);

    @Query("SELECT b FROM ATSRBatch b " +
            "WHERE b.batchId = :batchId " +
            "AND b.processingStatus = :processingState ")
    ATSRBatch findByBatchIdAndProcessingState(
            @Param("batchId") String batchId,
            @Param("processingState") String stateCode);
}