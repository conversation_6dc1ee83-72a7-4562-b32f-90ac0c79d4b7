package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSACC_Suspensions")
@XmlRootElement(name="ACC_Suspensions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSACC_Suspension extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSACC_Suspension(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="DEBITSUSPFLAG", nullable=true, length=1)
private boolean debitSuspFlag;
public boolean getDebitSuspFlag(){
return this.debitSuspFlag;
}
public void setDebitSuspFlag(boolean debitSuspFlag){
this.debitSuspFlag = debitSuspFlag;
}

@Column(name="CREDITSUSPFLAG", nullable=true, length=1)
private boolean creditSuspFlag;
public boolean getCreditSuspFlag(){
return this.creditSuspFlag;
}
public void setCreditSuspFlag(boolean creditSuspFlag){
this.creditSuspFlag = creditSuspFlag;
}

@Column(name="SUSPNOTE", nullable=false, length=1024)
private String suspNote;
public String getSuspNote(){
return this.suspNote;
}
public void setSuspNote(String suspNote){
this.suspNote = suspNote;
}

@Column(name="CURRDEBITSUSPFLAG", nullable=true, length=1)
@Transient
private boolean currDebitSuspFlag;
public boolean getCurrDebitSuspFlag(){
return this.currDebitSuspFlag;
}
public void setCurrDebitSuspFlag(boolean currDebitSuspFlag){
this.currDebitSuspFlag = currDebitSuspFlag;
}

@Column(name="CURRCREDITSUSPFLAG", nullable=true, length=1)
@Transient
private boolean currCreditSuspFlag;
public boolean getCurrCreditSuspFlag(){
return this.currCreditSuspFlag;
}
public void setCurrCreditSuspFlag(boolean currCreditSuspFlag){
this.currCreditSuspFlag = currCreditSuspFlag;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTICIPANTID", nullable=false)
private ATSPRT_Participant refParticipant;
public ATSPRT_Participant getRefParticipant(){
return this.refParticipant;
}
public void setRefParticipant(ATSPRT_Participant refParticipant){
this.refParticipant = refParticipant;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTACCID", nullable=false)
private ATSACC_Account refPartAcc;
public ATSACC_Account getRefPartAcc(){
return this.refPartAcc;
}
public void setRefPartAcc(ATSACC_Account refPartAcc){
this.refPartAcc = refPartAcc;
}

@Override
public String toString() {
return "ATSACC_Suspension [id= " + getId() + ", debitSuspFlag= " + getDebitSuspFlag() + ", creditSuspFlag= " + getCreditSuspFlag() + ", suspNote= " + getSuspNote() + ", currDebitSuspFlag= " + getCurrDebitSuspFlag() + ", currCreditSuspFlag= " + getCurrCreditSuspFlag() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSACC_Suspension other = (ATSACC_Suspension) obj;
return this.hashCode() == other.hashCode();}
}


}