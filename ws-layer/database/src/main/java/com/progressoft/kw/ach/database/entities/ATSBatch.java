package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.kw.ach.database.utils.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSBatches",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"BATCHID", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "Batches")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBatch extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSBatch() {/*Default Constructor*/}

    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "BATCHID", nullable = true, length = 35)
    private String batchId;

    public String getBatchId() {
        return this.batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    @Column(name = "AMOUNT", nullable = true, precision = 14, scale = 5, length = 16)
    private java.math.BigDecimal amount;

    public java.math.BigDecimal getAmount() {
        return this.amount;
    }

    public void setAmount(java.math.BigDecimal amount) {
        this.amount = amount;
    }

    @Column(name = "COUNT", nullable = true, length = 10)
    private long count;

    public long getCount() {
        return this.count;
    }

    public void setCount(long count) {
        this.count = count;
    }

    @Column(name = "SETTLEMENTDATE", nullable = true, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDate;

    public java.util.Date getSettlementDate() {
        return this.settlementDate;
    }

    public void setSettlementDate(java.util.Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    @XmlJavaTypeAdapter(TimestampAdapter.class)
    @Column(name = "CREATIONDT", nullable = true, length = 34)
    private java.sql.Timestamp creationDt;

    public java.sql.Timestamp getCreationDt() {
        return this.creationDt;
    }

    public void setCreationDt(java.sql.Timestamp creationDt) {
        this.creationDt = creationDt;
    }

    @Column(name = "ADDITIONALINFO", nullable = true, length = 4000)
    private String additionalInfo;

    public String getAdditionalInfo() {
        return this.additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    @Column(name = "PRIORITY", nullable = true, length = 4000)
    private String priority;

    public String getPriority() {
        return this.priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    @Column(name = "ISIMMEDIATE", nullable = true, length = 4000)
    private boolean isImmediate;

    public boolean getIsImmediate() {
        return this.isImmediate;
    }

    public void setIsImmediate(boolean isImmediate) {
        this.isImmediate = isImmediate;
    }

    @Column(name = "ISPULLED")
    private Boolean isPulled;

    public Boolean getIsPulled() {
        return isPulled;
    }

    public void setIsPulled(Boolean isPulled) {
        this.isPulled = isPulled;
    }

    @Column(name = "RcvdForInstdAgent")
    private Boolean isRcvdForInstdAgent;

    public Boolean getIsRcvdForInstdAgent() {
        return isRcvdForInstdAgent;
    }

    public void setIsRcvdForInstdAgent(Boolean isRcvdForInstdAgent) {
        this.isRcvdForInstdAgent = isRcvdForInstdAgent;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "MSGTYPEID", nullable = true)
    private ATSMSG_Type msgType;

    public ATSMSG_Type getMsgType() {
        return this.msgType;
    }

    public void setMsgType(ATSMSG_Type msgType) {
        this.msgType = msgType;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CATPURPID", nullable = true)
    private ATSMSG_CtgPurp catPurp;

    public ATSMSG_CtgPurp getCatPurp() {
        return this.catPurp;
    }

    public void setCatPurp(ATSMSG_CtgPurp catPurp) {
        this.catPurp = catPurp;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SOURCEID", nullable = true)
    private ATSLKP_BatchSource source;

    public ATSLKP_BatchSource getSource() {
        return this.source;
    }

    public void setSource(ATSLKP_BatchSource source) {
        this.source = source;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMPARTIID", nullable = true)
    private ATSPRT_Participant comParti;

    public ATSPRT_Participant getComParti() {
        return this.comParti;
    }

    public void setComParti(ATSPRT_Participant comParti) {
        this.comParti = comParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGPARTIID", nullable = true)
    private ATSPRT_Participant instgParti;

    public ATSPRT_Participant getInstgParti() {
        return this.instgParti;
    }

    public void setInstgParti(ATSPRT_Participant instgParti) {
        this.instgParti = instgParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTGBRANCHID", nullable = true)
    private ATSPRT_Branch instgBranch;

    public ATSPRT_Branch getInstgBranch() {
        return this.instgBranch;
    }

    public void setInstgBranch(ATSPRT_Branch instgBranch) {
        this.instgBranch = instgBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDPARTIID", nullable = true)
    private ATSPRT_Participant instdParti;

    public ATSPRT_Participant getInstdParti() {
        return this.instdParti;
    }

    public void setInstdParti(ATSPRT_Participant instdParti) {
        this.instdParti = instdParti;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTDBRANCHID", nullable = true)
    private ATSPRT_Branch instdBranch;

    public ATSPRT_Branch getInstdBranch() {
        return this.instdBranch;
    }

    public void setInstdBranch(ATSPRT_Branch instdBranch) {
        this.instdBranch = instdBranch;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "STATEID", nullable = true)
    private ATSState state;

    public ATSState getState() {
        return this.state;
    }

    public void setState(ATSState state) {
        this.state = state;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REASONID", nullable = true)
    private ATSMSG_Reason reason;

    public ATSMSG_Reason getReason() {
        return this.reason;
    }

    public void setReason(ATSMSG_Reason reason) {
        this.reason = reason;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "WINDOWID", nullable = true)
    private ATSBDI_Window window;

    public ATSBDI_Window getWindow() {
        return this.window;
    }

    public void setWindow(ATSBDI_Window window) {
        this.window = window;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "batch")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSTransaction> batchTransactions;

    public List<ATSTransaction> getBatchTransactions() {
        return this.batchTransactions;
    }

    public void setBatchTransactions(List<ATSTransaction> batchTransactions) {
        this.batchTransactions = batchTransactions;
    }

    @OneToMany(fetch = FetchType.LAZY,mappedBy = "orgBatch")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRBatch> orgBatchRBatches;

    public List<ATSRBatch> getOrgBatchRBatches() {
        return this.orgBatchRBatches;
    }

    public void setOrgBatchRBatches(List<ATSRBatch> orgBatchRBatches) {
        this.orgBatchRBatches = orgBatchRBatches;
    }

    @Override
    public String toString() {
        return "ATSBatch [id= " + getId() + ", batchId= " + getBatchId() + ", amount= " + getAmount() + ", count= " + getCount() + ", settlementDate= " + getSettlementDate() + ", creationDt= " + getCreationDt() + ", additionalInfo= " + getAdditionalInfo() + ", priority= " + getPriority() + ", isImmediate= " + getIsImmediate() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
        int Count = new Long("null".equals(getCount() + "") ? 0 : getCount()).intValue();
        result = prime * result + (int) (Count ^ Count >>> 32);
        result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
        result = prime * result + ((getAdditionalInfo() == null) ? 0 : getAdditionalInfo().hashCode());
        result = prime * result + ((getPriority() == null) ? 0 : getPriority().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSBatch other = (ATSBatch) obj;
            return this.hashCode() == other.hashCode();
        }
    }

}
