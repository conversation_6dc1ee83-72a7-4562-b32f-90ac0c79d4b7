package com.progressoft.kw.ach.database.repository;

import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency;
import com.progressoft.kw.ach.database.entities.ATSACC_Account;
import com.progressoft.kw.ach.database.entities.ATSPRT_Participant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import jakarta.transaction.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

public interface AccountRepository extends JpaRepository<ATSACC_Account, Long> {
    List<ATSACC_Account> findByCurrencyAndParticipant(JFWCurrency currency, ATSPRT_Participant participant);

    Optional<ATSACC_Account> findByIBAN(String IBAN);

    @Modifying
    @Transactional
    @Query("UPDATE ATSACC_Account a SET a.debitCap = :newLimitAmount WHERE a.IBAN = :IBAN")
    void updateDebitCap(@Param("IBAN") String IBAN, @Param("newLimitAmount") BigDecimal newLimitAmount);
}
