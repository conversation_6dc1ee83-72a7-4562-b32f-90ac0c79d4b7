package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;
import java.util.Objects;

@Entity

@Table(name = "ATSSplitRBatchStatus")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSSplitRBatchStatus extends JFWEntity implements Serializable {

    public ATSSplitRBatchStatus() {}

    public static final String ID = "id";
    @Id
    @SequenceGenerator(name = "SEQ_ATSSplitRBatchStatus", sequenceName = "SEQ_ATSSplitRBatchStatus", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="SEQ_ATSSplitRBatchStatus")
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String BATCH_ID = "batchId";
    @Column(name = "BATCHID", length = 35)
    private String batchId;

    public String getBatchId() {
        return this.batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public static final String IS_PROCESSED = "isProcessed";
    @Column(name = "ISPROCESSED")
    private boolean isProcessed;

    public boolean isProcessed() {
        return isProcessed;
    }

    public void setProcessed(boolean processed) {
        isProcessed = processed;
    }

    public static final String COUNT = "count";
    @Column(name = "COUNT")
    private long count;

    public long getCount() {
        return this.count;
    }

    public void setCount(long size) {
        this.count = size;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSSplitRBatchStatus{" +
                "id=" + id +
                ", batchId='" + batchId + '\'' +
                ", isProcessed=" + isProcessed +
                ", size=" + count +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ATSSplitRBatchStatus that)) return false;
        return id == that.id && isProcessed == that.isProcessed && count == that.count && Objects.equals(batchId, that.batchId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, batchId, isProcessed, count);
    }
}