package com.progressoft.kw.ach.database.entities;

import jakarta.persistence.*;

@Entity
@Table(name = "JFW_WF_STATUS")
public class JfwWfStatus {
    @Id
    @Column(name = "ID")
    private Long id;

    @Column(name = "Z_ARCHIVE_ON")
    private java.sql.Timestamp zArchiveOn;

    @Column(name = "Z_ARCHIVE_QUEUED")
    private java.sql.Timestamp zArchiveQueued;

    @Column(name = "Z_ARCHIVE_STATUS")
    private String zArchiveStatus;

    @Column(name = "Z_ASSIGNED_GROUP")
    private Long zAssignedGroup;

    @Column(name = "Z_ASSIGNED_USER")
    private Long zAssignedUser;

    @Column(name = "Z_CREATED_BY")
    private String zCreatedBy;

    @Column(name = "Z_CREATION_DATE")
    private java.sql.Timestamp zCreationDate;

    @Column(name = "Z_DELETED_BY")
    private String zDeletedBy;

    @Column(name = "Z_DELETED_FLAG")
    private Long zDeletedFlag;

    @Column(name = "Z_DELETED_ON")
    private java.sql.Timestamp zDeletedOn;

    @Column(name = "Z_EDITABLE")
    private Long zEditable;

    @Column(name = "Z_LOCKED_BY")
    private String zLockedBy;

    @Column(name = "Z_LOCKED_UNTIL")
    private java.sql.Timestamp zLockedUntil;

    @Column(name = "Z_ORG_ID")
    private Long zOrgId;

    @Column(name = "Z_TENANT_ID")
    private String zTenantId;

    @Column(name = "Z_UPDATED_BY")
    private String zUpdatedBy;

    @Column(name = "Z_UPDATING_DATE")
    private java.sql.Timestamp zUpdatingDate;

    @Column(name = "Z_WORKFLOW_ID")
    private Long zWorkflowId;

    @Column(name = "Z_WS_TOKEN")
    private String zWsToken;

    @Column(name = "CODE")
    private String code;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "ACTIVE")
    private Long active;

    @Column(name = "LABEL")
    private String label;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public java.sql.Timestamp getZArchiveOn() {
        return this.zArchiveOn;
    }

    public void setZArchiveOn(java.sql.Timestamp zArchiveOn) {
        this.zArchiveOn = zArchiveOn;
    }

    public java.sql.Timestamp getZArchiveQueued() {
        return this.zArchiveQueued;
    }

    public void setZArchiveQueued(java.sql.Timestamp zArchiveQueued) {
        this.zArchiveQueued = zArchiveQueued;
    }

    public String getZArchiveStatus() {
        return this.zArchiveStatus;
    }

    public void setZArchiveStatus(String zArchiveStatus) {
        this.zArchiveStatus = zArchiveStatus;
    }

    public Long getZAssignedGroup() {
        return this.zAssignedGroup;
    }

    public void setZAssignedGroup(Long zAssignedGroup) {
        this.zAssignedGroup = zAssignedGroup;
    }

    public Long getZAssignedUser() {
        return this.zAssignedUser;
    }

    public void setZAssignedUser(Long zAssignedUser) {
        this.zAssignedUser = zAssignedUser;
    }

    public String getZCreatedBy() {
        return this.zCreatedBy;
    }

    public void setZCreatedBy(String zCreatedBy) {
        this.zCreatedBy = zCreatedBy;
    }

    public java.sql.Timestamp getZCreationDate() {
        return this.zCreationDate;
    }

    public void setZCreationDate(java.sql.Timestamp zCreationDate) {
        this.zCreationDate = zCreationDate;
    }

    public String getZDeletedBy() {
        return this.zDeletedBy;
    }

    public void setZDeletedBy(String zDeletedBy) {
        this.zDeletedBy = zDeletedBy;
    }

    public Long getZDeletedFlag() {
        return this.zDeletedFlag;
    }

    public void setZDeletedFlag(Long zDeletedFlag) {
        this.zDeletedFlag = zDeletedFlag;
    }

    public java.sql.Timestamp getZDeletedOn() {
        return this.zDeletedOn;
    }

    public void setZDeletedOn(java.sql.Timestamp zDeletedOn) {
        this.zDeletedOn = zDeletedOn;
    }

    public Long getZEditable() {
        return this.zEditable;
    }

    public void setZEditable(Long zEditable) {
        this.zEditable = zEditable;
    }

    public String getZLockedBy() {
        return this.zLockedBy;
    }

    public void setZLockedBy(String zLockedBy) {
        this.zLockedBy = zLockedBy;
    }

    public java.sql.Timestamp getZLockedUntil() {
        return this.zLockedUntil;
    }

    public void setZLockedUntil(java.sql.Timestamp zLockedUntil) {
        this.zLockedUntil = zLockedUntil;
    }

    public Long getZOrgId() {
        return this.zOrgId;
    }

    public void setZOrgId(Long zOrgId) {
        this.zOrgId = zOrgId;
    }

    public String getZTenantId() {
        return this.zTenantId;
    }

    public void setZTenantId(String zTenantId) {
        this.zTenantId = zTenantId;
    }

    public String getZUpdatedBy() {
        return this.zUpdatedBy;
    }

    public void setZUpdatedBy(String zUpdatedBy) {
        this.zUpdatedBy = zUpdatedBy;
    }

    public java.sql.Timestamp getZUpdatingDate() {
        return this.zUpdatingDate;
    }

    public void setZUpdatingDate(java.sql.Timestamp zUpdatingDate) {
        this.zUpdatingDate = zUpdatingDate;
    }

    public Long getZWorkflowId() {
        return this.zWorkflowId;
    }

    public void setZWorkflowId(Long zWorkflowId) {
        this.zWorkflowId = zWorkflowId;
    }

    public String getZWsToken() {
        return this.zWsToken;
    }

    public void setZWsToken(String zWsToken) {
        this.zWsToken = zWsToken;
    }

    public String getCode() {
        return this.code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getActive() {
        return this.active;
    }

    public void setActive(Long active) {
        this.active = active;
    }

    public String getLabel() {
        return this.label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
