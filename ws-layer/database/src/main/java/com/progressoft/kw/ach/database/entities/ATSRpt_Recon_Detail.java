package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;

@Entity

@Table(name="ATSRpt_Recon_Details")
@XmlRootElement(name="Rpt_Recon_Details")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_Recon_Detail extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRpt_Recon_Detail(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String SECTION = "section";
@Column(name="SECTION", nullable=true, length=1000)
private String section;
public String getSection(){
return this.section;
}
public void setSection(String section){
this.section = section;
}

public static final String KEY = "key";
@Column(name="KEY", nullable=true, length=1000)
private String key;
public String getKey(){
return this.key;
}
public void setKey(String key){
this.key = key;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=1000)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String COUNT = "count";
@Column(name="COUNT", nullable=false, length=10)
private long count;
public long getCount(){
return this.count;
}
public void setCount(long count){
this.count = count;
}

public static final String AMOUNT = "amount";
@Column(name="AMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal amount;
public java.math.BigDecimal getAmount(){
return this.amount;
}
public void setAmount(java.math.BigDecimal amount){
this.amount = amount;
}

public static final String REPORT = "report";
@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REPORTID", nullable=true)
private ATSRpt_Recon report;
public ATSRpt_Recon getReport(){
return this.report;
}
public void setReport(ATSRpt_Recon report){
this.report = report;
}

@Override
public String toString() {
return "ATSRpt_Recon_Detail [id= " + getId() + ", section= " + getSection() + ", key= " + getKey() + ", description= " + getDescription() + ", count= " + getCount() + ", amount= " + getAmount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getSection() == null) ? 0 : getSection().hashCode());
result = prime * result + ((getKey() == null) ? 0 : getKey().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int Count= new Long("null".equals(getCount() + "") ? 0 : getCount()).intValue();
result = prime * result + (int) (Count ^ Count >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRpt_Recon_Detail other = (ATSRpt_Recon_Detail) obj;
return this.hashCode() == other.hashCode();}
}


}