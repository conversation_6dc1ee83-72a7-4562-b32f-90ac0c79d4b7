package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity

@Table(name="ATSDisputedTransactions")
@XmlRootElement(name="ATSDisputedTransactions")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )

public class ATSDisputedTransactions extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSDisputedTransactions(){/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @SequenceGenerator(name = "SEQ_ATSDisputedTransactions", sequenceName = "SEQ_ATSDisputedTransactions", allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator="SEQ_ATSDisputedTransactions")
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    public long getId(){
    return this.id;
    }
    public void setId(long id){
    this.id = id;
    }

    @Column(name="OUTRBATCHID", nullable=true)
    private Long outRBatchId;
    public Long getOutRBatchId() {
        return outRBatchId;
    }
    public void setOutRBatchId(Long outRBatchId) {
        this.outRBatchId = outRBatchId;
    }

    @Column(name="BATCHID", nullable=true)
    private String batchId;
    public String getBatchId() {
        return batchId;
    }
    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    @Column(name="ISPULLED", nullable=true)
    private boolean isPulled;
    public boolean isPulled() {
        return isPulled;
    }
    public void setPulled(boolean pulled) {
        isPulled = pulled;
    }

    @Column(name="ISRECEIVEDACK", nullable=true)
    private boolean isReceivedAck;
    public boolean isReceivedAck() {
        return isReceivedAck;
    }
    public void setReceivedAck(boolean receivedAck) {
        isReceivedAck = receivedAck;
    }

    @Column(name="PARTICIPANTCODE", nullable=true)
    private String participantCode;
    public String getParticipantCode() {
        return participantCode;
    }
    public void setParticipantCode(String participantCode) {
        this.participantCode = participantCode;
    }

    @Override
    public void injectTenantId() {}

    @Override
    public String toString() {
        return "ATSDisputedTransactions{" +
                "id=" + id +
                ", outRBatch=" + outRBatchId +
                ", batchId=" + batchId +
                ", isPulled=" + isPulled +
                ", isReceivedAck=" + isReceivedAck +
                ", participantCode='" + participantCode + '\'' +
                '}';
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, outRBatchId, batchId, isPulled, isReceivedAck, participantCode);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
        return true;
        else if (obj == null)
        return false;
        else if (getClass() != obj.getClass())
        return false;
        else {
            ATSDisputedTransactions other = (ATSDisputedTransactions) obj;
        return this.hashCode() == other.hashCode();}
    }

}