package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSOUTBatch;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface BatchOutRepository extends JpaRepository<ATSOUTBatch, Long> {
    @Query("SELECT COUNT(b) FROM ATSOUTBatch b " +
            "JOIN b.msgType m " +
            "JOIN b.instdParti p " +
            "WHERE p.code = :participantCode " +
            "AND m.code = :messageCode " +
            "AND b.isPulled = false")
    Long countByMsgTypeAndInstdParti(
            @Param("participantCode") String participantCode,
            @Param("messageCode") String messageCode);

    @Query("SELECT b FROM ATSOUTBatch b " +
            "JOIN b.state s " +
            "WHERE b.batchId = :batchId " +
            "AND s.code IN :stateCodes ")
    ATSOUTBatch findBatchByBatchIdAndStateCodes(
            @Param("batchId") String batchId,
            @Param("stateCodes") List<String> stateCodes);
}
