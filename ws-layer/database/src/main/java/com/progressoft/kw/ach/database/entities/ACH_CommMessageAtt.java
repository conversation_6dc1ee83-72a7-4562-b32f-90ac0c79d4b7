package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.attachments.AttachmentItem;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;

import java.io.Serializable;


@Entity
@Table(name = "COMMMESSAGESATT")
@XmlAccessorType(XmlAccessType.FIELD)
public class ACH_CommMessageAtt extends AttachmentItem implements Serializable {
    private static final long serialVersionUID = 1L;

    public ACH_CommMessageAtt() {/*Default Constructor*/}

    @Override
    public void injectTenantId() {

    }


}