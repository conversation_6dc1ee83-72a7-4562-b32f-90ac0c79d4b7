package com.progressoft.kw.ach.database.entities;

import jakarta.persistence.*;

@Entity
@Table(name = "JFW_OS_WFENTRY")
public class JfwOsWfEntry {
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private Long id;

    @Column(name = "Z_ARCHIVE_STATUS")
    private String zArchiveStatus;

    @Column(name = "NAME")
    private String name;

    @Column(name = "STATE")
    private Long state;

    @Column(name = "VERSION")
    private Long version;

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getZArchiveStatus() {
        return this.zArchiveStatus;
    }

    public void setZArchiveStatus(String zArchiveStatus) {
        this.zArchiveStatus = zArchiveStatus;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getState() {
        return this.state;
    }

    public void setState(Long state) {
        this.state = state;
    }

    public Long getVersion() {
        return this.version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }
}
