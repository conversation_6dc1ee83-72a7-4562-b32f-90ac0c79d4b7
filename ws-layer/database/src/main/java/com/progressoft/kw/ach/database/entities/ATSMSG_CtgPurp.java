package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMSG_CtgPurps",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"MTCODE","Z_TENANT_ID"})
})
@XmlRootElement(name="MSG_CtgPurps")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMSG_CtgPurp extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMSG_CtgPurp(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="MTCODE", nullable=false, length=3)
private String mtcode;
public String getMtcode(){
return this.mtcode;
}
public void setMtcode(String mtcode){
this.mtcode = mtcode;
}

@Column(name="ISPARTIALCANCELLATIONSUPPORTED")
private boolean isPartialCancellationSupported;
public boolean getIsPartialCancellationSupported() {
	return this.isPartialCancellationSupported;
}
public void setIsPartialCancellationSupported(boolean isPartialCancellationSupported) {
		this.isPartialCancellationSupported = isPartialCancellationSupported;
	}

@Column(name="ISFULLREJECTION")
private boolean isFullRejection;
public boolean getIsFullRejection() {
	return this.isFullRejection;
}
public void setIsFullRejection(boolean isFullRejection) {
	this.isFullRejection = isFullRejection;
}

@Override
public String toString() {
	return "ATSMSG_CtgPurp [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", mtcode= " + getMtcode() + ", isPartialCancellationSupported= " + getIsPartialCancellationSupported() + ", isFullRejection= " + getIsFullRejection() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
result = prime * result + ((getMtcode() == null) ? 0 : getMtcode().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMSG_CtgPurp other = (ATSMSG_CtgPurp) obj;
return this.hashCode() == other.hashCode();}
}


}