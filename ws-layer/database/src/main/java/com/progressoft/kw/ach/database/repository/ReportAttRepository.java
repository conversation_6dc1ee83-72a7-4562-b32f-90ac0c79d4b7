package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSReportRecordAtt;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

public interface ReportAttRepository extends JpaRepository<ATSReportRecordAtt, Long> {
    ATSReportRecordAtt findByName(String name);

    Optional<ATSReportRecordAtt> findByRecordId(String recordId);
}
