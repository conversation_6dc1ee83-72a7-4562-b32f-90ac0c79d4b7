package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSReportRecord")
@XmlRootElement(name="ReportRecord")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSReportRecord extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSReportRecord(){/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    public long getId(){
        return this.id;
    }
    public void setId(long id){
        this.id = id;
    }

    public static final String SETTLEMENT_DT = "settlementDt";
    @Column(name="SETTLEMENTDT", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDt;
    public java.util.Date getSettlementDt(){
        return this.settlementDt;
    }
    public void setSettlementDt(java.util.Date settlementDt){
        this.settlementDt = settlementDt;
    }

    public static final String PARTICIPATED_SYSTEMS = "participatedSystems";
    @Column(name="PARTICIPATEDSYSTEMS", nullable=true, length=200)
    private String participatedSystems;
    public String getParticipatedSystems(){
        return this.participatedSystems;
    }
    public void setParticipatedSystems(String participatedSystems){
        this.participatedSystems = participatedSystems;
    }

    public static final String WINDOW_SETTLEMENT_END_DATE = "windowSettlementEndDate";
    @Column(name="WINDOWSETTLEMENTENDDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date windowSettlementEndDate;
    public java.util.Date getWindowSettlementEndDate(){
        return this.windowSettlementEndDate;
    }
    public void setWindowSettlementEndDate(java.util.Date windowSettlementEndDate){
        this.windowSettlementEndDate = windowSettlementEndDate;
    }

    public static final String EXCHANGE_DATE = "exchangeDate";
    @Column(name="EXCHANGEDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date exchangeDate;
    public java.util.Date getExchangeDate(){
        return this.exchangeDate;
    }
    public void setExchangeDate(java.util.Date exchangeDate){
        this.exchangeDate = exchangeDate;
    }

    public static final String EXCHANGE_END_DATE = "exchangeEndDate";
    @Column(name="EXCHANGEENDDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date exchangeEndDate;
    public java.util.Date getExchangeEndDate(){
        return this.exchangeEndDate;
    }
    public void setExchangeEndDate(java.util.Date exchangeEndDate){
        this.exchangeEndDate = exchangeEndDate;
    }

    public static final String FILE_REF = "fileRef";
    @Column(name="FILEREF", nullable=true, length=255)
    private String fileRef;
    public String getFileRef(){
        return this.fileRef;
    }
    public void setFileRef(String fileRef){
        this.fileRef = fileRef;
    }

    public static final String REPORT_CREATED_DATE = "reportCreatedDate";
    @Column(name="REPORTCREATEDDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date reportCreatedDate;
    public java.util.Date getReportCreatedDate(){
        return this.reportCreatedDate;
    }
    public void setReportCreatedDate(java.util.Date reportCreatedDate){
        this.reportCreatedDate = reportCreatedDate;
    }

    public static final String WINDOW_ID = "windowId";
    @Column(name="WINDOWID", nullable=true, length=255)
    private String windowId;
    public String getWindowId(){
        return this.windowId;
    }
    public void setWindowId(String windowId){
        this.windowId = windowId;
    }

    public static final String REPORT_TYPE = "reportType";
    @Column(name="REPORTTYPE", nullable=true, length=255)
    private String reportType;
    public String getReportType(){
        return this.reportType;
    }
    public void setReportType(String reportType){
        this.reportType = reportType;
    }

    public static final String ROW_COUNT = "rowCount";
    @Column(name="ROWCOUNT", nullable=true, length=10)
    private long rowCount;
    public long getRowCount(){
        return this.rowCount;
    }
    public void setRowCount(long rowCount){
        this.rowCount = rowCount;
    }

    public static final String REPORT_HISTORY_DATE = "reportHistoryDate";
    @Column(name="REPORTHISTORYDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date reportHistoryDate;
    public java.util.Date getReportHistoryDate(){
        return this.reportHistoryDate;
    }
    public void setReportHistoryDate(java.util.Date reportHistoryDate){
        this.reportHistoryDate = reportHistoryDate;
    }

    public static final String WINDOW_SETTLEMENT_RETRY_NUMBER = "windowSettlementRetryNumber";
    @Column(name="WINDOWSETTLEMENTRETRYNUMBER", nullable=true, length=10)
    private long windowSettlementRetryNumber;
    public long getWindowSettlementRetryNumber(){
        return this.windowSettlementRetryNumber;
    }
    public void setWindowSettlementRetryNumber(long windowSettlementRetryNumber){
        this.windowSettlementRetryNumber = windowSettlementRetryNumber;
    }

    public static final String LOCK_DATE = "lockDate";
    @Column(name="LOCKDATE", nullable=true, length=34)
    @Temporal(TemporalType.DATE)
    private java.util.Date lockDate;
    public java.util.Date getLockDate(){
        return this.lockDate;
    }
    public void setLockDate(java.util.Date lockDate){
        this.lockDate = lockDate;
    }

    public static final String REPORT_COMMUNICATION_AUTO_IS_PULLED = "reportCommunicationAutoIsPulled";
    @Column(name="REPORTCOMMUNICATIONAUTOISPULLED", nullable=true, length=1)
    private boolean reportCommunicationAutoIsPulled;
    public boolean getReportCommunicationAutoIsPulled(){
        return this.reportCommunicationAutoIsPulled;
    }
    public void setReportCommunicationAutoIsPulled(boolean reportCommunicationAutoIsPulled){
        this.reportCommunicationAutoIsPulled = reportCommunicationAutoIsPulled;
    }

    public static final String REPORT_SETTLEMENT_AUTO_IS_PULLED = "reportSettlementAutoIsPulled";
    @Column(name="REPORTSETTLEMENTAUTOISPULLED", nullable=true, length=1)
    private boolean reportSettlementAutoIsPulled;
    public boolean getReportSettlementAutoIsPulled(){
        return this.reportSettlementAutoIsPulled;
    }
    public void setReportSettlementAutoIsPulled(boolean reportSettlementAutoIsPulled){
        this.reportSettlementAutoIsPulled = reportSettlementAutoIsPulled;
    }

    public static final String COMM_AGENT = "commAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="COMMAGENTID", nullable=true)
    private ATSPRT_Participant commAgent;
    public ATSPRT_Participant getCommAgent(){
        return this.commAgent;
    }
    public void setCommAgent(ATSPRT_Participant commAgent){
        this.commAgent = commAgent;
    }

    public static final String SETT_AGENT = "settAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="SETTAGENTID", nullable=true)
    private ATSPRT_Participant settAgent;
    public ATSPRT_Participant getSettAgent(){
        return this.settAgent;
    }
    public void setSettAgent(ATSPRT_Participant settAgent){
        this.settAgent = settAgent;
    }

    public static final String SESSION = "session";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="SESSIONID", nullable=true)
    private ATSBDI_Session session;
    public ATSBDI_Session getSession(){
        return this.session;
    }
    public void setSession(ATSBDI_Session session){
        this.session = session;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="CURRENCYID", nullable=true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;
    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency(){
        return this.currency;
    }
    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency){
        this.currency = currency;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSReportRecord [id= " + getId() + ", settlementDt= " + getSettlementDt() + ", participatedSystems= " + getParticipatedSystems() + ", windowSettlementEndDate= " + getWindowSettlementEndDate() + ", exchangeDate= " + getExchangeDate() + ", exchangeEndDate= " + getExchangeEndDate() + ", fileRef= " + getFileRef() + ", reportCreatedDate= " + getReportCreatedDate() + ", windowId= " + getWindowId() + ", reportType= " + getReportType() + ", rowCount= " + getRowCount() + ", reportHistoryDate= " + getReportHistoryDate() + ", windowSettlementRetryNumber= " + getWindowSettlementRetryNumber() + ", lockDate= " + getLockDate() + ", reportCommunicationAutoIsPulled= " + getReportCommunicationAutoIsPulled() + ", reportSettlementAutoIsPulled= " + getReportSettlementAutoIsPulled() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
        result = prime * result + ((getParticipatedSystems() == null) ? 0 : getParticipatedSystems().hashCode());
        result = prime * result + ((getWindowSettlementEndDate() == null) ? 0 : getWindowSettlementEndDate().hashCode());
        result = prime * result + ((getExchangeDate() == null) ? 0 : getExchangeDate().hashCode());
        result = prime * result + ((getExchangeEndDate() == null) ? 0 : getExchangeEndDate().hashCode());
        result = prime * result + ((getFileRef() == null) ? 0 : getFileRef().hashCode());
        result = prime * result + ((getReportCreatedDate() == null) ? 0 : getReportCreatedDate().hashCode());
        result = prime * result + ((getWindowId() == null) ? 0 : getWindowId().hashCode());
        result = prime * result + ((getReportType() == null) ? 0 : getReportType().hashCode());
        int RowCount= new Long("null".equals(getRowCount() + "") ? 0 : getRowCount()).intValue();
        result = prime * result + (int) (RowCount ^ RowCount >>> 32);
        result = prime * result + ((getReportHistoryDate() == null) ? 0 : getReportHistoryDate().hashCode());
        int WindowSettlementRetryNumber= new Long("null".equals(getWindowSettlementRetryNumber() + "") ? 0 : getWindowSettlementRetryNumber()).intValue();
        result = prime * result + (int) (WindowSettlementRetryNumber ^ WindowSettlementRetryNumber >>> 32);
        result = prime * result + ((getLockDate() == null) ? 0 : getLockDate().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {ATSReportRecord other = (ATSReportRecord) obj;
            return this.hashCode() == other.hashCode();}
    }


}