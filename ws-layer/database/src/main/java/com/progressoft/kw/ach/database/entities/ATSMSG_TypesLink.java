package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMSG_TypesLink")
@XmlRootElement(name="MSG_TypesLink")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMSG_TypesLink extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMSG_TypesLink(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MESSAGEID", nullable=false)
private ATSMSG_Type message;
public ATSMSG_Type getMessage(){
return this.message;
}
public void setMessage(ATSMSG_Type message){
this.message = message;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="ORIGINALMESSAGEID", nullable=false)
private ATSMSG_Type originalMessage;
public ATSMSG_Type getOriginalMessage(){
return this.originalMessage;
}
public void setOriginalMessage(ATSMSG_Type originalMessage){
this.originalMessage = originalMessage;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="ROOTMESSAGEID", nullable=false)
private ATSMSG_Type rootMessage;
public ATSMSG_Type getRootMessage(){
return this.rootMessage;
}
public void setRootMessage(ATSMSG_Type rootMessage){
this.rootMessage = rootMessage;
}

@Override
public String toString() {
return "ATSMSG_TypesLink [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMSG_TypesLink other = (ATSMSG_TypesLink) obj;
return this.hashCode() == other.hashCode();}
}


}