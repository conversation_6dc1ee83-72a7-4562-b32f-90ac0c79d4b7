package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "ATSConfigs", uniqueConstraints = {
        @UniqueConstraint(columnNames = {"CONFIGKEY", "Z_TENANT_ID"})
})
public class ATSConfig extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    @Column(name = "CONFIGKEY", nullable = false)
    private String configKey;
    @Column(name = "CONFIGVALUE")
    private String configValue;

    public ATSConfig() {/*Default Constructor*/}

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getConfigKey() {
        return this.configKey;
    }

    public void setConfigKey(String configKey) {
        this.configKey = configKey;
    }

    public String getConfigValue() {
        return this.configValue;
    }

    public void setConfigValue(String configValue) {
        this.configValue = configValue;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSConfig [id= " + getId() + ", configKey= " + getConfigKey() + ", configValue= " + getConfigValue() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getConfigKey() == null) ? 0 : getConfigKey().hashCode());
        result = prime * result + ((getConfigValue() == null) ? 0 : getConfigValue().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSConfig other = (ATSConfig) obj;
            return this.hashCode() == other.hashCode();}
    }


}