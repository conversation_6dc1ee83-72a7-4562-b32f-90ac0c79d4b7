package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSACC_CapsUpdateReq")
@XmlRootElement(name="ACC_CapsUpdateReq")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSACC_CapsUpdateReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSACC_CapsUpdateReq(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="OLDDEBITCAP", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal oldDebitCap;
public java.math.BigDecimal getOldDebitCap(){
return this.oldDebitCap;
}
public void setOldDebitCap(java.math.BigDecimal oldDebitCap){
this.oldDebitCap = oldDebitCap;
}

@Column(name="NEWDEBITCAP", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal newDebitCap;
public java.math.BigDecimal getNewDebitCap(){
return this.newDebitCap;
}
public void setNewDebitCap(java.math.BigDecimal newDebitCap){
this.newDebitCap = newDebitCap;
}

@Column(name="OLDCREDITCAP", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal oldCreditCap;
public java.math.BigDecimal getOldCreditCap(){
return this.oldCreditCap;
}
public void setOldCreditCap(java.math.BigDecimal oldCreditCap){
this.oldCreditCap = oldCreditCap;
}

@Column(name="NEWCREDITCAP", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal newCreditCap;
public java.math.BigDecimal getNewCreditCap(){
return this.newCreditCap;
}
public void setNewCreditCap(java.math.BigDecimal newCreditCap){
this.newCreditCap = newCreditCap;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTICIPANTID", nullable=false)
private ATSPRT_Participant refParticipant;
public ATSPRT_Participant getRefParticipant(){
return this.refParticipant;
}
public void setRefParticipant(ATSPRT_Participant refParticipant){
this.refParticipant = refParticipant;
}

@ManyToOne(fetch = FetchType.LAZY)
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFACCBALANCEID", nullable=false)
private ATSACC_Balance refAccBalance;

    public ATSACC_Balance getRefAccBalance() {
        return this.refAccBalance;
    }

    public void setRefAccBalance(ATSACC_Balance refAccBalance) {
        this.refAccBalance = refAccBalance;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "ATSACC_CapsUpdateReq [id= " + getId() + ", oldDebitCap= " + getOldDebitCap() + ", newDebitCap= " + getNewDebitCap() + ", oldCreditCap= " + getOldCreditCap() + ", newCreditCap= " + getNewCreditCap() + "]";
    }

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSACC_CapsUpdateReq other = (ATSACC_CapsUpdateReq) obj;
return this.hashCode() == other.hashCode();}
}


}