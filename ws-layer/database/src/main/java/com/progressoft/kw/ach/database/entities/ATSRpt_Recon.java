package com.progressoft.kw.ach.database.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSRpt_Recon")
@XmlRootElement(name = "Rpt_Recon")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_Recon extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSRpt_Recon() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "REPORTID")
    private String reportId;
    public String getReportId(){
        return this.reportId;
    }
    public void setReportId(String reportId) {
        this.reportId = reportId;
    }

    public static final String SETT_RETRY = "settRetry";
    @Column(name = "SETTRETRY", nullable = false, length = 10)
    private long settRetry;

    public long getSettRetry() {
        return this.settRetry;
    }

    public void setSettRetry(long settRetry) {
        this.settRetry = settRetry;
    }

    public static final String DESCRIPTION = "description";
    @Column(name = "DESCRIPTION", nullable = false, length = 40000)
    private String description;

    public String getDescription() {
        return this.description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public static final String IS_PULLED = "isPulled";
    @Column(name = "ISPULLED", nullable = false)
    private Boolean isPulled;

    public Boolean isPulled() {
        return isPulled;
    }

    public void setPulled(Boolean pulled) {
        isPulled = pulled;
    }

    public static final String COMM_AGENT = "commAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMMAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant commAgent;

    public ATSPRT_Participant getCommAgent() {
        return this.commAgent;
    }

    public void setCommAgent(ATSPRT_Participant commAgent) {
        this.commAgent = commAgent;
    }

    public static final String SETT_AGENT = "settAgent";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SETTAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant settAgent;

    public ATSPRT_Participant getSettAgent() {
        return this.settAgent;
    }

    public void setSettAgent(ATSPRT_Participant settAgent) {
        this.settAgent = settAgent;
    }

    public static final String SESSION = "session";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne(fetch = FetchType.LAZY)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    public static final String REPORT_RPT_RECON_DETAILS = "reportRpt_Recon_Details";
    @OneToMany(fetch = FetchType.LAZY,mappedBy = "report")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRpt_Recon_Detail> reportRpt_Recon_Details;

    public List<ATSRpt_Recon_Detail> getReportRptReconDetails() {
        return this.reportRpt_Recon_Details;
    }

    public void setReportRptReconDetails(List<ATSRpt_Recon_Detail> reportRpt_Recon_Details) {
        this.reportRpt_Recon_Details = reportRpt_Recon_Details;
    }

    @Override
    public void injectTenantId() {

    }

    @Override
    public String toString() {
        return "ATSRpt_Recon [id= " + getId() + ", settRetry= " + getSettRetry() + ", description= " + getDescription() + ", isPulled= " + isPulled() +"]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        int SettRetry = new Long("null".equals(getSettRetry() + "") ? 0 : getSettRetry()).intValue();
        result = prime * result + (int) (SettRetry ^ SettRetry >>> 32);
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((isPulled() == null) ? 0 : isPulled().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSRpt_Recon other = (ATSRpt_Recon) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}