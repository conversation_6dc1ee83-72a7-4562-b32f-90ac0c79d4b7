package com.progressoft.kw.ach.database.repository;

import com.progressoft.kw.ach.database.entities.ATSRecon_Message;
import com.progressoft.kw.ach.database.entities.ATSRpt_ReconAtt;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ReconMessageRepository extends JpaRepository<ATSRecon_Message, Long> {
    List<ATSRecon_Message> findAllByReconIdAndIsPulledFalseOrderByMessageIdAsc(String reconId);
    Optional<ATSRecon_Message> findByMessageId (String messageId);

    @Query("SELECT CASE WHEN COUNT(m) = 0 THEN TRUE ELSE FALSE END " +
            "FROM ATSRecon_Message m WHERE m.reconId = :reconId AND m.isPulled = FALSE")
    boolean areAllMessagesPulled(@Param("reconId") String reconId);
}
