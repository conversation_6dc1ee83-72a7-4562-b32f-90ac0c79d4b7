package com.progressoft.kw.ach.messaging.report.pullonlinereport;

import com.progressoft.kw.ach.messaging.report.pullonlinereport.invalidBatch.InvalidBatchRecordsSerializer;
import com.progressoft.kw.ach.messaging.report.pullonlinereport.invalidBatch.models.InvalidBatchRecord;
import org.junit.Before;
import org.junit.Test;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static java.util.TimeZone.getTimeZone;
import static java.util.TimeZone.setDefault;
import static org.junit.Assert.assertEquals;

public class InvalidBatchRecordsSerializerTest {
    private InvalidBatchRecordsSerializer serializer;

    @Before
    public void setUp() {
        setDefault(getTimeZone("UTC"));
        serializer = new InvalidBatchRecordsSerializer();
    }

    @Test
    public void serialize() throws IOException {
        String response = serializer.serialize(getInvalidBatchRecords());
        assertEquals(getResponse(), response);
    }

    private List<InvalidBatchRecord> getInvalidBatchRecords() {
        List<InvalidBatchRecord> invalidBatchRecords = new ArrayList<>();
        invalidBatchRecords.add(getInvalidBatchRecord1());
        invalidBatchRecords.add(getInvalidBatchRecord2());
        return invalidBatchRecords;
    }

    private InvalidBatchRecord getInvalidBatchRecord1() {
        return InvalidBatchRecord.builder()
                .batchId("batch id 1")
                .batchReceivingDate(LocalDateTime.of(1999, 5, 28, 13, 20))
                .batchSequence(1)
                .msgTypeDescription("Message type description 1")
                .msgTypeName("Message type name 1")
                .state("State 1")
                .build();
    }

    private InvalidBatchRecord getInvalidBatchRecord2() {
        return InvalidBatchRecord.builder()
                .batchId("batch id 2")
                .batchReceivingDate(LocalDateTime.of(2000, 6, 23, 11, 10))
                .batchSequence(2)
                .msgTypeDescription("Message type description 2")
                .msgTypeName("Message type name 2")
                .state("State 2")
                .build();
    }

    private String getResponse() {
        return "\n" +
                "<ArrayOfACH_INVLD_BATCH_RPT_RECORD>\n" +
                "<ACH_INVLD_BATCH_RPT_RECORD>\n" +
                "<State>State 1</State>\n" +
                "<BTCH_ID>batch id 1</BTCH_ID>\n" +
                "<BTCH_SEQ>1</BTCH_SEQ>\n" +
                "<BTCH_RECEIVING_DT>1999-05-28T13:20:00.000Z</BTCH_RECEIVING_DT>\n" +
                "<MSGTYP_NAME>Message type name 1</MSGTYP_NAME>\n" +
                "<MSGTYP_DESC>Message type description 1</MSGTYP_DESC>\n" +
                "</ACH_INVLD_BATCH_RPT_RECORD>\n" +
                "<ACH_INVLD_BATCH_RPT_RECORD>\n" +
                "<State>State 2</State>\n" +
                "<BTCH_ID>batch id 2</BTCH_ID>\n" +
                "<BTCH_SEQ>2</BTCH_SEQ>\n" +
                "<BTCH_RECEIVING_DT>2000-06-23T11:10:00.000Z</BTCH_RECEIVING_DT>\n" +
                "<MSGTYP_NAME>Message type name 2</MSGTYP_NAME>\n" +
                "<MSGTYP_DESC>Message type description 2</MSGTYP_DESC>\n" +
                "</ACH_INVLD_BATCH_RPT_RECORD>\n" +
                "</ArrayOfACH_INVLD_BATCH_RPT_RECORD>";
    }
}
