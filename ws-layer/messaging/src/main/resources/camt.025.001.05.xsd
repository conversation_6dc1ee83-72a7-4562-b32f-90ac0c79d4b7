<?xml version="1.0" encoding="UTF-8"?>
<!--- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
Legal Notices

SWIFT SCRL@2016. All rights reserved.

This schema is a component of MyStandards, the SWIFT collaborative Web application used to manage
standards definitions and industry usage.

This is a licensed product, which may only be used and distributed in accordance with MyStandards License
Terms as specified in MyStandards Service Description and the related Terms of Use.

Unless otherwise agreed in writing with SWIFT SCRL, the user has no right to:
 - authorise external end users to use this component for other purposes than their internal use.
 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices appearing in this physical medium.
 - re-sell or authorise another party e.g. software and service providers, to re-sell this component.

This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties
with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual 
property rights of whatever nature in this component will remain the exclusive property of SWIFT or its 
licensors.

Trademarks
SWIFT is the trade name of S.W.I.F.T. SCRL.
The following are registered trademarks of SWIFT: the SWIFT logo, SWIFT, SWIFTNet, SWIFTReady, Accord, Sibos, 3SKey, Innotribe, the Standards Forum logo, MyStandards, and SWIFT Institute.
Other product, service, or company names in this publication are trade names, trademarks, or registered trademarks of their respective owners.
- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

Group: CMA Small Systems
Collection: Kuwait RTGS (restricted)
Usage Guideline: Receipt_camt.025.001.05
Base Message: camt.025.001.05
Date of publication: 23 November 2020
URL: https://www2.swift.com/mystandards/#/mp/mx/_Tx40wPpTEemXdbviE8W5HA/_Tx40xvpTEemXdbviE8W5HA
Generated by the MyStandards web platform [http://www.swift.com/mystandards] on 2020-11-23T15:03:16+00:00
-->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:camt.025.001.05" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.025.001.05">
    <xs:element name="Document" type="Document"/>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6__1">
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CMA_Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemMemberIdentification2__1">
        <xs:sequence>
            <xs:element name="MmbId" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="Rct" type="ReceiptV05"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="EntryTypeIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[BEOVW]{1,1}[0-9]{2,2}|DUM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIDec2014Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="CMA_Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification1__1">
        <xs:sequence>
            <xs:element name="Id" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ISODate">
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="LEIIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="LongPaymentIdentification2__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TxId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UETR" type="UUIDv4Identifier"/>
            <xs:element name="IntrBkSttlmAmt" type="RestrictedFINImpliedCurrencyAndAmount"/>
            <xs:element name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtMtd" type="PaymentOrigin1Choice__1"/>
            <xs:element name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="NtryTp" type="EntryTypeIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EndToEndId" type="RestrictedFINXMax16Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Max16Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max3NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max4AlphaNumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9]{1,4}"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MessageHeader9__1">
        <xs:sequence>
            <xs:element name="MsgId" type="RestrictedFINXMax16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ReqTp" type="RequestType4Choice__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalMessageAndIssuer1__1">
        <xs:sequence>
            <xs:element name="MsgId" type="RestrictedFINXMax16Text"/>
            <xs:element name="MsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgtrNm" type="RestrictedFINXMax35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentIdentification6Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="LngBizId" type="LongPaymentIdentification2__1"/>
                <xs:element name="PrtryId" type="RestrictedFINXMax35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PaymentInstrument1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BDT"/>
            <xs:enumeration value="BCT"/>
            <xs:enumeration value="CDT"/>
            <xs:enumeration value="CCT"/>
            <xs:enumeration value="CHK"/>
            <xs:enumeration value="BKT"/>
            <xs:enumeration value="DCP"/>
            <xs:enumeration value="CCP"/>
            <xs:enumeration value="RTI"/>
            <xs:enumeration value="CAN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PaymentOrigin1Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="FINMT" type="Max3NumericText"/>
                <xs:element name="XMLMsgNm" type="Max35Text"/>
                <xs:element name="Prtry" type="RestrictedFINXMax35Text"/>
                <xs:element name="Instrm" type="PaymentInstrument1Code"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PostalAddress24__1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AdrLine" type="CMA_Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Receipt3__1">
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="OriginalMessageAndIssuer1__1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlPmtId" type="PaymentIdentification6Choice__1"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="ReqHdlg" type="RequestHandling1__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReceiptV05">
        <xs:sequence>
            <xs:element name="MsgHdr" type="MessageHeader9__1"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="RctDtls" type="Receipt3__1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestHandling1__1">
        <xs:sequence>
            <xs:element name="StsCd" type="Max4AlphaNumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Desc" type="RestrictedFINXMax140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RequestType4Choice__1">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Prtry" type="GenericIdentification1__1"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="RestrictedFINImpliedCurrencyAndAmount">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="14"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="RestrictedFINXMax140Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.\n\r,'\+ ]{1,140}"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="RestrictedFINXMax16Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="([0-9a-zA-Z\-\?:\(\)\.,'\+ ]([0-9a-zA-Z\-\?:\(\)\.,'\+ ]*(/[0-9a-zA-Z\-\?:\(\)\.,'\+ ])?)*)"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="RestrictedFINXMax35Text">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9a-zA-Z/\-\?:\(\)\.,'\+ ]{1,35}"/>
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
