package com.progressoft.kw.ach.messaging.pullswiftmessage;


import com.progressoft.kw.ach.messaging.pacs2.StatusReportSerializer;
import com.progressoft.kw.ach.messaging.pullswiftmessage.model.response.PullSwiftMessageResponse;
import com.progressoft.kw.ach.messaging.util.ContextSupplier;
import com.progressoft.kw.ach.messaging.util.JaxbSerializer;

import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.remove;

public class PullSwiftMessageSerializer {
    private static final StatusReportSerializer PACS_2_MESSAGE_SERIALIZER = new StatusReportSerializer();
    private static final ContextSupplier CONTEXT_SUPPLIER = new ContextSupplier(org.tempuri.PullSwiftMessageResponse.class.getPackage().getName());
    private static final JaxbSerializer PULL_SWIFT_SERIALIZER = new JaxbSerializer(CONTEXT_SUPPLIER);

    public String serialize(PullSwiftMessageResponse response) {
        return cleanXml(PULL_SWIFT_SERIALIZER.serialize(toXmlResponseData(response)));
    }

    private org.tempuri.PullSwiftMessageResponse toXmlResponseData(PullSwiftMessageResponse object) {
        org.tempuri.PullSwiftMessageResponse pullSwiftMessageResponse = new org.tempuri.PullSwiftMessageResponse();
        String pacs2;
        if (object.getDocumentModel() != null) {
            pacs2 = PACS_2_MESSAGE_SERIALIZER.serialize(object.getDocumentModel());
        } else {
            pacs2 = EMPTY;
        }

        pullSwiftMessageResponse.setPullSwiftMessageResult(pacs2);
        if (object.getSwiftXmlMessage() != null) {
            pullSwiftMessageResponse.setSwiftXmlMessage(object.getSwiftXmlMessage());
        } else {
            pullSwiftMessageResponse.setSwiftXmlMessage(EMPTY);
        }
        return pullSwiftMessageResponse;
    }

    private String cleanXml(String xml) {
        String directive = remove(xml, "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"yes\"?>\n");
        String documentDirective = remove(directive, "&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot; standalone=&quot;yes&quot;?&gt;");
        String namespace = remove(documentDirective, "<PullSwiftMessageResponse xmlns=\"http://tempuri.org/\" xmlns:ns2=\"http://www.Progressoft.com/ACH\" xmlns:ns3=\"urn:iso:std:iso:20022:tech:xsd:pacs.002.001.02\">\n");
        return remove(namespace, "</PullSwiftMessageResponse>");
    }
}
