package com.progressoft.ach.participant;

import com.progressoft.ach.MessageType;
import com.progressoft.ach.account.Account;
import com.progressoft.ach.branch.Branch;
import com.progressoft.ach.categorypurpose.CategoryPurpose;
import com.progressoft.ach.currency.Currency;
import com.progressoft.ach.transactionpurpose.TransactionPurpose;
import com.progressoft.communication.shared.AppEntity;
import com.progressoft.communication.shared.AppId;
import com.progressoft.jfw.AppUnhandledException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

import static com.progressoft.ach.PaymentValidationException.raise;
import static com.progressoft.ach.reason.Reason.*;
import static org.apache.commons.lang3.StringUtils.EMPTY;

final class NullParticipant extends Participant {
	static final String NOT_FOUND = "%s is not found";

	@Override
	public Account account(AppId currencyId) {
		return Account.NULL;
	}

	@Override
	public Branch branch(AppId identifier) {
		return Branch.NULL;
	}

	@Override
	public void validateAsCommunicating(AppEntity batch, Participant instructingParticipant) {
		raise(NOT_FOUND, COMMUNICATING);
	}

	@Override
	public void validateAsCreditor(Account account, MessageType messageType) {
		raise(CREDITOR_AGENT_DOES_NOT_EXIST, NOT_FOUND, CREDITOR);
	}

	@Override
	public void validateAsDebtor(Account account, MessageType messageType) {
		raise(DEBTOR_AGENT_DOES_NOT_EXIST, NOT_FOUND, DEBTOR);
	}

	@Override
	public void validateAsInstructed(MessageType messageType) {
		raise(INSTRUCTED_AGENT_DOES_NOT_EXIST, NOT_FOUND, INSTRUCTED);
	}

	@Override
	public void validateAsInstructing(MessageType messageType, Integer txSize) {
		raise(INSTRUCTING_AGENT_DOES_NOT_EXIST, NOT_FOUND, INSTRUCTING);
	}

	@Override
	public void validateAsInstructing() {
		raise(INSTRUCTING_AGENT_DOES_NOT_EXIST, NOT_FOUND, INSTRUCTING);
	}

	@Override
	public void validateCreditTxPertBatchLimit(Integer txSize) {
		throw new AppUnhandledException();
	}

	@Override
	public void validateMatching(Participant replyParticipant, String type) {
		raise(NOT_FOUND, type);
	}

	@Override
	public void validateMatching(Participant replyParticipant, String type, AppId reason, String additionalInfo) {
		raise(NOT_FOUND, type);
	}

	@Override
	public void validateNotMatching(Participant other) {
		throw new AppUnhandledException();
	}

	@Override
	public void validateTransaction(Participant sender, BigDecimal amount, Currency currency, MessageType messageType, CategoryPurpose categoryPurpose, TransactionPurpose transactionPurpose) {
		throw new AppUnhandledException();
	}

	@Override
	AppId communicatingParticipantId() {
		return AppId.NULL;
	}

	@Override
	public String prefix() {
		return StringUtils.EMPTY;
	}

	@Override
	public String name() {
		return StringUtils.EMPTY;
	}

	@Override
	public void validateDebitTxPertBatchLimit(Integer txSize) {
		throw new AppUnhandledException();
	}

	@Override
	void validateLimits(BigDecimal amount, Currency currency, MessageType messageType) {
		throw new AppUnhandledException();
	}

	@Override
	void validateSettlement(Participant settlementParticipant) {
		raise(NOT_FOUND, SETTLEMENT);
	}

	@Override
	public void validatePrefix(String messageId) {
		raise(NOT_FOUND, COMMUNICATING);
	}

	@Override
	public String format() {
		return EMPTY;
	}

    @Override
    public String type() {
        return EMPTY;
    }

    @Override
    public long dailyBatchCount() {
        return 0L;
    }

    @Override
    public boolean isOnUsPaymentAllowed() {
        return false;
    }

    @Override
    public String pacsMessageVersion() {
        return EMPTY;
    }
}