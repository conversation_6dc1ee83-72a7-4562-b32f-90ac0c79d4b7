package com.progressoft.ach.batch;

import com.progressoft.ach.MessageType;
import com.progressoft.ach.PaymentParams;
import com.progressoft.ach.PersistenceCache;
import com.progressoft.ach.State;
import com.progressoft.ach.balancechange.BalanceChange;
import com.progressoft.ach.balancechange.CapExceededException;
import com.progressoft.ach.batch.RTGSBatchProcessor.BatchResponse;
import com.progressoft.ach.billbookentry.BillBookEntry;
import com.progressoft.ach.branch.Branch;
import com.progressoft.ach.capexceededaction.CapExceededAction;
import com.progressoft.ach.capexceededaction.CapExceededAction.CapExceededActions;
import com.progressoft.ach.categorypurpose.CategoryPurpose;
import com.progressoft.ach.categorypurposeprofile.CategoryPurposeProfile;
import com.progressoft.ach.currency.Currency;
import com.progressoft.ach.customer.Customer;
import com.progressoft.ach.participant.AgentValidator;
import com.progressoft.ach.participant.Participant;
import com.progressoft.ach.pendingpayment.PendingPaymentService;
import com.progressoft.ach.pendingpayment.PendingPaymentStatus;
import com.progressoft.ach.pendingpayment.PendingPaymentType;
import com.progressoft.ach.rbatch.RBatch;
import com.progressoft.ach.rbatch.RBatchBuilder;
import com.progressoft.ach.reason.Reason;
import com.progressoft.ach.session.RelatedSession;
import com.progressoft.ach.session.Session;
import com.progressoft.ach.source.Source;
import com.progressoft.ach.transaction.Transaction;
import com.progressoft.ach.transaction.TransactionDTO;
import com.progressoft.ach.transformers.SerializerFactory;
import com.progressoft.ach.transformers.dto.Priority;
import com.progressoft.ach.window.Window;
import com.progressoft.communication.*;
import com.progressoft.communication.shared.AppId;
import com.progressoft.communication.shared.AppPersistenceException;
import com.progressoft.communication.shared.AppServiceLoader;
import com.progressoft.communication.shared.AppValidationException;
import com.progressoft.jfw.model.service.utils.AppContext;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;

import static com.progressoft.ach.LogUtils.*;
import static com.progressoft.ach.MessageType.CREDIT;
import static com.progressoft.ach.PaymentValidationException.raiseIf;
import static com.progressoft.ach.State.*;
import static com.progressoft.ach.System.ACH;
import static com.progressoft.ach.System.PSYS;
import static com.progressoft.ach.participant.Participant.clearingHouse;
import static com.progressoft.ach.participant.Participant.isInterBank;
import static com.progressoft.ach.pendingpayment.PendingPaymentType.FUTURE_DATED;
import static com.progressoft.ach.pendingpayment.PendingPaymentType.ORIGINAL_PAYMENT;
import static com.progressoft.ach.reason.Reason.*;
import static com.progressoft.ach.transaction.Transaction.filter;
import static com.progressoft.ach.transaction.Transaction.generateResponses;
import static com.progressoft.communication.Direction.OUTWARD;
import static com.progressoft.communication.ProcessingState.SENT;
import static com.progressoft.communication.shared.SystemDateUtils.now;
import static com.progressoft.communication.shared.SystemDateUtils.today;

public class BatchImpl extends Batch {
    private static final AppServiceLoader<SerializerFactory> serializers = new AppServiceLoader<>(SerializerFactory.class);
    private static final AppServiceLoader<BatchLimits> batchLimitsLoader = new AppServiceLoader<>(BatchLimits.class);
    protected static final AppServiceLoader<PaymentParams> paymentParamsLoader = new AppServiceLoader<>(PaymentParams.class);
    private static final AppServiceLoader<BatchService> serviceLoader = new AppServiceLoader<>(BatchService.class);
    protected static final AppServiceLoader<PendingPaymentService> pendingPaymentServiceLoader = new AppServiceLoader<>(PendingPaymentService.class);

    private RBatch response = RBatch.NULL;
    protected CategoryPurposeProfile categoryPurposeProfile = CategoryPurposeProfile.NULL;

    MessageType messageType = MessageType.NULL;
    CategoryPurpose categoryPurpose = CategoryPurpose.NULL;
    Date settlementDate = today();
    Currency currency = Currency.NULL;
    Source source = Source.NULL;
    Participant communicatingParticipant = Participant.NULL;
    Date creationDate = now();
    Participant instructingParticipant = Participant.NULL;
    Branch instructingBranch = Branch.NULL;
    Branch instructedBranch = Branch.NULL;
    Participant instructedParticipant = Participant.NULL;
    String state = State.UNDEFINED;
    Reason reason = Reason.NULL;
    String additionalInfo = StringUtils.EMPTY;
    List<Transaction> transactions = new ArrayList<>();
    String priority = Priority.NORM.toString();
    Session session = Session.NULL;
    Window window = Window.NULL;
    String settlementMethod = StringUtils.EMPTY;
    String clearingSystem = StringUtils.EMPTY;
    String clearingChannel = StringUtils.EMPTY;
    Long count = 0l;
    String sourceBatchId;
    PendingPaymentType paymentType = ORIGINAL_PAYMENT;

    @Override
    public void cancel(RBatch cancellationBatch) {
        cancellationBatch.cancel(state);
    }

    @Override
    public void fillBatchInfo(TransactionDTO dto) {
        dto.setBatch(this);
        dto.setInstructingParticipant(instructingParticipant);
        dto.setInstructingBranch(instructingBranch);
        if (!instructedParticipant.equals(Participant.NULL))
            dto.setInstructedParticipant(instructedParticipant);
        dto.setInstructedBranch(instructedBranch);
        dto.setCategoryPurpose(categoryPurpose);
        dto.setSession(session);
        dto.setWindow(window);
    }

    @Override
    public long hashCodeInfo(BigDecimal amount, Customer creditor, Customer debtor) {
        return Customer.hashCodeInfo(amount, instructingParticipant, settlementDate, debtor, creditor);
    }

    @Override
    public void link(Transaction transaction) {
        transactions.add(transaction);
    }

    @Override
    public void process() throws RuntimeException {
        log(this).info(PROCESSING, this);
        init();
        validate();
        persist();
        persistResponse();
        List<Transaction> list = Transaction.filterAcceptedTransactions(transactions);
        if (!list.isEmpty() && isInterBank(instructingParticipant, instructedParticipant)) {
            validateLimits();
            updateBalance(list);
            new PaymentSortingHandler(paymentParamsLoader.load().isSortOnPriority()).process(this);
        }
        log(this).info(FINISHED_PROCESSING, this, state(state, reason, additionalInfo));
        if (!paymentType.equals(ORIGINAL_PAYMENT)) {
            log(this).info("Updating the pending payment status to he processed since the message is processed");
            pendingPaymentServiceLoader.load().updatePendingPaymentStatus(appId().code(), PendingPaymentStatus.PROCESSED.getStatus());
        }
    }

    @Override
    public RelatedSession relatedSession(MessageType messageType, Participant participant) {
        return session.relatedSession(messageType, participant);
    }

    @Override
    public void syncStatus() {
        loadTransactions();
        resolveState();
        serviceLoader.load().update(dto());
    }

    @Override
    public void validate() {
        try {
            validateBatch();
            Transaction.validate(transactions, instructingParticipant, instructedParticipant, categoryPurpose, settlementDate, communicatingParticipant);
            resolveState();
        } catch (AppValidationException ex) {
            log(this).error(ex.getMessage(), ex);
            updateState(transactions, State.REJECTED, ex.reasonId(), ex.getMessage());
        }
        log(this).info(FINISHED_VALIDATING, this, state(state, reason, additionalInfo));
    }

    @Override
    public void validateCancellation(Participant relatedInstructing, Participant relatedInstructed, Participant relatedCommunicating) {
        instructingParticipant.validateMatching(relatedInstructing, Participant.INSTRUCTING, INSTRUCTING_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTING_AGENT, "RFC Instructing Agent not matching original Instructing Agent");
        if (!relatedInstructed.equals(Participant.NULL))
            instructedParticipant.validateMatching(relatedInstructed, Participant.INSTRUCTED, INSTRUCTED_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTED_AGENT, "RFC Instructed Agent not matching original Instructed Agent");
        communicatingParticipant.validateMatching(relatedCommunicating, Participant.COMMUNICATING, SENDING_AGENT_NOT_MATCH_ORIGINAL_SENDING_AGENT, "RFC exchange Agent not matching original exchange Agent");
        State.validateState(this, state, ORIGINAL_BATCH_IN_INVALID_STATUS, "Original Batch With Invalid Status", ACCEPTED, PARTIALLY_ACCEPTED);
    }

    @Override
    public void validateReply(Participant relatedInstructing, Participant relatedInstructed, Participant relatedCommunicating) {
        instructedParticipant.validateMatching(relatedInstructing, Participant.INSTRUCTING, INSTRUCTING_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTED_AGENT, "Instructing Agent Not Matching Original Instructed Agent");
        if (!relatedInstructed.equals(Participant.NULL))
            instructingParticipant.validateMatching(relatedInstructed, Participant.INSTRUCTED, INSTRUCTED_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTING_AGENT, "Instructed Agent Not Matching Original Instructing Agent");
        instructedParticipant.validateMatching(relatedCommunicating, Participant.COMMUNICATING, CALLING_AGENT_IS_NOT_THE_RECEIVER_OF_ORIGINAL_BATCH, "Calling Agent Is Not The Receiver Of Original Batch");
        State.validateState(this, state, PAYMENT_STATUS_REPORT_WITH_INVALID_STATUS, "Payment Status Report With Invalid Status", ACCEPTED, PARTIALLY_ACCEPTED);
    }


    @Override
    public void handle(Throwable ex, String messageId) {
        AppPersistenceException ape = (AppPersistenceException) ex;
        CapExceededAction action = transactions.get(0).retrieveAction();
        if (ex instanceof CapExceededException) {
            action.applyAction(this, ape);
            if (action.isImmediateSettlement())
                return;
        } else
            updateState(transactions, State.REJECTED, ape.reasonId(), ape.getMessage());
        persist();
        persistResponse();
    }

    protected void resolveState() {
        String centralBankCode = paymentParamsLoader.load().getCentralBankCode();
        state = transactions.stream().anyMatch(txn -> REJECTED.equals(txn.getState()))
                && (categoryPurpose.isFullRejection() || instructingParticipant.code().equals(centralBankCode))
                ? State.REJECTED
                : Transaction.batchState(state, transactions);
    }

    @Override
    public void persistResponse() {
        response = generateResponse();
        response.persist();
    }

    @Override
    public void reject(AppPersistenceException ex, CapExceededActions action) {
        updateState(filter(transactions, ACCEPTED), REJECTED, ex.reasonId(), ex.getMessage());
    }

    @Override
    public void validateReturnOrReversal(MessageType relatedMessageType,MessageType originalMessageType) {
        State.validateState(this, state, State.SETTLED);
        session.validateReturnReversal(relatedMessageType,originalMessageType);
    }

    @Override
    public MessageType messageType() {
        return messageType;
    }

    @Override
    public Currency currency() {
        return currency;
    }

    @Override
    public String getState() {
        return state;
    }

    @Override
    public void processRTGSResponse(BatchResponse rtgsResponse) {
        loadTransactions();
        if (rtgsResponse.equals(BatchResponse.ACCEPT))
            changeStateTo(SETTLED);
        else
            changeStateTo(REJECTED);
        serviceLoader.load().update(dto());
    }

    @Override
    public void populateTransactionCache(PersistenceCache persistenceCache) {
        serviceLoader.load().populateTransactionCache(appId(), persistenceCache);
    }

    public Batch convertToImmediate() {
        return new ImmediateBatchImpl(this);
    }

    public void setAppId(AppId id) {
        appId(id);
    }

    RBatchBuilder rBatchBuilder() {
        return new RBatchBuilder();
    }

    RBatch generateResponse() {
        AppId rBatchId = Participant.newId();
        return rBatchBuilder()
                .id(rBatchId)
                .state(ACCEPTED)
                .messageType(messageType.replyType())
                .originalMessageType(messageType)
                .communicatingParticipant(clearingHouse())
                .instructingParticipant(clearingHouse())
                .instructedParticipant(instructingParticipant)
                .originalBatch(this)
                .originalState(state)
                .originalReason(reason)
                .source(source)
                .session(session)
                .window(window)
                .originalAdditionalInfo(additionalInfo)
                .transactions(generateResponses(rBatchId, transactions))
                .build();
    }

    void persistMessage(List<Transaction> list) {
        LockInfo lockInfo = LockInfo.generate(LocalDateTime.now());
        Message message = Message.newBuilder()
                .setId(MessageId.of(appId().code()).value())
                .setDirection(OUTWARD)
                .setSource(Side.of(ACH, paymentParamsLoader.load().getDefaultTenant()).value())
                .setDestination(Side.of(PSYS, instructedParticipant.name()).value())
                .setType(com.progressoft.communication.MessageType.of(messageType.code()).value())
                .setFormat(MessageFormat.of(instructedParticipant.format()).value())
                .setContent(MessageContent.of(content(list, instructedParticipant)).value())
                .setOriginalInfo(OriginalInfo.of(code(), messageType.code()).value())
                .setMessageDate(LocalDateTime.now())
                .setCommunicationDate(LocalDateTime.now())
                .setProcessingState(SENT)
                .setLockInfo(lockInfo)
                .setRequiresAck(false)
                .build().value();
        AppContext.getApplicationContext().getBean(MessageRepository.class).persist(message);
    }

    private void changeStateTo(String newState) {
        this.state = newState;
        Transaction.setState(transactions, newState, Reason.NULL, "Batch is " + newState + " by RTGS.");
    }


    protected void init() {
        currency = Transaction.currency(transactions);
        categoryPurposeProfile = CategoryPurposeProfile.get(categoryPurpose, messageType, currency);
        instructingParticipant = communicatingParticipant;
        initWindow();
    }

    protected void initWindow() {
        window = Window.findAvailable(categoryPurpose, messageType, instructingParticipant, settlementDate, currency, priority);
        session = window.session();
        if (paymentType.equals(FUTURE_DATED)){
            settlementDate = session.settlementDate();
            updatePendingPaymentSettlementDate(settlementDate);
        }
    }

    private void updatePendingPaymentSettlementDate(Date settlementDate) {
        String messageId = appId().code();
        pendingPaymentServiceLoader.load().updatePendingPaymentSettlementDate(messageId, settlementDate);
    }

    public BatchDTO dto() {
        return fill(new BatchDTO());
    }

    <T extends BatchDTO> T fill(T dto) {
        dto.setId(appId());
        dto.setCount(transactions.size());
        dto.setCurrency(Transaction.currency(transactions));
        dto.setSettlementDate(settlementDate);
        dto.setAmount(Transaction.totalAmount(transactions));
        dto.setMessageType(messageType);
        dto.setCategoryPurpose(categoryPurpose);
        dto.setSource(source);
        dto.setCommunicatingParticipant(communicatingParticipant);
        dto.setInstructingParticipant(instructingParticipant);
        dto.setInstructingBranch(instructingBranch);
        dto.setInstructedParticipant(instructedParticipant);
        dto.setInstructedBranch(instructedBranch);
        dto.setState(state);
        dto.setReason(reason);
        dto.setWindow(window);
        dto.setSession(session);
        dto.setCreationDate(creationDate);
        dto.setAdditionalInfo(additionalInfo);
        dto.setPriority(priority);
        dto.setSourceBatchId(sourceBatchId);
        dto.setIsPulled(false);
        return dto;
    }

    private void loadTransactions() {
        transactions.clear();
        Transaction.link(Transaction.list(appId()), this);
    }

    private void persist() {
        log(this).info(PERSISTING, this);
        appId(serviceLoader.load().persist(dto()));
        if (!reason.appId().equals(DUPLICATE_BATCH_ID) && !reason.appId().equals(DUPLICATE_TRANSACTION_ID))
            serviceLoader.load().persistInHistoryBatch(appId(), creationDate);
        Transaction.persist(transactions);
    }

    public void persistOutward(List<Transaction> orgTxList) {
        log(this).info(PERSISTING, this);
        appId(serviceLoader.load().persistOutward(dto()));
        Transaction.persistOutward(transactions);
    }

    public BatchDTO getBatchDto() {
        BatchDTO dto = dto();
        dto.setTransactions(Transaction.serialize(transactions));
        return dto;
    }

    private String content(List<Transaction> list, Participant participant) {
        BatchDTO dto = dto();
        dto.setTransactions(Transaction.serialize(list));
        return serializers.load().get(participant.format(), messageType.code()).serialize(dto);
    }

    private void setState(String state, Reason reason, String additionalInfo) {
        this.state = state;
        this.reason = reason;
        this.additionalInfo = additionalInfo;
    }

    private void updateBalance(List<Transaction> acceptedTransactions) {
        List<BillBookEntry> entries = Transaction.billBookEntries(acceptedTransactions, session);
        BillBookEntry.persist(entries);
        BalanceChange.reflect(BillBookEntry.changes(entries), session.appId());
    }

    protected void updateState(List<Transaction> list, String state, AppId reasonCode, String additionalInfo) {
        Reason reason = Reason.get(reasonCode);
        setState(state, reason, additionalInfo);
        Transaction.setState(list, state, reason, additionalInfo);
    }

    private void validateBatch() {
        messageType.validateBatch();
        source.validate();
        currency.validate();
        categoryPurpose.validateBatch(instructingParticipant);
        validateBatchClearing();
        communicatingParticipant.validateAsCommunicating(this, instructingParticipant);
        if (!instructingParticipant.equals(Participant.NULL)) {
            instructingParticipant.validateAsInstructing(messageType, transactions.size());
            if (!instructingBranch.equals(Branch.NULL))
                instructingBranch.validateAsInstructing();
        }
        if (!instructedParticipant.equals(Participant.NULL)) {
            instructedParticipant.validateAsInstructed(messageType);
            instructedBranch.validateAsInstructed();
        }
        session.validate(window);
        validateSettlementDate();
        categoryPurposeProfile.validateBatch(transactions.size());
        validateBatchReceivingAgent();
        validateBatchSendingAgent();
        validateDuplicateBatchId();
        validateDuplicateTransaction();
        validateBatchId();
        validateBatchCountPerAgentPerSession();
        validateBatchTransactionCount();
    }

    protected void validateBatchReceivingAgent() {
        if (null != transactions) {
            List<TransactionDTO> transactionDTOList = Transaction.serialize(transactions);
            if (instructedParticipant != null) {
                for (TransactionDTO transactionDTO : transactionDTOList) {
                    Participant settlementAgent = getSettlementAgentForReceiving(transactionDTO);
                    raiseIf(!AgentValidator.isCommunicatingFor(instructedParticipant, settlementAgent),
                            messageType.equals(CREDIT) ? BATCH_HAS_INVALID_CORRESPONDENT_CREDITOR_AGENT : BATCH_HAS_INVALID_CORRESPONDENT_DEBTOR_AGENT,
                            "One or more transaction creditor/debtor agent ID is different than batch instructed agent");
                }
            }
        }
    }

    protected void validateBatchSendingAgent() {
        if (null != transactions) {
            List<TransactionDTO> transactionDTOList = Transaction.serialize(transactions);
            if (instructingParticipant != null) {
                for (TransactionDTO transactionDTO : transactionDTOList) {
                    Participant settlementAgent = getSettlementAgentSending(transactionDTO);
                    raiseIf(!AgentValidator.isCommunicatingFor(instructingParticipant, settlementAgent),
                            messageType.equals(CREDIT) ? BATCH_HAS_INVALID_CORRESPONDENT_DEBTOR_AGENT : BATCH_HAS_INVALID_CORRESPONDENT_CREDITOR_AGENT,
                            "One or more transaction creditor/debtor agent ID is different than batch instructing agent");
                }
            }
        }
    }

    private void validateSettlementDate() {
        raiseIf(window == Window.NULL ||
                        (!paymentType.equals(FUTURE_DATED) && !window.session().settlementDate().equals(settlementDate)),
                INVALID_PRESENTMENT_CYCLE, "Invalid Presentment Cycle");

    }

    private Participant getSettlementAgentForReceiving(TransactionDTO transactionDTO) {
        if (messageType == CREDIT) {
            return transactionDTO.getCreditorParticipant();
        } else if (messageType == MessageType.DEBIT) {
            return transactionDTO.getDebtorParticipant();
        }
        return null;
    }

    private Participant getSettlementAgentSending(TransactionDTO transactionDTO) {
        if (messageType == CREDIT) {
            return transactionDTO.getDebtorParticipant();
        } else if (messageType == MessageType.DEBIT) {
            return transactionDTO.getCreditorParticipant();
        }
        return null;
    }

    protected void validateBatchClearing() {
        raiseIf(null == this.settlementMethod, INVALID_SETTLEMENT_METHOD, "Invalid batch settlement method");
        String settlementMethod = paymentParamsLoader.load().getSettlementMethod();
        raiseIf(!this.settlementMethod.equalsIgnoreCase(settlementMethod), INVALID_SETTLEMENT_METHOD, "Invalid batch settlement method");

        String clearingSystem = paymentParamsLoader.load().getClearingSystem();
        raiseIf(null == this.clearingSystem, INVALID_CLEARING_SYSTEM, "Invalid batch clearing system");
        raiseIf(!clearingSystem.equalsIgnoreCase(this.clearingSystem), INVALID_CLEARING_SYSTEM, "Invalid batch clearing system");

        raiseIf(!StringUtils.isEmpty(this.clearingChannel) && !isValidClearingChannel(),
                INVALID_CLEARING_CHANNEL, "Invalid batch clearing channel");
    }

    private boolean isValidClearingChannel() {
        String clearingChannel = paymentParamsLoader.load().getClearingChannel();
        return this.clearingChannel.equalsIgnoreCase(clearingChannel) || this.clearingChannel.equalsIgnoreCase("RTNS");
    }

    protected void validateDuplicateBatchId() {
        Batch batch = Batch.get(this.appId());
        raiseIf(!batch.equals(NULL) || serviceLoader.load().isDuplicateBatchId(this.appId()), DUPLICATE_BATCH_ID, "Batch ID is duplicate");
    }

    protected void validateDuplicateTransaction() {
        raiseIf(transactions.stream().map(Transaction::appId).distinct().count() != transactions.size(), BATCH_HAS_DUPLICATE_TRANSACTION,
                "Batch contains duplicate transactions");
    }

    private void validateLimits() {
        batchLimitsLoader.load().validate(instructingParticipant, settlementDate, session, currency, messageType, transactions.size());
    }


    protected void validateBatchCountPerAgentPerSession() {
        batchLimitsLoader.load().validateBatchLimit(instructingParticipant, settlementDate, session, currency, messageType);
    }

    protected void validateBatchId() {
        Pattern pattern = Pattern.compile(paymentParamsLoader.load().getRegexpBatchId());
        raiseIf(!pattern.matcher(appId().code()).matches(), BATCHID_IS_NOT_VALID, "Batch ID is not valid");
    }

    protected void validateBatchTransactionCount() {
        raiseIf(transactions.size() != count, BATCHTX_COUNT_MISMATCH_BETWEEN_SPECIFIED_AND_EXISTING, "Actual number of transactions per batch does not match specified count batch");
        batchLimitsLoader.load().validateTransactionLimit(instructingParticipant, settlementDate, session, currency, messageType, transactions.size());
    }

    public Participant getInstructedParticipant() {
        return instructedParticipant;
    }

    public Participant getInstructingParticipant() {
        return instructingParticipant;
    }

    public Participant getCommunicatingParticipant() {
        return communicatingParticipant;
    }

    public Session getSession() {
        return session;
    }

    public Date getCreationDate() {
        return creationDate;
    }

    public Reason getReason() {
        return reason;
    }

    public Long getCount() {
        return count;
    }
}
