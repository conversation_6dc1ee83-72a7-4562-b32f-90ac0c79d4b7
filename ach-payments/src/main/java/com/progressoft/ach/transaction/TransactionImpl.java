package com.progressoft.ach.transaction;

import com.progressoft.ach.MessageType;
import com.progressoft.ach.PaymentParams;
import com.progressoft.ach.PersistenceCache;
import com.progressoft.ach.State;
import com.progressoft.ach.account.Account;
import com.progressoft.ach.batch.Batch;
import com.progressoft.ach.batch.BatchImpl;
import com.progressoft.ach.billbookentry.BillBookEntry;
import com.progressoft.ach.billbookentry.BillBookEntryBuilder;
import com.progressoft.ach.branch.Branch;
import com.progressoft.ach.capexceededaction.CapExceededAction;
import com.progressoft.ach.capexceededaction.CapExceededActionService;
import com.progressoft.ach.categorypurpose.CategoryPurpose;
import com.progressoft.ach.categorypurposeprofile.CategoryPurposeProfile;
import com.progressoft.ach.currency.Currency;
import com.progressoft.ach.customer.Customer;
import com.progressoft.ach.participant.AgentValidator;
import com.progressoft.ach.participant.Participant;
import com.progressoft.ach.reason.Reason;
import com.progressoft.ach.session.Session;
import com.progressoft.ach.transactionpurpose.TransactionPurpose;
import com.progressoft.ach.transactionpurpose.TransactionPurposeService;
import com.progressoft.ach.transformers.dto.Priority;
import com.progressoft.communication.shared.AppId;
import com.progressoft.communication.shared.AppServiceLoader;
import com.progressoft.communication.shared.AppValidationException;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static com.progressoft.ach.LogUtils.*;
import static com.progressoft.ach.MessageType.CREDIT;
import static com.progressoft.ach.MessageType.DEBIT;
import static com.progressoft.ach.PaymentValidationException.raiseIf;
import static com.progressoft.ach.State.*;
import static com.progressoft.ach.customer.Customer.CREDITOR;
import static com.progressoft.ach.customer.Customer.DEBTOR;
import static com.progressoft.ach.participant.ParticipantResolverUtil.resolveInstructedAgent;
import static com.progressoft.ach.participant.ParticipantResolverUtil.resolveInstructingAgent;
import static com.progressoft.ach.reason.Reason.*;
import static com.progressoft.communication.shared.SystemDateUtils.now;
import static java.math.BigDecimal.ZERO;
import static org.apache.commons.lang3.StringUtils.EMPTY;
import static org.apache.commons.lang3.StringUtils.isEmpty;

public class TransactionImpl extends Transaction {
    private static final AppServiceLoader<CapExceededActionService> capExceededActionServiceAppServiceLoader = new AppServiceLoader<>(CapExceededActionService.class);
    private static final AppServiceLoader<TransactionService> serviceLoader = new AppServiceLoader<>(TransactionService.class);
    private static final AppServiceLoader<PaymentParams> paymentParamsLoader = new AppServiceLoader<>(PaymentParams.class);
    private static final AppServiceLoader<TransactionPurposeService> transactionPurposeServiceLoader = new AppServiceLoader<>(TransactionPurposeService.class);
    static final String INVALID_AMOUNT = "Invalid Amount";
    static final String AMOUNT_MISMATCH = "Amount %s of %s is less than return/reversal amount %s";
    static final String RELATED_AMOUNT_MISMATCH = "Return/Reversal amount %s of %s is less than the transaction amount %s";
    static final String CURRENCY_MISMATCH = "%s for %s is not matching return / reversal %s";
    public static final String ADDITIONAL_INFO_FORMAT = "%s: (%s)";

    Participant instructingParticipant = Participant.NULL;
    Participant instructedParticipant = Participant.NULL;
    long paymentSequence = 0L;
    Date collectionDate = now();
    String instructionId = StringUtils.EMPTY;
    String endToEndId = StringUtils.EMPTY;
    BigDecimal amount = BigDecimal.ZERO;
    Currency currency = Currency.NULL;
    long hashCodeInfo = Customer.EMPTY_HASH_CODE_INFO;
    boolean isDuplicate = Boolean.FALSE;
    String state = StringUtils.EMPTY;
    Reason reason = Reason.NULL;
    String additionalInfo = StringUtils.EMPTY;
    Participant debtorParticipant = Participant.NULL;
    Branch debtorBranch = Branch.NULL;
    Account debtorAccount = Account.NULL;
    Customer debtorCustomer = Customer.NULL;
    Participant creditorParticipant = Participant.NULL;
    Branch creditorBranch = Branch.NULL;
    Account creditorAccount = Account.NULL;
    Customer creditorCustomer = Customer.NULL;
    String mandateId = StringUtils.EMPTY;
    Batch batch = Batch.NULL;
    MessageType messageType = MessageType.NULL;
    TransactionPurpose transactionPurpose = TransactionPurpose.NULL;
    String remittance = StringUtils.EMPTY;
    String clearingChannel = EMPTY;
    String outTransactionId;
    Priority priority;
    boolean isRtgsPulled;

    void setAppId(AppId id) {
        appId(id);
    }

    public Participant getInstructedParticipant() {
        return instructedParticipant;
    }

    @Override
    public BillBookEntry billBookEntry(Session session) {
        return new BillBookEntryBuilder()
                .transactionId(code())
                .messageType(messageType)
                .session(session)
                .amount(amount)
                .currency(currency)
                .creditorAccount(creditorAccount)
                .debtorAccount(debtorAccount)
                .build();
    }

    void calculateHashCode() {
        this.hashCodeInfo = batch.hashCodeInfo(amount, creditorCustomer, debtorCustomer);
    }

    @Override
    public void processRelatedReply(String relatedState) {
        State.process(relatedState,
                () -> updateState(State.advance(state), reason, additionalInfo),
                () -> updateState(State.revert(state), Reason.NULL, EMPTY));
    }

    @Override
    public void updateState(PersistenceCache persistenceCache, String state, Reason reason, String additionalInfo) {
        setState(targetState(state, reason), reason, additionalInfo);
        log(this).info("Updating {}", this);
        serviceLoader.load().update(persistenceCache, dto());
    }

    private String targetState(String state, Reason reason) {
        String result = state;
        if (State.CANCELLATION_REQUESTED.equals(state)) {
            if (PENDING.equals(this.getState()))
                result = State.REJECTED;
            else
                result = reason.is(Reason.FORCED) ? REJECTED : state;
        }
        return result;
    }

    @Override
    public void validateCancellation(Participant relatedInstructing, Participant relatedInstructed) {
        State.validateState(this, state, ORIGINAL_TRANSACTION_IN_INVALID_STATUS, "Original Inward Transaction In Invalid Status", ACCEPTED, PENDING);
        if (!instructingParticipant.equals(Participant.NULL))
            instructingParticipant.validateMatching(relatedInstructing, Participant.INSTRUCTING, INSTRUCTING_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTING_AGENT, "RFC Instructing Agent not matching original Instructing Agent");
        if (!instructedParticipant.equals(Participant.NULL))
            instructedParticipant.validateMatching(relatedInstructed, Participant.INSTRUCTED, INSTRUCTED_AGENT_NOT_MATCHING_ORIGINAL_INSTRUCTED_AGENT, "RFC Instructed Agent not matching original Instructed Agent");
    }

    @Override
    public void validateReply() {
        State.validateState(this, state, ORIGINAL_TRANSACTION_IN_INVALID_STATUS, "Original Transaction In Invalid Status", ACCEPTED);
    }

    @Override
    protected void checkDuplicationAgainst(List<Transaction> list) {
        this.isDuplicate = duplicateIn(list) || duplicateInDb();
    }

    @Override
    protected void link(Batch batch) {
        this.batch = batch;
        batch.link(this);
    }

    @Override
    protected void persist(PersistenceCache persistenceCache) {
        log(this).info(PERSISTING, this);
        appId(serviceLoader.load().persist(persistenceCache, dto(), debtorCustomer.dto(), creditorCustomer.dto()));
        if (!reason.appId().equals(DUPLICATE_TRANSACTION_ID) && !((BatchImpl) batch).getReason().appId().equals(DUPLICATE_BATCH_ID))
            serviceLoader.load().persistInHistoryTransaction(appId(), ((BatchImpl) batch).getCreationDate());
    }

    protected void persistOutward(PersistenceCache persistenceCache) {
        log(this).info(PERSISTING, this);
        appId(serviceLoader.load().persistOutward(persistenceCache, dto(), debtorCustomer.dto(), creditorCustomer.dto()));
    }

    @Override
    protected void setState(String state, Reason reason, String additionalInfo) {
        this.state = state;
        this.reason = reason;
        this.additionalInfo = additionalInfo;
    }

    private Stream<TransactionImpl> stream(List<Transaction> list) {
        return list.stream().map(tx -> (TransactionImpl) tx);
    }

    @Override
    protected void validate(String acceptedState, Participant batchInstructingParticipant, CategoryPurpose categoryPurpose,
                            Date settlementDate, Participant communicatingParticipant) {
        try {
            validateTransaction(acceptedState, batchInstructingParticipant, categoryPurpose, settlementDate, communicatingParticipant);
        } catch (AppValidationException ex) {
            setState(State.REJECTED, Reason.get(ex.reasonId()), String.format(ADDITIONAL_INFO_FORMAT, code(), ex.getMessage()));
        }
        log(this).info(FINISHED_VALIDATING, this, state(state, reason, additionalInfo));
    }


    protected void validateForPendingTransaction(String acceptedState, Participant instructing, CategoryPurpose purpose, Participant communicatingParticipant) {
        try {
            validatePendingTransaction(acceptedState, instructing, purpose, communicatingParticipant);
        } catch (AppValidationException ex) {
            setState(State.REJECTED, Reason.get(ex.reasonId()), String.format(ADDITIONAL_INFO_FORMAT, code(), ex.getMessage()));
        }
        log(this).info(FINISHED_VALIDATING + "for pending transaction", this, state(state, reason, additionalInfo));
    }

    private void markAsRejected(AppValidationException ex) {
        log(this).error(ex.getMessage(), ex);
        setState(State.REJECTED, Reason.get(ex.reasonId()), ex.getMessage());
    }

    private void validateTransaction(String acceptedState, Participant batchInstructingParticipant, CategoryPurpose categoryPurpose,
                                     Date settlementDate, Participant communicatingParticipant) {
        validateTransactionId();
        resolveInstructingAndInstructedAgent();
        instructedParticipant.validateAsInstructed(messageType);
        instructingParticipant.validateAsInstructing();
        validateIban();
        validateClearingChannel();
        creditorParticipant.validateAsCreditor(creditorAccount, messageType);
        creditorAccount.validateAsCreditor();
        currency.validate();
        creditorAccount.validateCreditorBalance(creditorParticipant.appId(), settlementDate, currency.appId());
        validateDuplicateTx();
        debtorParticipant.validateAsDebtor(debtorAccount, messageType);
        debtorAccount.validateDebtorBalance(debtorParticipant.appId(), settlementDate, currency.appId());
        validateCrossAgent();
        validateEndToEndId();
        validateOnUs(instructingParticipant, instructedParticipant);
        validateExchangeAgent(communicatingParticipant);
        Participant sender = messageType.sender(debtorParticipant, creditorParticipant);
        batchInstructingParticipant.validateTransaction(sender, amount, currency, messageType, categoryPurpose, transactionPurpose);
        CategoryPurposeProfile.get(categoryPurpose, messageType, currency).validateTransaction(getAmount());
        validateTransactionPurpose(transactionPurpose);
        this.state = acceptedState;
    }


    private void validatePendingTransaction(String acceptedState, Participant batchInstructingParticipant, CategoryPurpose categoryPurpose, Participant communicatingParticipant) {
        validateTransactionId();
        resolveInstructingAndInstructedAgent();
        instructedParticipant.validateAsInstructed(messageType);
        instructingParticipant.validateAsInstructing();
        validateIban();
        validateClearingChannel();
        creditorParticipant.validateAsCreditor(creditorAccount, messageType);
        creditorAccount.validateAsCreditor();
        currency.validate();
        validateDuplicateTx();
        debtorParticipant.validateAsDebtor(debtorAccount, messageType);
        validateCrossAgent();
        validateEndToEndId();
        validateOnUs(instructingParticipant, instructedParticipant);
        validateExchangeAgent(communicatingParticipant);
        Participant sender = messageType.sender(debtorParticipant, creditorParticipant);
        batchInstructingParticipant.validateTransaction(sender, amount, currency, messageType, categoryPurpose, transactionPurpose);
        CategoryPurposeProfile.get(categoryPurpose, messageType, currency).validateTransaction(getAmount());
        validateTransactionPurpose(transactionPurpose);
        this.state = acceptedState;
    }


    private void resolveInstructingAndInstructedAgent() {
        instructingParticipant = instructingParticipant.equals(Participant.NULL) ?
                resolveInstructingAgent(messageType, creditorParticipant, debtorParticipant) : instructingParticipant;
        instructedParticipant = instructedParticipant.equals(Participant.NULL) ?
                resolveInstructedAgent(messageType, creditorParticipant, debtorParticipant) : instructedParticipant;
    }

    private void validateExchangeAgent(Participant communicatingParticipant) {
        if (CREDIT.equals(messageType)) {
            raiseIf(!AgentValidator.isCommunicatingFor(communicatingParticipant, debtorParticipant),
                    DEBTOR_AGENT_MISMATCH_WITH_CALLING_AGENT,
                    "Calling Agent is not communicating for the debtor agent");

        } else if (DEBIT.equals(messageType)) {
            raiseIf(!AgentValidator.isCommunicatingFor(communicatingParticipant, creditorParticipant),
                    CREDITOR_AGENT_MISMATCH_WITH_CALLING_AGENT,
                    "Calling Agent is not communicating for the creditor agent");

        }
    }

    private void validateOnUs(Participant instructingAgent, Participant instructedAgent) {
        if (instructingAgent != null && instructedAgent != null && instructingParticipant.equals(instructedParticipant)) {
            raiseIf(!paymentParamsLoader.load().isOnUsSupported(), ONUS_TRANSACTIONS_NOT_SUPPORTED,
                    "OnUs Transactions Not Supported");
            raiseIf(!instructingParticipant.isOnUsPaymentAllowed(), ONUS_TRANSACTIONS_NOT_SUPPORTED,
                    "Agent is not allowed to send OnUs Transactions");
        }
    }

    private void validateDuplicateTx() {
        Transaction transaction = Transaction.get(appId());
        raiseIf(!NULL.equals(transaction) || serviceLoader.load().isDuplicateTransactionId(appId()), DUPLICATE_TRANSACTION_ID,
                "Transaction already exists");
    }

    private void validateCrossAgent() {
        if (CREDIT.equals(messageType)) {
            raiseIf(!AgentValidator.isCommunicatingFor(instructedParticipant, creditorParticipant),
                    CREDITOR_AGENT_MISMATCH_WITH_INSTRUCTED_AGENT,
                    "Instructed Agent is not communicating for the creditor agent");
            raiseIf(!AgentValidator.isCommunicatingFor(instructingParticipant, debtorParticipant),
                    DEBTOR_AGENT_MISMATCH_WITH_INSTRUCTING_AGENT,
                    "Instructing Agent is not communicating for the debtor agent");
        } else if (DEBIT.equals(messageType)) {
            raiseIf(!AgentValidator.isCommunicatingFor(instructedParticipant, debtorParticipant),
                    DEBTOR_AGENT_MISMATCH_WITH_INSTRUCTED_AGENT,
                    "Instructed Agent is not communicating for the debtor agent");
            raiseIf(!AgentValidator.isCommunicatingFor(instructingParticipant, creditorParticipant),
                    CREDITOR_AGENT_MISMATCH_WITH_INSTRUCTING_AGENT,
                    "Instructing Agent is not communicating for the creditor agent");
        }
    }

    private void validateClearingChannel() {
        raiseIf(!StringUtils.isEmpty(this.clearingChannel) && !isValidClearingChannel(),
                INVALID_CLEARING_CHANNEL, "Invalid transaction clearing channel");
    }

    private boolean isValidClearingChannel() {
        String clearingChannel = paymentParamsLoader.load().getClearingChannel();
        return this.clearingChannel.equalsIgnoreCase(clearingChannel) || this.clearingChannel.equalsIgnoreCase("RTNS");
    }

    private void validateIban() {
        if (Participant.NULL == creditorParticipant || Participant.NULL == debtorParticipant)
            return;
        if (Account.NULL == creditorAccount || Account.NULL == debtorAccount)
            return;
        debtorCustomer.validateIban(DEBTOR,debtorParticipant.code());
        creditorCustomer.validateIban(CREDITOR,creditorParticipant.code());
    }

    private void validateTransactionId() {
        Pattern pattern = Pattern.compile(paymentParamsLoader.load().getRegexpTransactionId());
        raiseIf(!pattern.matcher(appId().code()).matches(), TRANSACTION_ID_NOT_VALID,
                "Transaction ID is not valid [%s]", appId().code());
    }

    private void validateEndToEndId() {
        raiseIf(isEmpty(endToEndId), TRANSACTION_END_TO_END_ID_IS_NOT_VALID, "End to End ID is not Valid");
    }


    private void validateTransactionPurpose(TransactionPurpose transactionPurpose) {
        validateTxPurposeRegex(transactionPurpose);
        TransactionPurpose txPurposeFromLookup = transactionPurposeServiceLoader.load().get(transactionPurpose.appId());
        raiseIf(txPurposeFromLookup == TransactionPurpose.NULL, INVALID_TRANSACTION_PURPOSE, "Invalid Transaction Purpose");
        raiseIf(transactionPurpose.id() != Long.parseLong(paymentParamsLoader.load().getFreeTextTxPurposeId()) &&
                        !transactionPurpose.name().equals(txPurposeFromLookup.name()), INVALID_TRANSACTION_PURPOSE,
                "Invalid Transaction Purpose");
        transactionPurpose.validate(instructingParticipant, instructedParticipant);
    }

    private void validateTxPurposeRegex(TransactionPurpose transactionPurpose) {
        Pattern pattern = Pattern.compile(paymentParamsLoader.load().getTransactionPurposeRegex());
        String txPurposeCodeName = transactionPurpose.code() + "/" + transactionPurpose.name();
        raiseIf(!pattern.matcher(txPurposeCodeName).matches(), INVALID_TRANSACTION_PURPOSE,
                "Invalid Transaction Purpose [%s]", txPurposeCodeName);
    }

    TransactionDTO dto() {
        TransactionDTO result = new TransactionDTO();
        result.setId(appId());
        result.setCreditorCustomer(creditorCustomer);
        result.setDebtorCustomer(debtorCustomer);
        result.setMessageType(messageType);
        result.setDebtorParticipant(debtorParticipant);
        result.setDebtorBranch(debtorBranch);
        result.setCreditorParticipant(creditorParticipant);
        result.setCreditorBranch(creditorBranch);
        result.setCollectionDate(collectionDate);
        result.setPaymentSequence(paymentSequence);
        result.setAmount(getAmount());
        result.setCurrency(currency);
        result.setState(getState());
        result.setReason(getReason());
        result.setAdditionalInfo(getAdditionalInfo());
        result.setHashCodeInfo(hashCodeInfo);
        result.setDuplicate(isDuplicate);
        result.setMandateId(mandateId);
        result.setTransactionPurpose(transactionPurpose);
        result.setInstructionId(instructionId);
        result.setEndToEndId(endToEndId());
        result.setCollectionDate(collectionDate);
        result.setRemittance(remittance);
        result.setInstructedParticipant(instructedParticipant);
        result.setInstructingParticipant(instructingParticipant);
        result.setSourceTransactionId(outTransactionId);
        result.setRtgsPulled(isRtgsPulled);
        batch.fillBatchInfo(result);
        return result;
    }

    private boolean duplicateIn(List<Transaction> list) {
        return stream(list).anyMatch(that -> !this.equals(that) && this.hashCodeInfo == that.hashCodeInfo);
    }

    private boolean duplicateInDb() {
        return serviceLoader.load().checkDuplicate(appId(), hashCodeInfo);
    }

    @Override
    public String endToEndId() {
        return endToEndId;
    }

    @Override
    public Participant instructedParticipant() {
        return instructedParticipant;
    }

    @Override
    public Participant instructingParticipant() {
        return instructingParticipant;
    }

    @Override
    public Currency currency() {
        return currency;
    }

    @Override
    public Customer debtorCustomer() {
        return debtorCustomer;
    }

    @Override
    public Customer creditorCustomer() {
        return creditorCustomer;
    }

    @Override
    public MessageType messageType() {
        return messageType;
    }

    @Override
    public Participant debtorParticipant() {
        return debtorParticipant;
    }

    @Override
    public Branch debtorBranch() {
        return debtorBranch;
    }

    @Override
    public Account debtorAccount(){
        return debtorAccount;
    }

    @Override
    public Participant creditorParticipant() {
        return creditorParticipant;
    }

    @Override
    public Branch creditorBranch() {
        return creditorBranch;
    }

    @Override
    public Account creditorAccount(){
        return creditorAccount;
    }

    @Override
    public Date collectionDate() {
        return collectionDate;
    }

    @Override
    public long paymentSequence() {
        return paymentSequence;
    }

    @Override
    public BigDecimal amount() {
        return amount;
    }

    @Override
    public Reason reason() {
        return reason;
    }

    @Override
    public String additionalInfo() {
        return additionalInfo;
    }

    @Override
    public String mandateId() {
        return mandateId;
    }

    @Override
    public TransactionPurpose transactionPurpose() {
        return transactionPurpose;
    }

    @Override
    public String instructionId() {
        return instructionId;
    }

    @Override
    public String remittance() {
        return remittance;
    }

    @Override
    public Priority priority() {
        return priority;
    }

    @Override
    public String getAdditionalInfo() {
        return additionalInfo;
    }

    @Override
    public BigDecimal getAmount() {
        return amount;
    }

    @Override
    public Reason getReason() {
        return reason;
    }

    @Override
    public MessageType getMessageType() {
        return messageType;
    }

    @Override
    public String getState() {
        return state;
    }

    @Override
    public void validateReturnReversal(BigDecimal relatedAmount, Currency relatedCurrency) {
        raiseIf(ZERO.equals(relatedAmount), INVALID_AMOUNT);
        raiseIf(!currency.equals(relatedCurrency), CURRENCY_MISMATCH, currency, this, relatedCurrency);
        raiseIf(amount.compareTo(relatedAmount) < 0, AMOUNT_MISMATCH, amount.toString(), this, relatedAmount.toString());
        raiseIf(relatedAmount.compareTo(amount) != 0 && isCbkRelated(), RELATED_AMOUNT_MISMATCH, relatedAmount.toString(), this, amount.toString());
    }

    private boolean isCbkRelated() {
        String centralBankCode = paymentParamsLoader.load().getCentralBankCode();
        return debtorParticipant.code().equals(centralBankCode) || creditorParticipant.code().equals(centralBankCode);
    }

    @Override
    public CapExceededAction retrieveAction() {
        return capExceededActionServiceAppServiceLoader.load().get(creditorParticipant, debtorParticipant);
    }

    public Participant getInstructingParticipant() {
        return instructingParticipant;
    }

    public Batch getBatch() {
        return batch;
    }

    public String getOutTransactionId() {
        return outTransactionId;
    }
}