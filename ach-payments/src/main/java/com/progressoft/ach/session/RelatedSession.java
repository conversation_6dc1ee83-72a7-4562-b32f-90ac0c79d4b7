package com.progressoft.ach.session;

import com.progressoft.ach.window.Window;

import java.util.Objects;

public class RelatedSession {
    public final static RelatedSession NULL = new RelatedSession(Session.NULL, Window.NULL);

    private final Session session;
    private final Window window;

    public RelatedSession(Session session, Window window) {
        this.session = session;
        this.window = window;
    }

    public Session getSession() {
        return session;
    }

    public Window getWindow() {
        return window;
    }

    @Override
    public boolean equals(Object that) {
        return that != null &&
                (this == that || this.getClass().equals(that.getClass()) &&
                        this.hashCode() == that.hashCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(session, window);
    }
}
