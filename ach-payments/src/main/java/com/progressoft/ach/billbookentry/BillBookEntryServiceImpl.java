package com.progressoft.ach.billbookentry;

import com.progressoft.ach.PersistenceCache;
import com.progressoft.ach.entities.ATSACC_Account;
import com.progressoft.ach.entities.ATSBDI_Session;
import com.progressoft.ach.entities.ATSMSG_Type;
import com.progressoft.ach.payments.entities.ATSBillBookEntry;
import com.progressoft.communication.shared.AppId;
import com.progressoft.communication.shared.AppService;
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency;
import org.springframework.stereotype.Component;

import static com.progressoft.communication.shared.AppIdUtils.idOf;

@Component
public class BillBookEntryServiceImpl extends AppService implements BillBookEntryService {
	@Override
	public AppId persist(PersistenceCache persistenceCache, BillBookEntryDTO dto) {
		ATSBillBookEntry e = jfwEntity(persistenceCache, dto, 0);
		persist(e);
		return idOf(e, null);
	}

	private ATSBillBookEntry jfwEntity(PersistenceCache persistenceCache, BillBookEntryDTO dto, long id) {
		ATSBillBookEntry e = new ATSBillBookEntry();
		e.setId(id);
		e.setSession(persistenceCache.getOrElse(ATSBDI_Session.class, dto.getSession(), () ->getById(ATSBDI_Session.class, dto.getSession()))) ;
		e.setSettlementDate(dto.getSettlementDate());
		e.setAmount(dto.getAmount());
		e.setCurrency(lookup(JFWCurrency.class, dto.getCurrency()));
		e.setMsgType(lookup(ATSMSG_Type.class, dto.getMessageType()));
		e.setTxId(dto.getTransactionId());
		e.setDbtrAcnt(persistenceCache.getOrElse(ATSACC_Account.class, dto.getDebtorAccount(), () -> getById(ATSACC_Account.class, dto.getDebtorAccount())));
		e.setCrdtrAcnt(persistenceCache.getOrElse(ATSACC_Account.class, dto.getCreditorAccount(), () -> getById(ATSACC_Account.class, dto.getCreditorAccount())));
		return e;
	}
}