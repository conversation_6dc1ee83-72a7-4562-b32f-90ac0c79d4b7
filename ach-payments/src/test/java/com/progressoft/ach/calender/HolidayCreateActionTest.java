package com.progressoft.ach.calender;

import com.progressoft.jfw.model.bussinessobject.calendar.JFWHoliday;
import com.progressoft.jfw.workflow.WfContext;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;

import static org.mockito.Mockito.*;

public class HolidayCreateActionTest {
    private HolidayCreateAction notifier;

    @Before
    public void setUp() throws Exception {
        notifier = new HolidayCreateAction();
    }

    @Test
    public void setPropertyTest() {
        WfContext<JFWHoliday> wfContext = mock(WfContext.class);
        notifier.execute(wfContext);
        Mockito.verify(wfContext, times(1)).setProperty("HolidayAction", "NEW");
    }
}
