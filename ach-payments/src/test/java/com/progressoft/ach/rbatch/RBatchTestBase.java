package com.progressoft.ach.rbatch;

import com.progressoft.ach.FakeMessageRepository;
import com.progressoft.ach.PaymentUnitTest;
import com.progressoft.ach.System;
import com.progressoft.ach.batch.Batch;
import com.progressoft.ach.batch.BatchBuilder;
import com.progressoft.ach.fake.FakeMessageSender;
import com.progressoft.ach.reason.Reason;
import com.progressoft.ach.rtransaction.RTransaction;
import com.progressoft.ach.rtransaction.RTransactionBuilder;
import com.progressoft.ach.testcases.MessageTestCase;
import com.progressoft.ach.transaction.Transaction;
import com.progressoft.ach.transaction.TransactionBuilder;
import com.progressoft.communication.*;
import com.progressoft.communication.shared.AppId;
import org.junit.Before;

import static com.progressoft.ach.Format.MX;
import static com.progressoft.ach.MessageType.*;
import static com.progressoft.ach.State.ACCEPTED;
import static com.progressoft.ach.State.REJECTED;
import static com.progressoft.ach.System.PSYS;
import static com.progressoft.communication.Direction.INWARD;
import static com.progressoft.communication.ProcessingState.IN_PROCESS;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

class RBatchTestBase extends PaymentUnitTest {
    Batch acceptedNboBatch;
    Batch rejectedNboBatch;
    RBatch acceptedNboRBatch;
    Batch acceptedCboBatch;
    RBatch acceptedCboRBatch;
    Batch rejectedCboBatch;

    MessageRepository messageRepository;

    @Before
    public void testSetup() {
        itemDao.affectItems();
        itemDao.filterItems();
        acceptedNboBatch = buildBatch(nbo.appId()).state(ACCEPTED).build();
        acceptedCboBatch = buildBatchCbo(cbo.appId()).state(ACCEPTED).build();
        acceptedNboBatch.link(transaction(ACCEPTED, acceptedNboBatch));
        acceptedCboBatch.link(transaction(ACCEPTED, acceptedCboBatch));
        rejectedNboBatch = buildBatch(nbo.appId()).state(REJECTED).build();
        acceptedNboRBatch = buildRBatch(nbo.appId()).state(ACCEPTED).build();
        acceptedNboRBatch.link(rTransaction(ACCEPTED, acceptedNboRBatch));
        acceptedCboRBatch = buildRBatchCbo(cbo.appId()).state(ACCEPTED).build();
        acceptedCboRBatch.link(rTransaction(ACCEPTED, acceptedCboRBatch));
        rejectedCboBatch = buildBatchCbo(cbo.appId()).state(REJECTED).build();
        messageRepository = new FakeMessageRepository();
    }

    private BatchBuilder buildBatch(AppId id) {
        return new BatchBuilder().id(id)
                .messageType(CREDIT)
                .instructingParticipant(nbo)
                .instructedParticipant(aib)
                .source(integration)
                .session(session)
                .window(window)
                .creationDate(now)
                .categoryPurpose(salary)
                .communicatingParticipant(nbo);
    }

    private RBatchBuilder buildRBatch(AppId id) {
        return new RBatchBuilder().id(id)
                .messageType(CANCELLATION)
                .instructingParticipant(nbo)
                .instructedParticipant(aib)
                .source(integration)
                .session(session)
                .window(window)
                .creationDate(now)
                .communicatingParticipant(nbo);
    }

    private RBatchBuilder buildRBatchCbo(AppId id) {
        return new RBatchBuilder()
                .id(id)
                .messageType(CANCELLATION)
                .originalMessageType(DEBIT)
                .instructingParticipant(cbo)
                .instructedParticipant(hsbc)
                .source(integration)
                .session(session)
                .window(window)
                .creationDate(now)
                .communicatingParticipant(cbo);
    }

    private BatchBuilder buildBatchCbo(AppId id) {
        return new BatchBuilder()
                .id(id)
                .messageType(CREDIT)
                .instructingParticipant(cbo)
                .instructedParticipant(hsbc)
                .source(integration)
                .session(session)
                .window(window)
                .creationDate(now)
                .communicatingParticipant(cbo);
    }

    void checkBatchState(RBatch rBatch, String state, Reason reason, String additionalInfo) {
        assertTrue(rBatch instanceof RBatchImpl);
        RBatchImpl rBatchImpl = (RBatchImpl) rBatch;
        assertEquals(state, rBatchImpl.state);
        assertEquals(reason, rBatchImpl.reason);
        assertEquals(additionalInfo, rBatchImpl.additionalInfo);
    }

    Transaction transaction(String state, Batch originalBatch) {
        return new TransactionBuilder()
                .id(ID)
                .messageType(CREDIT)
                .state(state)
                .batch(originalBatch)
                .build();
    }

    RTransaction rTransaction(String state, RBatch originalRBatch) {
        return new RTransactionBuilder()
                .id(ID)
                .messageType(CANCELLATION)
                .state(state)
                .batch(originalRBatch)
                .build();
    }

    void clearChanges() {
        jfw.getBean(FakeMessageSender.class).sentMessages.clear();
        postAccountBalance.balanceChanges.clear();
        itemDao.clearChanges();
    }

    void act(MessageTestCase testcase) {
        Message message = Message.newBuilder()
                .setProcessingState(IN_PROCESS)
                .setId(MessageId.of(testcase.messageId()).value())
                .setType(MessageType.of(testcase.messageType().code()).value())
                .setContent(MessageContent.of(testcase.content()).value())
                .setDirection(INWARD)
                .setFormat(MessageFormat.of(MX).value())
                .setSource(Side.of(PSYS, testcase.participant().name()).value())
                .setDestination(Side.of(System.ACH, DEFAULT_TENANT).value())
                .build().value();
        messageRepository.persist(message);
        testcase.assertResult(jfw, itemDao);
    }
}