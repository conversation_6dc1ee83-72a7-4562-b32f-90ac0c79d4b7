package com.progressoft.ach.rbatch;

import com.progressoft.ach.MessageType;
import com.progressoft.ach.State;
import com.progressoft.ach.batch.Batch;
import com.progressoft.ach.batch.BatchBuilder;
import com.progressoft.ach.batch.BatchImpl;
import com.progressoft.ach.batch.BatchServiceImpl;
import com.progressoft.ach.entities.ATSMSG_Type;
import com.progressoft.ach.entities.ATSPRT_Participant;
import com.progressoft.ach.payments.entities.*;
import com.progressoft.ach.reason.Reason;
import com.progressoft.ach.reason.ReasonImpl;
import com.progressoft.ach.rtransaction.*;
import com.progressoft.ach.transaction.TransactionBuilder;
import com.progressoft.communication.shared.AppId;
import com.progressoft.communication.shared.AppServiceLoader;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Arrays;

import static com.progressoft.ach.MessageType.*;
import static com.progressoft.ach.State.*;
import static com.progressoft.ach.reason.Reason.*;
import static com.progressoft.ach.transformers.MessageVersions.VER_201001;
import static com.progressoft.communication.shared.AppIdUtils.idOf;
import static com.progressoft.communication.shared.AppServiceLocator.locate;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

public class RBatchPaymentReplyTest extends RBatchTestBase {

    private RTransactionServiceImpl service;
    private BatchServiceImpl batchService;

    @Before
    public void setup() {
        service = locate(RTransactionServiceImpl.class);
        batchService = locate(BatchServiceImpl.class);
        itemDao.add(new ATSTransaction());
    }

    @Test
    public void process_fails() {
        AppId batchId = batchService.persist(((BatchImpl) rejectedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getPart2());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RBatch rBatch = rBatchHsbc(rejectedCboBatch, ACCEPTED);
        rBatch.process();
        checkBatchState(rBatch, REJECTED, Reason.get(PAYMENT_STATUS_REPORT_WITH_INVALID_STATUS), "Payment Status Report With Invalid Status");
    }

    @Test
    public void process_passes() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getPart2());
        itemDao.add(getPart3());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        itemDao.add(transaction2(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalTransaction(ID)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();

        RBatch rBatch = rBatchHsbc(acceptedCboBatch, IN_PROCESS, rTransaction);
        rBatch.process();
        checkBatchState(rBatch, ACCEPTED, Reason.NULL, EMPTY);
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatch.appId()).size());
        RTransactionImpl rTransactionImpl = (RTransactionImpl) transactionServiceLoader.load().list(rBatch.appId()).get(0);
        assertEquals(ACCEPTED, rTransactionImpl.getState());
    }

    @Test
    public void givenDuplicateRBatchId_whenItAlreadyExistInHistoryRBatchTable_thenStatusIsRejected() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getPart2());
        itemDao.add(getPart3());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        itemDao.add(transaction2(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();

        RBatch rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        HistoryRBatchIds historyRBatchIds = new HistoryRBatchIds();
        historyRBatchIds.setRBatchId(rBatch.appId().code());
        itemDao.add(historyRBatchIds);
        rBatch.process();
        checkBatchState(rBatch, REJECTED, Reason.get(DUPLICATE_BATCH_ID), "Batch ID is duplicate");
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatch.appId()).size());
        RTransactionImpl rTransactionImpl = (RTransactionImpl) transactionServiceLoader.load().list(rBatch.appId()).get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
    }

    @Test
    public void givenDuplicateRTransactionId_whenItAlreadyExistInHistoryRTransactionTable_thenStatusIsRejected() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getPart2());
        itemDao.add(getPart3());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        itemDao.add(transaction2(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(idOf("BBMEOMRX001"))
                .originalTransaction(ID)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();

        RBatchImpl rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        HistoryRTransactionIds historyRBatchIds = new HistoryRTransactionIds();
        historyRBatchIds.setRTransactionId("BBMEOMRX001");
        itemDao.add(historyRBatchIds);
        rBatch.process();
        assertEquals(REJECTED, rBatch.state);
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatch.appId()).size());
        RTransactionImpl rTransactionImpl = (RTransactionImpl) transactionServiceLoader.load().list(rBatch.appId()).get(0);
        assertEquals("Rejected", rTransactionImpl.getState());
        assertEquals(Reason.get(DUPLICATE_TRANSACTION_ID), rTransactionImpl.getReason());
        assertEquals("RTransaction already exists", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void process_passesRejectedTransactions() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getPart2());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RBatchImpl rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED);
        rBatch.originalState = REJECTED;
        rBatch.originalReason = Reason.get(INTERNAL_SYSTEM_ERROR);
        rBatch.originalAdditionalInfo = "originalAdditionalInfo";
        rBatch.process();
        checkBatchState(rBatch, ACCEPTED, Reason.NULL, EMPTY);
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatch.appId()).size());
        assertTrue(itemDao.getPersistedItems().get(3) instanceof ATSRTransaction);
        ATSRTransaction rTransaction = (ATSRTransaction) itemDao.getPersistedItems().get(3);
        assertEquals(ACCEPTED, rTransaction.getState().getCode());
        assertEquals("originalAdditionalInfo", rTransaction.getOrgAdditionalInfo());
        assertEquals(INTERNAL_SYSTEM_ERROR.id(), rTransaction.getOrgReason().getId());
        assertTrue(itemDao.getMergedItems().get(0) instanceof ATSTransaction);
        ATSTransaction transaction = (ATSTransaction) itemDao.getMergedItems().get(0);
        assertEquals(REJECTED, transaction.getState().getCode());
        assertEquals("originalAdditionalInfo", transaction.getAdditionalInfo());
        assertEquals(INTERNAL_SYSTEM_ERROR.id(), transaction.getReason().getId());
    }

    @Test
    public void checkSRMessageNumberOfTransactionMustBeEqualActualNumberOfTransactions() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getPart1());
        itemDao.add(getBatch(batchId));
        RBatchImpl rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction(ACCEPTED, acceptedCboRBatch));
        rBatch.originalState = PARTIALLY_ACCEPTED;
        rBatch.process();
        checkBatchState(rBatch, REJECTED, Reason.get(BATCH_TX_COUNT_MISMATCH_BETWEEN_SPECIFIED_AND_EXISTING), "Actual number of transactions per batch does not match specified count");
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatch.appId()).size());
        RTransactionImpl rTransactionImpl = (RTransactionImpl) transactionServiceLoader.load().list(rBatch.appId()).get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
    }

    @Test
    public void checkBatchReasonIfNullReasonAndStateRejected() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getOUTBatch1(batchId));
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED);
        rBatchImpl.originalState = REJECTED;
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(BATCH_REJECTED_WITH_NO_REASONS_AND_NO_TRANSACTIONS), "Batch Rejected With No Reasons");
    }

    @Test
    public void checkBatchReasonIfNotNullReasonAndOriginalStateAccepted() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getOUTBatch1(batchId));
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED);
        rBatchImpl.originalReason = Reason.get(UNSUPPORTED_REASON_CODE);
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(BATCH_LEVEL_ACCEPTANCE_WITH_TRANSACTIONS_OR_REASONS), "Batch Level Acceptance With Reasons");
    }

    @Test
    public void checkBatchWithOriginalStatusInProcess() {
        Batch batch = new BatchBuilder()
                .id(cbo.appId())
                .messageType(CREDIT)
                .instructingParticipant(cbo)
                .instructedParticipant(hsbc)
                .source(integration)
                .state(IN_PROCESS)
                .session(session)
                .window(window)
                .creationDate(now)
                .communicatingParticipant(cbo)
                .build();
        AppId batchId = batchService.persist(((BatchImpl) batch).dto());
        itemDao.add(getOUTBatch1(batchId));
        RBatchImpl rBatchImpl = rBatchHsbc(batch, ACCEPTED);
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(PAYMENT_STATUS_REPORT_WITH_INVALID_STATUS), "Payment Status Report With Invalid Status");

    }

    @Test
    public void process_rejectedTransaction_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(REJECTED)
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();

        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalReason = Reason.get(UNSUPPORTED_REASON_CODE);
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(BATCH_LEVEL_ACCEPTANCE_WITH_TRANSACTIONS_OR_REASONS), "Batch Level Acceptance With Reasons");
        AppServiceLoader<RTransactionService> transactionServiceLoader = new AppServiceLoader<>(RTransactionService.class);
        assertEquals(1, transactionServiceLoader.load().list(rBatchImpl.appId()).size());
        RTransactionImpl rTransactionImpl = (RTransactionImpl) transactionServiceLoader.load().list(rBatchImpl.appId()).get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
    }

    @Test
    public void process_rejectedTransactionWithNoOriginalTransaction_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(ORIGINAL_TRANSACTION_NOT_FOUND, rTransactionImpl.getReason().appId());
        assertEquals("Original transactions does not exist", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void process_rejectedTransactionWithInvalidStatus_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .originalTransaction(transaction(REJECTED, acceptedCboBatch))
                .originalState(REJECTED)
                .state(REJECTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(ORIGINAL_TRANSACTION_IN_INVALID_STATUS, rTransactionImpl.getReason().appId());
        assertEquals("Original Transaction In Invalid Status", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void transactionWithInvalidOriginalStatus_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalTransaction(transaction(PARTIALLY_ACCEPTED, acceptedCboBatch))
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(ORIGINAL_TRANSACTION_IN_INVALID_STATUS, rTransactionImpl.getReason().appId());
        assertEquals("Original Transaction In Invalid Status", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void transactionWithSystemReasonInOriginalReason_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        AppId reasonId = idOf(2000, "ID");
        env.initReason(reasonId, true, false);
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(ID)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(UNSUPPORTED_REASON_CODE, rTransactionImpl.getReason().appId());
        assertEquals("Reason [2000,ID] is only used by system", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void originalTransactionWithUnsupportedReasonCode_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        AppId reasonId = idOf(2000, "ID");
        env.initReason(reasonId, false, false);
        ReasonImpl reason = (ReasonImpl) Reason.get(ID);
        reason.getLinks().remove(0);

        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(PENDING)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(reason)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, IN_PROCESS, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(UNSUPPORTED_REASON_CODE, rTransactionImpl.getReason().appId());
        assertEquals("Unsupported Reason Code", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void originalTransactionWithRejectedStatusAndEmptyReason_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(REJECTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(TRANSACTION_REJECTED_WITH_NO_REASONS_AND_NO_BATCH_REASONS, rTransactionImpl.getReason().appId());
        assertEquals("Transaction Rejected With No Reasons And No Batch Reasons", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void originalTransactionWithAcceptedStatusAndReason_fails() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransactionImpl = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransactionImpl.getState());
        assertEquals(TRANSACTION_ACCEPTED_WITH_REASONS, rTransactionImpl.getReason().appId());
        assertEquals("Transaction Accepted With Reasons", rTransactionImpl.getAdditionalInfo());
    }

    @Test
    public void checkInvalidMessageType() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RBatchImpl rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED);
        rBatch.originalMessageType = CANCELLATION_REPLY;
        rBatch.process();
        checkBatchState(rBatch, REJECTED, Reason.get(MESSAGE_SENT_ON_INVALID_MESSAGE_TYPE), "Message Sent On Invalid Message Type");
    }

    @Test
    public void checkInvalidOriginalMessageType() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RBatchImpl rBatch = rBatchHsbc(acceptedCboBatch, ACCEPTED);
        rBatch.originalMessageType = DEBIT;
        rBatch.process();
        checkBatchState(rBatch, REJECTED, Reason.get(INVALID_ORIGINAL_MESSAGE_TYPE), "Invalid original message type");
    }

    @Test
    public void checkBatchWithDuplicateTransactionsIDs() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getOUTTransaction2(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        itemDao.add(transaction2(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(ACCEPTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(RBATCH_CONTAINS_MORE_THAN_ONE_RTX_REFERRING_TO_THE_SAME_PAYMENT_TX), "R Batch Contains Duplicate Tx Reference");
    }

    @Test
    public void checkTransactionAcceptedWithReasons() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(IN_PROCESS)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl rTransaction1 = (RTransactionImpl) rBatchImpl.getTransactions().get(0);
        assertEquals(REJECTED, rTransaction1.getState());
        assertEquals(Reason.get(TRANSACTION_ACCEPTED_WITH_REASONS), rTransaction1.getReason());
        assertEquals("Transaction Accepted With Reasons", rTransaction1.getAdditionalInfo());
        checkBatchState(rBatchImpl, REJECTED, Reason.NULL, "");
    }

    @Test
    public void checkBatchWithDuplicateTransactions() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        itemDao.add(transaction2(batchId));
        RTransaction rTransaction1 = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(ACCEPTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .build();
        RTransaction rTransaction2 = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(new TransactionBuilder()
                        .id(ID2)
                        .messageType(CREDIT)
                        .state(ACCEPTED)
                        .batch(acceptedCboBatch)
                        .build())
                .originalState(ACCEPTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction1, rTransaction2);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        checkBatchState(rBatchImpl, REJECTED, Reason.get(BATCH_HAS_DUPLICATE_TRANSACTION), "Batch contains duplicate transactions");
    }

    @Test
    public void checkBatchWithOnUsTransactions() {
        AppId batchId = batchService.persist(((BatchImpl) acceptedCboBatch).dto());
        itemDao.add(getPart1());
        itemDao.add(getOUTBatch1(batchId));
        itemDao.add(getOUTTransaction1(getOUTBatch1(batchId)));
        itemDao.add(getBatch(batchId));
        itemDao.add(transaction1(batchId));
        RTransaction rTransaction = new RTransactionBuilder()
                .id(HSBC)
                .messageType(REPLY)
                .originalMessageType(CREDIT)
                .state(ACCEPTED)
                .originalTransaction(transaction(ACCEPTED, acceptedCboBatch))
                .originalState(ACCEPTED)
                .originalAdditionalInfo(RANDOM_STRING)
                .originalReason(INTERNAL_SYSTEM_ERROR)
                .instructedParticipant(cbo)
                .instructingParticipant(cbo)
                .build();
        RBatchImpl rBatchImpl = rBatchHsbc(acceptedCboBatch, ACCEPTED, rTransaction);
        rBatchImpl.originalState = PARTIALLY_ACCEPTED;
        rBatchImpl.process();
        RTransactionImpl transactionImpl = (RTransactionImpl) rTransaction;
        assertEquals(REJECTED, transactionImpl.getState());
        assertEquals(Reason.get(ONUS_TRANSACTIONS_NOT_SUPPORTED), transactionImpl.getReason());
        assertEquals("On Us transaction", transactionImpl.getAdditionalInfo());
    }

    private RBatchImpl rBatchHsbc(Batch originalBatch, String state, RTransaction... rTransaction) {
        RBatchImpl rBatch = new PaymentRBatchImpl(hsbc.appId(), MessageType.CREDIT);
        rBatch.originalReason = Reason.NULL;
        rBatch.communicatingParticipant = hsbc;
        rBatch.instructingParticipant = hsbc;
        rBatch.originalState = IN_PROCESS;
        rBatch.reason = Reason.NULL;
        rBatch.source = integration;
        rBatch.instructedParticipant = cbo;
        rBatch.originalBatch = originalBatch;
        rBatch.state = state;
        rBatch.outBatchId = originalBatch.appId();
        RTransaction.link(Arrays.asList(rTransaction), rBatch);
        return rBatch;
    }

    private ATSTransaction transaction1(AppId appId) {
        ATSTransaction rTx = new ATSTransaction();
        rTx.setId(ID.id());
        rTx.setTxId(ID.code());
        rTx.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        rTx.setState(service.lookup(ATSState.class, ACCEPTED));
        rTx.setInstdParti(getPart1());
        rTx.setBatch(getBatch(appId));
        rTx.setAmount(BigDecimal.ONE);
        return rTx;
    }

    private ATSTransaction transaction2(AppId appId) {
        ATSTransaction rTx = new ATSTransaction();
        rTx.setId(ID2.id());
        rTx.setTxId(ID2.code());
        rTx.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        rTx.setState(service.lookup(ATSState.class, ACCEPTED));
        rTx.setInstdParti(getPart1());
        rTx.setBatch(getBatch(appId));
        rTx.setAmount(BigDecimal.ONE);
        return rTx;
    }

    private ATSBatch getBatch(AppId appId) {
        ATSBatch batch = new ATSBatch();
        batch.setId(appId.id());
        batch.setBatchId(appId.code());
        batch.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        batch.setState(service.lookup(ATSState.class, ACCEPTED));
        batch.setAmount(BigDecimal.ONE);
        return batch;
    }

    private ATSPRT_Participant getPart1() {
        ATSPRT_Participant participant = new ATSPRT_Participant();
        participant.setId(HSBC.id());
        participant.setCode(HSBC.code());
        participant.setPacsMessageVersion(VER_201001.name());
        return participant;
    }

    private ATSPRT_Participant getPart2() {
        ATSPRT_Participant participant = new ATSPRT_Participant();
        participant.setId(cbo.id());
        participant.setCode(cbo.code());
        participant.setPacsMessageVersion(VER_201001.name());
        return participant;
    }

    private ATSPRT_Participant getPart3() {
        ATSPRT_Participant participant = new ATSPRT_Participant();
        participant.setId(nbo.id());
        participant.setCode(nbo.code());
        participant.setPacsMessageVersion(VER_201001.name());
        return participant;
    }

    private ATSOUTBatch getOUTBatch1(AppId batchId) {
        ATSOUTBatch outBatch = new ATSOUTBatch();
        outBatch.setId(cbo.id());
        outBatch.setBatchId(cbo.code());
        outBatch.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        outBatch.setComParti(getPart2());
        outBatch.setInstgParti(getPart2());
        outBatch.setInstdParti(getPart1());
        outBatch.setSourceBatchId(batchId.code());
        outBatch.setAmount(BigDecimal.ONE);
        return outBatch;
    }


    private ATSOUTTransaction getOUTTransaction1(ATSOUTBatch outBatch) {
        ATSOUTTransaction outTransaction = new ATSOUTTransaction();
        outTransaction.setId(ID.id());
        outTransaction.setTxId(ID.code());
        outTransaction.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        outTransaction.setBatch(outBatch);
        outTransaction.setInstgParti(getPart1());
        outTransaction.setInstdParti(getPart3());
        outTransaction.setSourceTransactionId(ID.code());
        outBatch.setAmount(BigDecimal.ONE);
        return outTransaction;
    }

    private ATSOUTTransaction getOUTTransaction2(ATSOUTBatch outBatch) {
        ATSOUTTransaction outTransaction = new ATSOUTTransaction();
        outTransaction.setId(ID2.id());
        outTransaction.setTxId(ID2.code());
        outTransaction.setMsgType(service.lookup(ATSMSG_Type.class, CREDIT.code()));
        outTransaction.setBatch(outBatch);
        outTransaction.setInstgParti(getPart1());
        outTransaction.setInstdParti(getPart3());
        outTransaction.setSourceTransactionId(ID2.code());
        outBatch.setAmount(BigDecimal.ONE);
        return outTransaction;
    }
}
