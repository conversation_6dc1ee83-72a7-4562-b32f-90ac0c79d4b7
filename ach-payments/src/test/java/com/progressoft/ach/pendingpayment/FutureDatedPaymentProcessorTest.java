package com.progressoft.ach.pendingpayment;


import com.progressoft.ach.PaymentParams;
import com.progressoft.ach.State;
import com.progressoft.ach.payments.entities.ATSPendingPayment;
import com.progressoft.jfw.test.FakeItemDao;
import com.progressoft.jfw.test.FakeJfwImpl;
import org.junit.Before;
import org.junit.Test;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static com.progressoft.ach.pendingpayment.PendingPaymentStatus.PENDING;
import static com.progressoft.ach.pendingpayment.PendingPaymentType.FUTURE_DATED;
import static com.progressoft.ach.reason.Reason.INVALID_PRESENTMENT_CYCLE;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class FutureDatedPaymentProcessorTest {
    private static final String MESSAGE_ID = "123";
    private FakeItemDao itemDao;
    private StatusReportSender statusReportSender;
    private FutureDatedPaymentProcessor futureDatedPaymentProcessor;

    @Before
    public void setUp() throws Exception {
        itemDao = new FakeItemDao();
        itemDao.filterItems();
        PaymentParams params = mock(PaymentParams.class);
        statusReportSender = mock(StatusReportSender.class);
        futureDatedPaymentProcessor = new FutureDatedPaymentProcessor(itemDao,new FakeJfwImpl(), params, statusReportSender);
    }

    @Test
    public void givenPendingPaymentWithFutureDateTypeAndPendingStatusButWithOldDate_whenProcess_thenShouldRejectPendingPaymentAndSendStatusReportWithRejection() throws Exception {
        Calendar dueDate = Calendar.getInstance();
        dueDate.add(Calendar.DAY_OF_YEAR, -2);
        addPendingPayment(dueDate.getTime());

        futureDatedPaymentProcessor.doProcess(null);

        verify(statusReportSender, times(1)).send(eq(MESSAGE_ID), eq(State.REJECTED),
                eq(INVALID_PRESENTMENT_CYCLE), eq("Future dated payment are due"));
        List<Object> mergedItems = itemDao.getMergedItems();
        assertFalse(mergedItems.isEmpty());
        ATSPendingPayment mergedPayment = (ATSPendingPayment) mergedItems.get(0);
        assertEquals(PendingPaymentStatus.REJECTED.getStatus(), mergedPayment.getStatus());
    }

    @Test
    public void givenPendingPaymentWithFutureDateTypeAndPendingStatusAndTodayDate_whenProcess_thenShouldNotRejectPendingPaymentAndShouldNotSendStatusReportWithRejection() throws Exception {
        addPendingPayment(new Date());

        futureDatedPaymentProcessor.doProcess(null);

        verify(statusReportSender, times(0)).send(eq(MESSAGE_ID), eq(State.REJECTED),
                eq(INVALID_PRESENTMENT_CYCLE), eq("Future dated payment are due"));
        List<Object> mergedItems = itemDao.getMergedItems();
        assertTrue(mergedItems.isEmpty());
    }

    @Test
    public void givenPendingPaymentWithFutureDateTypeAndPendingStatusAndFutureDate_whenProcess_thenShouldNotRejectPendingPaymentAndShouldNotSendStatusReportWithRejection() throws Exception {
        Calendar futureDate = Calendar.getInstance();
        futureDate.add(Calendar.DAY_OF_YEAR, 2);
        addPendingPayment(futureDate.getTime());

        futureDatedPaymentProcessor.doProcess(null);

        verify(statusReportSender, times(0)).send(eq(MESSAGE_ID), eq(State.REJECTED),
                eq(INVALID_PRESENTMENT_CYCLE), eq("Future dated payment are due"));
        List<Object> mergedItems = itemDao.getMergedItems();
        assertTrue(mergedItems.isEmpty());
    }

    private void addPendingPayment(Date settlementDate) {
        ATSPendingPayment payment = new ATSPendingPayment();
        payment.setId(1L);
        payment.setMessageId(MESSAGE_ID);
        payment.setStatus(PENDING.getStatus());
        payment.setType(FUTURE_DATED.getValue());
        payment.setSettlementDate(settlementDate);
        itemDao.add(payment);
    }
}