package com.progressoft.ach.balancechange;

import com.progressoft.ach.PaymentUnitTest;
import com.progressoft.ach.entities.ATSACC_Account;
import com.progressoft.ach.entities.ATSACC_Balance;
import com.progressoft.ach.entities.ATSBDI_Session;
import com.progressoft.ach.entities.ATSPRT_Participant;
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.Date;

import static org.junit.Assert.assertEquals;

public class BalanceServiceImplTest extends PaymentUnitTest {

    private BalanceService service;
    private ATSBDI_Session session;
    private ATSACC_Account account;
    private ATSACC_Balance balance;

    @Before
    public void setup() {
        service = env.initService(new BalanceServiceImpl());
        initializeTest();
    }

    private void initializeTest() {
        ATSPRT_Participant participant = participant();
        account = account(participant);
        session = session();
        itemDao.clear();
        itemDao.add(participant);
        itemDao.add(account);
        itemDao.add(session);
        balance = balance(new BigDecimal(-98000), session, account);
        itemDao.add(balance);
    }

    @Test
    public void getBalance_Works() {
        assertEquals(balance, service.getBalance(account.getId(), session.getId()));
    }

    private ATSACC_Balance balance(BigDecimal balanceAmount, ATSBDI_Session session, ATSACC_Account account) {
        ATSACC_Balance balance = new ATSACC_Balance();
        balance.setRefSession(session);
        balance.setSettlementDt(new Date());
        balance.setRefAccount(account);
        balance.setDebitCap(new BigDecimal(-100000));
        balance.setCreditCap(new BigDecimal(100000));
        balance.setBalance(balanceAmount);
        balance.setCurrency(getCurrency());
        return balance;
    }

    private JFWCurrency getCurrency() {
        JFWCurrency currency = new JFWCurrency();
        currency.setStringISOCode("KWD");
        return currency;
    }

    private ATSACC_Account account(ATSPRT_Participant participant) {
        ATSACC_Account account = new ATSACC_Account();
        account.setAccNumber("12345");
        account.setDebitWatermark(95);
        account.setParticipant(participant);
        return account;
    }

    private ATSPRT_Participant participant() {
        ATSPRT_Participant participant = new ATSPRT_Participant();
        participant.setId(55L);
        participant.setName("Bank1");
        participant.setCode("Bank1XXXX");
        return participant;
    }

    private ATSBDI_Session session() {
        ATSBDI_Session session = new ATSBDI_Session();
        session.setSessionSeq("1");
        session.setCurrPeriod("Exchange");
        return session;
    }

}
