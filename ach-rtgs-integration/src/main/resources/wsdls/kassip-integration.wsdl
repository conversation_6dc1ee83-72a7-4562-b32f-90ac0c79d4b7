<?xml version="1.0" encoding="UTF-8"?>
<definitions xmlns:tns="http://integration.gwclient.smallsystems.cma.se/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" name="GWClientSAOWService"
             targetNamespace="http://integration.gwclient.smallsystems.cma.se/"
             xmlns="http://schemas.xmlsoap.org/wsdl/">
    <types>
        <xs:schema targetNamespace="http://integration.gwclient.smallsystems.cma.se/" version="1.0"
                   xmlns:tns="http://integration.gwclient.smallsystems.cma.se/"
                   xmlns:xs="http://www.w3.org/2001/XMLSchema">

            <xs:element name="logon" type="tns:logon_t"/>
            <xs:element name="logonResponse" type="tns:logonResponse_t"/>
            <xs:element name="logout" type="tns:session_t"/>
            <xs:element name="logoutResponse" type="tns:empty_t"/>
            <xs:element name="send" type="tns:send_t"/>
            <xs:element name="sendResponse" type="tns:sendResponse"/>
            <xs:element name="getUpdates" type="tns:getUpdates_t"/>
            <xs:element name="getUpdatesResponse" type="tns:ParamsMtMsgArray"/>
            <xs:element name="sendACKNAK" type="tns:sendACKNAK_t"/>
            <xs:element name="sendACKNAKResponse" type="tns:empty_t"/>
            <xs:element name="fault" type="tns:fault_t"/>

            <xs:complexType name="logon_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="username" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="password" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="signature" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="clientWSUrl" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="logonResponse_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="session_id" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="data" type="tns:result_t"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="getUpdates_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="session_id" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="unbounded" name="data" type="tns:result_t"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="session_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="session_id" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="send_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="session_id" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="message" type="tns:ParamsMtMsg"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="sendACKNAK_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="session_id" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="data" type="tns:result_t"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="ParamsMtMsg">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="block4" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgCopySrvId" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgCopySrvInfo" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgDelNotifRq" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgFinValidation" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgFormat" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgId" type="xs:long"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgMacResult" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgNetInputTime" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgNetMir" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgNetOutputDate" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgPacResult" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgPde" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgPdm" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgPriority" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="msgReceiver" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="msgSender" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgSequence" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgSession" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgSubFormat" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="msgType" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgSrvTpId" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgEnd2EndRef" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgUserPriority" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="msgUserReference" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="format" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="refMsgUserReference" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType final="#all" name="ParamsMtMsgArray">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0" name="item" nillable="true" type="tns:ParamsMtMsg"/>
                </xs:sequence>
            </xs:complexType>
            <xs:simpleType name="ack_nak_type">
                <xs:restriction base="xs:string">
                    <xs:enumeration value="ACK"/>
                    <xs:enumeration value="NAK"/>
                </xs:restriction>
            </xs:simpleType>
            <xs:complexType name="sendResponse">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="data" type="tns:result_t"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="result_t">
                <xs:sequence>
                    <xs:element minOccurs="1" maxOccurs="1" name="type" type="tns:ack_nak_type"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="datetime" type="xs:string"/>
                    <xs:element minOccurs="1" maxOccurs="1" name="mir" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="ref" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="signature" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="code" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="description" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="info" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
            <xs:complexType name="empty_t"/>
            <xs:complexType name="fault_t">
                <xs:sequence>
                    <xs:element minOccurs="0" maxOccurs="1" name="code" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="description" type="xs:string"/>
                    <xs:element minOccurs="0" maxOccurs="1" name="info" type="xs:string"/>
                </xs:sequence>
            </xs:complexType>
        </xs:schema>
    </types>

    <message name="GWClientMU_logon">
        <part name="data" element="tns:logon"/>
    </message>
    <message name="GWClientMU_logonResponse">
        <part name="data" element="tns:logonResponse"/>
    </message>
    <message name="GWClientMU_logout">
        <part name="data" element="tns:logout"/>
    </message>
    <message name="GWClientMU_logoutResponse">
        <part name="data" element="tns:logoutResponse"/>
    </message>
    <message name="GWClientMU_send">
        <part name="data" element="tns:send"/>
    </message>
    <message name="GWClientMU_sendResponse">
        <part name="data" element="tns:sendResponse"/>
    </message>
    <message name="GWClientMU_getUpdates">
        <part name="data" element="tns:getUpdates"/>
    </message>
    <message name="GWClientMU_getUpdatesResponse">
        <part name="data" element="tns:getUpdatesResponse"/>
    </message>
    <message name="GWClientMU_sendACKNAK">
        <part name="data" element="tns:sendACKNAK"/>
    </message>
    <message name="GWClientMU_sendACKNAKResponse">
        <part name="data" element="tns:sendACKNAKResponse"/>
    </message>
    <message name="gwClientFault">
        <part name="data" element="tns:fault"/>
    </message>

    <portType name="GWClientMU">
        <operation name="logon">
            <input message="tns:GWClientMU_logon"/>
            <output message="tns:GWClientMU_logonResponse"/>
            <fault name="gwFault" message="tns:gwClientFault"/>
        </operation>
        <operation name="logout">
            <input message="tns:GWClientMU_logout"/>
            <output message="tns:GWClientMU_logoutResponse"/>
            <fault name="gwFault" message="tns:gwClientFault"/>
        </operation>
        <operation name="send">
            <input message="tns:GWClientMU_send"/>
            <output message="tns:GWClientMU_sendResponse"/>
            <fault name="gwFault" message="tns:gwClientFault"/>
        </operation>
        <operation name="getUpdates">
            <input message="tns:GWClientMU_getUpdates"/>
            <output message="tns:GWClientMU_getUpdatesResponse"/>
            <fault name="gwFault" message="tns:gwClientFault"/>
        </operation>
        <operation name="sendACKNAK">
            <input message="tns:GWClientMU_sendACKNAK"/>
            <output message="tns:GWClientMU_sendACKNAKResponse"/>
            <fault name="gwFault" message="tns:gwClientFault"/>
        </operation>
    </portType>

    <binding name="GWClientMUBinding" type="tns:GWClientMU">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
        <operation name="logon">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="gwFault">
                <soap:fault name="gwFault" use="literal"/>
            </fault>
        </operation>
        <operation name="logout">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="gwFault">
                <soap:fault name="gwFault" use="literal"/>
            </fault>
        </operation>
        <operation name="send">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="gwFault">
                <soap:fault name="gwFault" use="literal"/>
            </fault>
        </operation>
        <operation name="sendACKNAK">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="gwFault">
                <soap:fault name="gwFault" use="literal"/>
            </fault>
        </operation>
        <operation name="getUpdates">
            <soap:operation soapAction=""/>
            <input>
                <soap:body use="literal"/>
            </input>
            <output>
                <soap:body use="literal"/>
            </output>
            <fault name="gwFault">
                <soap:fault name="gwFault" use="literal"/>
            </fault>
        </operation>
    </binding>

    <service name="GWClientMUService">
        <port name="GWClientMUPort" binding="tns:GWClientMUBinding">
            <soap:address location="http://someserver/GWClientMUService/GWClientMU"/>
        </port>
    </service>
</definitions>