package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSBDI_WindAdd")
@XmlRootElement(name="BDI_WindAdd")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_WindAdd extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_WindAdd(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PERIOD_START = "periodStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODSTART", nullable=true, length=34)
private java.sql.Timestamp periodStart;
public java.sql.Timestamp getPeriodStart(){
return this.periodStart;
}
public void setPeriodStart(java.sql.Timestamp periodStart){
this.periodStart = periodStart;
}

public static final String PERIOD_END = "periodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODEND", nullable=true, length=34)
private java.sql.Timestamp periodEnd;
public java.sql.Timestamp getPeriodEnd(){
return this.periodEnd;
}
public void setPeriodEnd(java.sql.Timestamp periodEnd){
this.periodEnd = periodEnd;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String WINDOW_START = "windowStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWSTART", nullable=false, length=34)
private java.sql.Timestamp windowStart;
public java.sql.Timestamp getWindowStart(){
return this.windowStart;
}
public void setWindowStart(java.sql.Timestamp windowStart){
this.windowStart = windowStart;
}

public static final String WINDOW_END = "windowEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWEND", nullable=false, length=34)
private java.sql.Timestamp windowEnd;
public java.sql.Timestamp getWindowEnd(){
return this.windowEnd;
}
public void setWindowEnd(java.sql.Timestamp windowEnd){
this.windowEnd = windowEnd;
}

public static final String GRACE_PERIOD = "gracePeriod";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="GRACEPERIOD", nullable=false, length=34)
private java.sql.Timestamp gracePeriod;
public java.sql.Timestamp getGracePeriod(){
return this.gracePeriod;
}
public void setGracePeriod(java.sql.Timestamp gracePeriod){
this.gracePeriod = gracePeriod;
}

public static final String REF_SESSION = "refSession";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "addWindowSessionProvider", keyProperty = "id")
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDI_Session refSession){
this.refSession = refSession;
}

public static final String REF_PERIOD = "refPeriod";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPERIODID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "addWindowPeriodProvider", keyProperty = "id")
private ATSBDI_Period refPeriod;
public ATSBDI_Period getRefPeriod(){
return this.refPeriod;
}
public void setRefPeriod(ATSBDI_Period refPeriod){
this.refPeriod = refPeriod;
}

public static final String REQUEST_PARTICIPANT = "requestParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodManagementReqPrtProvider", keyProperty = "id")
private ATSPRT_Participant requestParticipant;
public ATSPRT_Participant getRequestParticipant(){
return this.requestParticipant;
}
public void setRequestParticipant(ATSPRT_Participant requestParticipant){
this.requestParticipant = requestParticipant;
}

public static final String MSG_TYPE = "msgType";
@OneToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "allMessageTypeProvider", keyProperty = "id")
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

public static final String RELATED_MSG_TYPE = "relatedMsgType";
@OneToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="RELATEDMSGTYPEID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "messageTypeProvider", keyProperty = "id")
private ATSMSG_Type relatedMsgType;
public ATSMSG_Type getRelatedMsgType(){
return this.relatedMsgType;
}
public void setRelatedMsgType(ATSMSG_Type relatedMsgType){
this.relatedMsgType = relatedMsgType;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "windowAdditionParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String CTGRY_PURP = "ctgryPurp";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSBDI_WindAddCtgrypurp", joinColumns = @JoinColumn(name = "BDI_WINDADD_ID"), inverseJoinColumns = @JoinColumn(name = "CTGRYPURP_ID"))
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private List<ATSMSG_CtgPurp> ctgryPurp = new ArrayList<ATSMSG_CtgPurp>();
public List<ATSMSG_CtgPurp> getCtgryPurp(){
return this.ctgryPurp;
}
public void setCtgryPurp(List<ATSMSG_CtgPurp> ctgryPurp){
this.ctgryPurp = ctgryPurp;
}

@Override
public String toString() {
return "ATSBDI_WindAdd [id= " + getId() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", name= " + getName() + ", windowStart= " + getWindowStart() + ", windowEnd= " + getWindowEnd() + ", gracePeriod= " + getGracePeriod() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_WindAdd other = (ATSBDI_WindAdd) obj;
return this.hashCode() == other.hashCode();}
}


}