package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSLKP_FwdDatedSettings",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"PARTICIPANTID","MSGTYPEID","Z_TENANT_ID"})
})
@XmlRootElement(name="LKP_FwdDatedSettings")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSLKP_FwdDatedSetting extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSLKP_FwdDatedSetting(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String IS_ACTIVE = "isActive";
@Column(name="ISACTIVE", nullable=false, length=1)
private boolean isActive;
public boolean getIsActive(){
return this.isActive;
}
public void setIsActive(boolean isActive){
this.isActive = isActive;
}

public static final String MAX_NUMBER_OF_FUTURE_DAYS = "maxNumberOfFutureDays";
@Column(name="MAXNUMBEROFFUTUREDAYS", nullable=false, length=3)
private long maxNumberOfFutureDays;
public long getMaxNumberOfFutureDays(){
return this.maxNumberOfFutureDays;
}
public void setMaxNumberOfFutureDays(long maxNumberOfFutureDays){
this.maxNumberOfFutureDays = maxNumberOfFutureDays;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "forwardDatedSettingParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String MSG_TYPE = "msgType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "messageTypeProvider", keyProperty = "id")
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

@Override
public String toString() {
return "ATSLKP_FwdDatedSetting [id= " + getId() + ", isActive= " + getIsActive() + ", maxNumberOfFutureDays= " + getMaxNumberOfFutureDays() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
int MaxNumberOfFutureDays= new Long("null".equals(getMaxNumberOfFutureDays() + "") ? 0 : getMaxNumberOfFutureDays()).intValue();
result = prime * result + (int) (MaxNumberOfFutureDays ^ MaxNumberOfFutureDays >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSLKP_FwdDatedSetting other = (ATSLKP_FwdDatedSetting) obj;
return this.hashCode() == other.hashCode();}
}


}