package com.progressoft.ach.entities;

public class WF_WindowAdditionReqs
 {
    // Steps
    public static final String STEP_Initialization = "3000001";
    public static final String STEP_NEW = "3000002";
    public static final String STEP_NEW_AUTHORIZATION = "3000003";
    public static final String STEP_REPAIR = "3000004";
    public static final String STEP_REPAIR_AUTHORIZATION = "3000005";
    public static final String STEP_DELETE_AUTHORIZATION = "3000006";
    public static final String STEP_P_NEW = "3000007";
    public static final String STEP_P_APPROVE_AUTHORIZATION = "3000008";
    public static final String STEP_P_REJECT_AUTHORIZATION = "3000009";
    public static final String STEP_P_REPAIR = "3000010";
    public static final String STEP_DELETED = "3000011";
    public static final String STEP_APPROVED = "3000012";
    public static final String STEP_REJECTED = "3000013";
    // Statuses
    public static final String STEP_STATUS_Initialization = "";
    public static final String STEP_STATUS_NEW = "New";
    public static final String STEP_STATUS_NEW_AUTHORIZATION = "New Authorization";
    public static final String STEP_STATUS_REPAIR = "Repair";
    public static final String STEP_STATUS_REPAIR_AUTHORIZATION = "Repair Authorization";
    public static final String STEP_STATUS_DELETE_AUTHORIZATION = "Delete Authorization";
    public static final String STEP_STATUS_P_NEW = "New";
    public static final String STEP_STATUS_P_APPROVE_AUTHORIZATION = "Approve Authorization";
    public static final String STEP_STATUS_P_REJECT_AUTHORIZATION = "Reject Authorization";
    public static final String STEP_STATUS_P_REPAIR = "Repair";
    public static final String STEP_STATUS_DELETED = "Deleted";
    public static final String STEP_STATUS_APPROVED = "Approved";
    public static final String STEP_STATUS_REJECTED = "Rejected";
    // Action Names
    public static final String ACTION_NAME_Initialize = "Initialize";
    public static final String ACTION_NAME_Create = "Create";
    public static final String ACTION_NAME_Submit = "Submit";
    public static final String ACTION_NAME_Save = "Save";
    public static final String ACTION_NAME_Delete = "Delete";
    public static final String ACTION_NAME_Cancel = "Cancel";
    public static final String ACTION_NAME_SVC_NEW = "SVC_NEW";
    public static final String ACTION_NAME_Approve = "Approve";
    public static final String ACTION_NAME_Reject = "Reject";
    public static final String ACTION_NAME_Repair = "Repair";
    // Action Codes
    public static final String ACTION_CODE_InitializeInitialization = "1";
    public static final String ACTION_CODE_CreateInitialization = "2";
    public static final String ACTION_CODE_SubmitNEW = "3";
    public static final String ACTION_CODE_SaveNEW = "4";
    public static final String ACTION_CODE_DeleteNEW = "5";
    public static final String ACTION_CODE_CancelNEW = "6";
    public static final String ACTION_CODE_SVC_NEWNEW = "7";
    public static final String ACTION_CODE_ApproveNEW_AUTHORIZATION = "8";
    public static final String ACTION_CODE_RejectNEW_AUTHORIZATION = "9";
    public static final String ACTION_CODE_RepairNEW_AUTHORIZATION = "10";
    public static final String ACTION_CODE_CancelNEW_AUTHORIZATION = "11";
    public static final String ACTION_CODE_SubmitREPAIR = "12";
    public static final String ACTION_CODE_SaveREPAIR = "13";
    public static final String ACTION_CODE_DeleteREPAIR = "14";
    public static final String ACTION_CODE_CancelREPAIR = "15";
    public static final String ACTION_CODE_ApproveREPAIR_AUTHORIZATION = "16";
    public static final String ACTION_CODE_RejectREPAIR_AUTHORIZATION = "17";
    public static final String ACTION_CODE_RepairREPAIR_AUTHORIZATION = "18";
    public static final String ACTION_CODE_CancelREPAIR_AUTHORIZATION = "19";
    public static final String ACTION_CODE_ApproveDELETE_AUTHORIZATION = "20";
    public static final String ACTION_CODE_RejectDELETE_AUTHORIZATION = "21";
    public static final String ACTION_CODE_CancelDELETE_AUTHORIZATION = "22";
    public static final String ACTION_CODE_ApproveP_NEW = "23";
    public static final String ACTION_CODE_RejectP_NEW = "24";
    public static final String ACTION_CODE_CancelP_NEW = "25";
    public static final String ACTION_CODE_ApproveP_APPROVE_AUTHORIZATION = "26";
    public static final String ACTION_CODE_RejectP_APPROVE_AUTHORIZATION = "27";
    public static final String ACTION_CODE_CancelP_APPROVE_AUTHORIZATION = "28";
    public static final String ACTION_CODE_ApproveP_REJECT_AUTHORIZATION = "29";
    public static final String ACTION_CODE_RepairP_REJECT_AUTHORIZATION = "30";
    public static final String ACTION_CODE_CancelP_REJECT_AUTHORIZATION = "31";
    public static final String ACTION_CODE_ApproveP_REPAIR = "32";
    public static final String ACTION_CODE_RejectP_REPAIR = "33";
    public static final String ACTION_CODE_CancelP_REPAIR = "34";
    // Action Keys
    public static final String ACTION_KEY_InitializeInitialization = "10";
    public static final String ACTION_KEY_CreateInitialization = "1";
    public static final String ACTION_KEY_SubmitNEW = "1187089095";
    public static final String ACTION_KEY_SaveNEW = "897232819";
    public static final String ACTION_KEY_DeleteNEW = "470764279";
    public static final String ACTION_KEY_CancelNEW = "723680262";
    public static final String ACTION_KEY_SVC_NEWNEW = "1152243879";
    public static final String ACTION_KEY_ApproveNEW_AUTHORIZATION = "955610896";
    public static final String ACTION_KEY_RejectNEW_AUTHORIZATION = "100007222";
    public static final String ACTION_KEY_RepairNEW_AUTHORIZATION = "994047557";
    public static final String ACTION_KEY_CancelNEW_AUTHORIZATION = "761793097";
    public static final String ACTION_KEY_SubmitREPAIR = "444805643";
    public static final String ACTION_KEY_SaveREPAIR = "1413179095";
    public static final String ACTION_KEY_DeleteREPAIR = "793176601";
    public static final String ACTION_KEY_CancelREPAIR = "1992302261";
    public static final String ACTION_KEY_ApproveREPAIR_AUTHORIZATION = "1014861478";
    public static final String ACTION_KEY_RejectREPAIR_AUTHORIZATION = "848970988";
    public static final String ACTION_KEY_RepairREPAIR_AUTHORIZATION = "1313691905";
    public static final String ACTION_KEY_CancelREPAIR_AUTHORIZATION = "59579204";
    public static final String ACTION_KEY_ApproveDELETE_AUTHORIZATION = "34940017";
    public static final String ACTION_KEY_RejectDELETE_AUTHORIZATION = "1606589351";
    public static final String ACTION_KEY_CancelDELETE_AUTHORIZATION = "166597958";
    public static final String ACTION_KEY_ApproveP_NEW = "705820498";
    public static final String ACTION_KEY_RejectP_NEW = "396359177";
    public static final String ACTION_KEY_CancelP_NEW = "736851082";
    public static final String ACTION_KEY_ApproveP_APPROVE_AUTHORIZATION = "2125173905";
    public static final String ACTION_KEY_RejectP_APPROVE_AUTHORIZATION = "1637649224";
    public static final String ACTION_KEY_CancelP_APPROVE_AUTHORIZATION = "1273826304";
    public static final String ACTION_KEY_ApproveP_REJECT_AUTHORIZATION = "1950297630";
    public static final String ACTION_KEY_RepairP_REJECT_AUTHORIZATION = "1876763110";
    public static final String ACTION_KEY_CancelP_REJECT_AUTHORIZATION = "1668800748";
    public static final String ACTION_KEY_ApproveP_REPAIR = "313868798";
    public static final String ACTION_KEY_RejectP_REPAIR = "87382239";
    public static final String ACTION_KEY_CancelP_REPAIR = "937206257";

}