package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_Defaultings")
@XmlRootElement(name="PRT_Defaultings")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_Defaulting extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_Defaulting(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String DEFAULT_FLAG = "defaultFlag";
@Column(name="DEFAULTFLAG", nullable=true, length=1)
private boolean defaultFlag;
public boolean getDefaultFlag(){
return this.defaultFlag;
}
public void setDefaultFlag(boolean defaultFlag){
this.defaultFlag = defaultFlag;
}

public static final String NOTE = "note";
@Column(name="NOTE", nullable=false, length=1024)
private String note;
public String getNote(){
return this.note;
}
public void setNote(String note){
this.note = note;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "prtDefaultingParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

@Override
public String toString() {
return "ATSPRT_Defaulting [id= " + getId() + ", defaultFlag= " + getDefaultFlag() + ", note= " + getNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_Defaulting other = (ATSPRT_Defaulting) obj;
return this.hashCode() == other.hashCode();}
}


}