package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.attachments.AttachmentItem;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Entity
@Table(name="ATSCreditorCreationReqATT")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSCreditorCreationReqAtt extends AttachmentItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSCreditorCreationReqAtt(){/*Default Constructor*/}


}