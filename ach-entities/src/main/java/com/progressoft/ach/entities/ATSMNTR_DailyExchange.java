package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMNTR_DailyExchange")
@XmlRootElement(name="MNTR_DailyExchange")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMNTR_DailyExchange extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMNTR_DailyExchange(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PART_NAME = "partName";
@Column(name="PARTNAME", nullable=true, length=255)
private String partName;
public String getPartName(){
return this.partName;
}
public void setPartName(String partName){
this.partName = partName;
}

public static final String TOTAL_CREDIT = "totalCredit";
@Column(name="TOTALCREDIT", nullable=true, length=21)
private long totalCredit;
public long getTotalCredit(){
return this.totalCredit;
}
public void setTotalCredit(long totalCredit){
this.totalCredit = totalCredit;
}

public static final String TOTAL_DEBIT = "totalDebit";
@Column(name="TOTALDEBIT", nullable=true, length=21)
private long totalDebit;
public long getTotalDebit(){
return this.totalDebit;
}
public void setTotalDebit(long totalDebit){
this.totalDebit = totalDebit;
}

public static final String TOTAL_P_S_R = "totalPSR";
@Column(name="TOTALPSR", nullable=true, length=21)
private long totalPSR;
public long getTotalPSR(){
return this.totalPSR;
}
public void setTotalPSR(long totalPSR){
this.totalPSR = totalPSR;
}

public static final String TOTAL_R_F_C = "totalRFC";
@Column(name="TOTALRFC", nullable=true, length=21)
private long totalRFC;
public long getTotalRFC(){
return this.totalRFC;
}
public void setTotalRFC(long totalRFC){
this.totalRFC = totalRFC;
}

@Override
public String toString() {
return "ATSMNTR_DailyExchange [id= " + getId() + ", partName= " + getPartName() + ", totalCredit= " + getTotalCredit() + ", totalDebit= " + getTotalDebit() + ", totalPSR= " + getTotalPSR() + ", totalRFC= " + getTotalRFC() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getPartName() == null) ? 0 : getPartName().hashCode());
int TotalCredit= new Long("null".equals(getTotalCredit() + "") ? 0 : getTotalCredit()).intValue();
result = prime * result + (int) (TotalCredit ^ TotalCredit >>> 32);
int TotalDebit= new Long("null".equals(getTotalDebit() + "") ? 0 : getTotalDebit()).intValue();
result = prime * result + (int) (TotalDebit ^ TotalDebit >>> 32);
int TotalPSR= new Long("null".equals(getTotalPSR() + "") ? 0 : getTotalPSR()).intValue();
result = prime * result + (int) (TotalPSR ^ TotalPSR >>> 32);
int TotalRFC= new Long("null".equals(getTotalRFC() + "") ? 0 : getTotalRFC()).intValue();
result = prime * result + (int) (TotalRFC ^ TotalRFC >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMNTR_DailyExchange other = (ATSMNTR_DailyExchange) obj;
return this.hashCode() == other.hashCode();}
}


}