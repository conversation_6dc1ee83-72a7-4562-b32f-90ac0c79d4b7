package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name="ATSACL_Configs")
@XmlRootElement(name="ATSACL_Configs")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
@Searchable({
        @Searchable.SearchableField(fieldPath = "viewName", label = "JFW_PROPERTIES.**********", searchType = SearchType.DROP_DOWN, possibleValuesListBean = "paymentViewNameProvider")
})
public class ATSACL_Config extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSACL_Config(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String VIEW_NAME = "viewName";
@Column(name="VIEWNAME", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "viewNamesProvider")
private String viewName;
public String getViewName(){
return this.viewName;
}
public void setViewName(String viewName){
this.viewName = viewName;
}

public static final String USERS = "users";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSACL_CONFIGSUSERS", joinColumns = @JoinColumn(name = "ACL_CONFIGS_ID"), inverseJoinColumns = @JoinColumn(name = "USERS_ID"))
@WithValueProvider(jupiterValueProviderBean = "keycloakUsersProvider", keyProperty = "id")
private List<ATSKC_User> users = new ArrayList<>();
public List<ATSKC_User> getUsers(){
    return this.users;
}
public void setUsers(List<ATSKC_User> users){
        this.users = users;
    }

public static final String MAXAMOUNT = "maxAmount";
@Column(name="MAXAMOUNT", nullable=true)
private BigDecimal maxAmount;
public BigDecimal getMaxAmount(){
    return this.maxAmount;
}
public void setMaxAmount(BigDecimal maxAmount){
        this.maxAmount = maxAmount;
    }

public static final String ACCOUNTS = "accounts";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSACL_CONFIGSACCOUNTS", joinColumns = @JoinColumn(name = "ACL_CONFIGS_ID"), inverseJoinColumns = @JoinColumn(name = "ACCOUNTS_ID"))
@WithValueProvider(jupiterValueProviderBean = "aclAccountsProvider", keyProperty = "id")
private List<ATSACC_Account> accounts = new ArrayList<>();
public List<ATSACC_Account> getAccounts(){
    return this.accounts;
}
public void setAccounts(List<ATSACC_Account> accounts){
        this.accounts = accounts;
    }

@Override
public String toString() {
    return "ATSACL_Config{" +
            "id=" + id +
            ", viewName='" + viewName + '\'' +
            ", users=" + users +
            ", maxAmount=" + maxAmount +
            ", accounts=" + accounts +
            '}';
}

@Override
public int hashCode() {
    return Objects.hash(id, viewName, users, maxAmount, accounts);
}

    @Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {
    ATSACL_Config other = (ATSACL_Config) obj;
return this.hashCode() == other.hashCode();}
}
}