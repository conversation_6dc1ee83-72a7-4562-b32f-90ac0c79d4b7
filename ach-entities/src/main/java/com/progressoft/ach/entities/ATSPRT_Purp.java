package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSPRT_Purps",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"PARTICIPANTID","Z_TENANT_ID"})
})
@XmlRootElement(name="PRT_Purps")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSPRT_Purp extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_Purp(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "purpParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String CATEGORY_PURPOSES = "categoryPurposes";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSPRT_PurpsCategorypurposes", joinColumns = @JoinColumn(name = "PRT_PURPS_ID"), inverseJoinColumns = @JoinColumn(name = "CATEGORYPURPOSES_ID"))
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private List<ATSMSG_CtgPurp> categoryPurposes = new ArrayList<ATSMSG_CtgPurp>();
public List<ATSMSG_CtgPurp> getCategoryPurposes(){
return this.categoryPurposes;
}
public void setCategoryPurposes(List<ATSMSG_CtgPurp> categoryPurposes){
this.categoryPurposes = categoryPurposes;
}

@Override
public String toString() {
return "ATSPRT_Purp [id= " + getId() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_Purp other = (ATSPRT_Purp) obj;
return this.hashCode() == other.hashCode();}
}


}