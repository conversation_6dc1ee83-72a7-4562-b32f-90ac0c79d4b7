package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name = "ATSUser_AllotmentView")
@XmlRootElement(name = "ATSUser_AllotmentView")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE)
@Searchable({
        @Searchable.SearchableField(fieldPath = "name", label = "Name", searchType = SearchType.LIKE)
})
public class ATSUser_AllotmentView extends JFWEntity implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    @Column(name = "NAME")
    private String name;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ATSUser_AllotmentView that = (ATSUser_AllotmentView) o;
        return id == that.id && Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, name);
    }


    @Override
    public String toString() {
        return "ATSUser_AllotmentView{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
