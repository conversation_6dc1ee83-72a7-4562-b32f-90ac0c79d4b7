package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSBDS_Windows",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"REFPERIODID","NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="BDS_Windows")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDS_Window extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_Window(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String OFFSET = "offset";
@Column(name="OFFSET", nullable=false, length=20)
private long offset;
public long getOffset(){
return this.offset;
}
public void setOffset(long offset){
this.offset = offset;
}

public static final String DURATION = "duration";
@Column(name="DURATION", nullable=false, length=20)
private long duration;
public long getDuration(){
return this.duration;
}
public void setDuration(long duration){
this.duration = duration;
}

public static final String GRACE_PERIOD = "gracePeriod";
@Column(name="GRACEPERIOD", nullable=true, length=3)
private long gracePeriod;
public long getGracePeriod(){
return this.gracePeriod;
}
public void setGracePeriod(long gracePeriod){
this.gracePeriod = gracePeriod;
}

public static final String REF_PERIOD = "refPeriod";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPERIODID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "bdsPeriodProvider", keyProperty = "id")
private ATSBDS_Period refPeriod;
public ATSBDS_Period getRefPeriod(){
return this.refPeriod;
}
public void setRefPeriod(ATSBDS_Period refPeriod){
this.refPeriod = refPeriod;
}

public static final String MSG_TYPE = "msgType";
@OneToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "allMessageTypeProvider", keyProperty = "id")
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

public static final String _ORGN_MSG_TYPE = "OrgnMsgType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="ORGNMSGTYPEID", nullable=true)
private ATSMSG_Type OrgnMsgType;
public ATSMSG_Type getOrgnMsgType(){
return this.OrgnMsgType;
}
public void setOrgnMsgType(ATSMSG_Type OrgnMsgType){
this.OrgnMsgType = OrgnMsgType;
}

public static final String PARTICIPANTS = "participants";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSBDS_WindowsParticipants", joinColumns = @JoinColumn(name = "BDS_WINDOWS_ID"), inverseJoinColumns = @JoinColumn(name = "PARTICIPANTS_ID"))
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private List<ATSPRT_Participant> participants = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getParticipants(){
return this.participants;
}
public void setParticipants(List<ATSPRT_Participant> participants){
this.participants = participants;
}

public static final String CATEGORY_PURPOSES = "categoryPurposes";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSBDS_WindowsCategorypurposes", joinColumns = @JoinColumn(name = "BDS_WINDOWS_ID"), inverseJoinColumns = @JoinColumn(name = "CATEGORYPURPOSES_ID"))
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private List<ATSMSG_CtgPurp> categoryPurposes = new ArrayList<ATSMSG_CtgPurp>();
public List<ATSMSG_CtgPurp> getCategoryPurposes(){
return this.categoryPurposes;
}
public void setCategoryPurposes(List<ATSMSG_CtgPurp> categoryPurposes){
this.categoryPurposes = categoryPurposes;
}

@Override
public String toString() {
return "ATSBDS_Window [id= " + getId() + ", name= " + getName() + ", description= " + getDescription() + ", offset= " + getOffset() + ", duration= " + getDuration() + ", gracePeriod= " + getGracePeriod() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int Offset= new Long("null".equals(getOffset() + "") ? 0 : getOffset()).intValue();
result = prime * result + (int) (Offset ^ Offset >>> 32);
int Duration= new Long("null".equals(getDuration() + "") ? 0 : getDuration()).intValue();
result = prime * result + (int) (Duration ^ Duration >>> 32);
int GracePeriod= new Long("null".equals(getGracePeriod() + "") ? 0 : getGracePeriod()).intValue();
result = prime * result + (int) (GracePeriod ^ GracePeriod >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_Window other = (ATSBDS_Window) obj;
return this.hashCode() == other.hashCode();}
}


}