package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSCreditorCreationReqs")
@XmlRootElement(name="CreditorCreationReqs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSCreditorCreationReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSCreditorCreationReq(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_ID = "requestId";
@Column(name="REQUESTID", nullable=false, length=4000)
private String requestId;
public String getRequestId(){
return this.requestId;
}
public void setRequestId(String requestId){
this.requestId = requestId;
}

public static final String CREDITOR_CODE = "creditorCode";
@Column(name="CREDITORCODE", nullable=false, length=12)
private String creditorCode;
public String getCreditorCode(){
return this.creditorCode;
}
public void setCreditorCode(String creditorCode){
this.creditorCode = creditorCode;
}

public static final String CREDITOR_NAME = "creditorName";
@Column(name="CREDITORNAME", nullable=false, length=70)
private String creditorName;
public String getCreditorName(){
return this.creditorName;
}
public void setCreditorName(String creditorName){
this.creditorName = creditorName;
}

public static final String CREDITOR_ACCOUNT_NUMBER = "creditorAccountNumber";
@Column(name="CREDITORACCOUNTNUMBER", nullable=false, length=34)
private String creditorAccountNumber;
public String getCreditorAccountNumber(){
return this.creditorAccountNumber;
}
public void setCreditorAccountNumber(String creditorAccountNumber){
this.creditorAccountNumber = creditorAccountNumber;
}

public static final String CREDITOR_ID_VALUE = "creditorIdValue";
@Column(name="CREDITORIDVALUE", nullable=true, length=35)
private String creditorIdValue;
public String getCreditorIdValue(){
return this.creditorIdValue;
}
public void setCreditorIdValue(String creditorIdValue){
this.creditorIdValue = creditorIdValue;
}

public static final String EFFECTIVE_START_DATE = "effectiveStartDate";
@Column(name="EFFECTIVESTARTDATE", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date effectiveStartDate;
public java.util.Date getEffectiveStartDate(){
return this.effectiveStartDate;
}
public void setEffectiveStartDate(java.util.Date effectiveStartDate){
this.effectiveStartDate = effectiveStartDate;
}

public static final String EFFECTIVE_END_DATE = "effectiveEndDate";
@Column(name="EFFECTIVEENDDATE", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date effectiveEndDate;
public java.util.Date getEffectiveEndDate(){
return this.effectiveEndDate;
}
public void setEffectiveEndDate(java.util.Date effectiveEndDate){
this.effectiveEndDate = effectiveEndDate;
}

public static final String REJECTION_NOTE = "rejectionNote";
@Column(name="REJECTIONNOTE", nullable=true, length=4000)
private String rejectionNote;
public String getRejectionNote(){
return this.rejectionNote;
}
public void setRejectionNote(String rejectionNote){
this.rejectionNote = rejectionNote;
}

public static final String REQUESTING_PARTICIPANT = "requestingParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTINGPARTICIPANTID", nullable=false)
private ATSPRT_Participant requestingParticipant;
public ATSPRT_Participant getRequestingParticipant(){
return this.requestingParticipant;
}
public void setRequestingParticipant(ATSPRT_Participant requestingParticipant){
this.requestingParticipant = requestingParticipant;
}

public static final String CREDITOR_BRANCH = "creditorBranch";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CREDITORBRANCHID", nullable=false)
private ATSPRT_Branch creditorBranch;
public ATSPRT_Branch getCreditorBranch(){
return this.creditorBranch;
}
public void setCreditorBranch(ATSPRT_Branch creditorBranch){
this.creditorBranch = creditorBranch;
}

public static final String CREDITOR_ID_TYPE = "creditorIdType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CREDITORIDTYPEID", nullable=true)
private ATSLKP_PrivateIdentification creditorIdType;
public ATSLKP_PrivateIdentification getCreditorIdType(){
return this.creditorIdType;
}
public void setCreditorIdType(ATSLKP_PrivateIdentification creditorIdType){
this.creditorIdType = creditorIdType;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "requestRReasonProvider", keyProperty = "id")
private ATSRequestRReason rejectionReason;
public ATSRequestRReason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSRequestRReason rejectionReason){
this.rejectionReason = rejectionReason;
}

@Override
public String toString() {
return "ATSCreditorCreationReq [id= " + getId() + ", requestId= " + getRequestId() + ", creditorCode= " + getCreditorCode() + ", creditorName= " + getCreditorName() + ", creditorAccountNumber= " + getCreditorAccountNumber() + ", creditorIdValue= " + getCreditorIdValue() + ", effectiveStartDate= " + getEffectiveStartDate() + ", effectiveEndDate= " + getEffectiveEndDate() + ", rejectionNote= " + getRejectionNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getRequestId() == null) ? 0 : getRequestId().hashCode());
result = prime * result + ((getCreditorCode() == null) ? 0 : getCreditorCode().hashCode());
result = prime * result + ((getCreditorName() == null) ? 0 : getCreditorName().hashCode());
result = prime * result + ((getCreditorAccountNumber() == null) ? 0 : getCreditorAccountNumber().hashCode());
result = prime * result + ((getCreditorIdValue() == null) ? 0 : getCreditorIdValue().hashCode());
result = prime * result + ((getEffectiveStartDate() == null) ? 0 : getEffectiveStartDate().hashCode());
result = prime * result + ((getEffectiveEndDate() == null) ? 0 : getEffectiveEndDate().hashCode());
result = prime * result + ((getRejectionNote() == null) ? 0 : getRejectionNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSCreditorCreationReq other = (ATSCreditorCreationReq) obj;
return this.hashCode() == other.hashCode();}
}


}