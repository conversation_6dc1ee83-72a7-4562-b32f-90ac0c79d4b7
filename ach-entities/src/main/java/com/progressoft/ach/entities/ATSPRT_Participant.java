package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSPRT_Participants",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"CODE", "Z_TENANT_ID"}),
                        @UniqueConstraint(columnNames = {"NAME", "Z_TENANT_ID"}),
                        @UniqueConstraint(columnNames = {"PREFIX", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "PRT_Participants")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE)
public class ATSPRT_Participant extends JFWLookableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSPRT_Participant() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String HEAD_OFFICE_BRANCH_CODE = "headOfficeBranchCode";
    @Column(name = "HEADOFFICEBRANCHCODE", nullable = false, length = 3)
    private String headOfficeBranchCode;

    public String getHeadOfficeBranchCode() {
        return this.headOfficeBranchCode;
    }

    public void setHeadOfficeBranchCode(String headOfficeBranchCode) {
        this.headOfficeBranchCode = headOfficeBranchCode;
    }

    public static final String ACCOUNT_NUMBER_LENGTH = "accountNumberLength";
    @Column(name = "ACCOUNTNUMBERLENGTH", nullable = false, length = 2)
    private long accountNumberLength;

    public long getAccountNumberLength() {
        return this.accountNumberLength;
    }

    public void setAccountNumberLength(long accountNumberLength) {
        this.accountNumberLength = accountNumberLength;
    }

    public static final String COUNTRY = "country";
    @Column(name = "COUNTRY", nullable = true, length = 150)
    private String country;

    public String getCountry() {
        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public static final String CITY = "city";
    @Column(name = "CITY", nullable = true, length = 150)
    private String city;

    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public static final String PHONE = "phone";
    @Column(name = "PHONE", nullable = true, length = 32)
    private String phone;

    public String getPhone() {
        return this.phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public static final String EMAIL = "email";
    @Column(name = "EMAIL", nullable = true, length = 128)
    private String email;

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public static final String ADDRESS = "address";
    @Column(name = "ADDRESS", nullable = true, length = 4000)
    private String address;

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public static final String SEND_SUSP_FLAG = "sendSuspFlag";
    @Column(name = "SENDSUSPFLAG", nullable = true, length = 1)
    private boolean sendSuspFlag;

    public boolean getSendSuspFlag() {
        return this.sendSuspFlag;
    }

    public void setSendSuspFlag(boolean sendSuspFlag) {
        this.sendSuspFlag = sendSuspFlag;
    }

    public static final String RECEIVE_SUSP_FLAG = "receiveSuspFlag";
    @Column(name = "RECEIVESUSPFLAG", nullable = true, length = 1)
    private boolean receiveSuspFlag;

    public boolean getReceiveSuspFlag() {
        return this.receiveSuspFlag;
    }

    public void setReceiveSuspFlag(boolean receiveSuspFlag) {
        this.receiveSuspFlag = receiveSuspFlag;
    }

    public static final String DEFAULT_FLAG = "defaultFlag";
    @Column(name = "DEFAULTFLAG", nullable = true, length = 1)
    private boolean defaultFlag;

    public boolean getDefaultFlag() {
        return this.defaultFlag;
    }

    public void setDefaultFlag(boolean defaultFlag) {
        this.defaultFlag = defaultFlag;
    }

    public static final String TERMINATE_FLAG = "terminateFlag";
    @Column(name = "TERMINATEFLAG", nullable = true, length = 1)
    private boolean terminateFlag;

    public boolean getTerminateFlag() {
        return this.terminateFlag;
    }

    public void setTerminateFlag(boolean terminateFlag) {
        this.terminateFlag = terminateFlag;
    }

    public static final String IS_ONUS_ALLOWED = "isOnusAllowed";
    @Column(name = "ISONUSALLOWED", nullable = true, length = 1)
    private boolean isOnusAllowed;

    public boolean getIsOnusAllowed() {
        return this.isOnusAllowed;
    }

    public void setIsOnusAllowed(boolean isOnusAllowed) {
        this.isOnusAllowed = isOnusAllowed;
    }

    public static final String CHRG_EXCLUSION_FLAG = "chrgExclusionFlag";
    @Column(name = "CHRGEXCLUSIONFLAG", nullable = true, length = 1)
    private boolean chrgExclusionFlag;

    public boolean getChrgExclusionFlag() {
        return this.chrgExclusionFlag;
    }

    public void setChrgExclusionFlag(boolean chrgExclusionFlag) {
        this.chrgExclusionFlag = chrgExclusionFlag;
    }

    public static final String FORMAT = "format";
    @Column(name = "FORMAT", nullable = false, length = 20)
    private String format;

    public String getFormat() {
        return this.format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public static final String PREFIX = "prefix";
    @Column(name = "PREFIX", nullable = false, length = 20)
    private String prefix;

    public String getPrefix() {
        return this.prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public static final String PACS_MESSAGE_VERSION = "pacsMessageVersion";
    @Column(name = "PACSMESSAGEVERSION", nullable = true, length = 255)
    private String pacsMessageVersion;

    public String getPacsMessageVersion() {
        return this.pacsMessageVersion;
    }

    public void setPacsMessageVersion(String pacsMessageVersion) {
        this.pacsMessageVersion = pacsMessageVersion;
    }

    public static final String PARTICIPANT_TYPE = "participantType";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "PARTICIPANTTYPEID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantTypeProvider", keyProperty = "id")
    private ATSPRT_Type participantType;

    public ATSPRT_Type getParticipantType() {
        return this.participantType;
    }

    public void setParticipantType(ATSPRT_Type participantType) {
        this.participantType = participantType;
    }

    public static final String COMM_PART = "commPart";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMMPARTID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "communicationParticipantsProvider", keyProperty = "id")
    private ATSPRT_Participant commPart;

    public ATSPRT_Participant getCommPart() {
        return this.commPart;
    }

    public void setCommPart(ATSPRT_Participant commPart) {
        this.commPart = commPart;
    }

    public static final String INSTITUTION_TYPE = "institutionType";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "INSTITUTIONTYPEID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "institutionTypeProvider", keyProperty = "id")
    private ATSPRT_Institution institutionType;

    public ATSPRT_Institution getInstitutionType() {
        return this.institutionType;
    }

    public void setInstitutionType(ATSPRT_Institution institutionType) {
        this.institutionType = institutionType;
    }

    public static final String LIMITS_PROFILE = "limitsProfile";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "LIMITSPROFILEID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "limitsProfileProvider", keyProperty = "id")
    private ATSPRT_LimitsProfile limitsProfile;

    public ATSPRT_LimitsProfile getLimitsProfile() {
        return this.limitsProfile;
    }

    public void setLimitsProfile(ATSPRT_LimitsProfile limitsProfile) {
        this.limitsProfile = limitsProfile;
    }

    public static final String REF_PARTICIPANT_P_R_T_SETT_PARTICIPANTS = "refParticipantPRT_SettParticipants";
    @OneToMany(mappedBy = "refParticipant")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSPRT_SettParticipant> refParticipantPRT_SettParticipants;

    @Column(name = "FUTUREDATEDPAYMENTALLOWED")
    private boolean isFutureDatedPaymentAllowed = true;
    public boolean getIsFutureDatedPaymentAllowed() {
        return isFutureDatedPaymentAllowed;
    }
    public void setIsFutureDatedPaymentAllowed(boolean futureDatedPaymentAllowed) {
        this.isFutureDatedPaymentAllowed = futureDatedPaymentAllowed;
    }

    public List<ATSPRT_SettParticipant> getRefParticipantPRTSettParticipants() {
        return this.refParticipantPRT_SettParticipants;
    }

    public void setRefParticipantPRTSettParticipants(List<ATSPRT_SettParticipant> refParticipantPRT_SettParticipants) {
        this.refParticipantPRT_SettParticipants = refParticipantPRT_SettParticipants;
    }

    @Override
    public String toString() {
        return "ATSPRT_Participant [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", headOfficeBranchCode= " + getHeadOfficeBranchCode() + ", accountNumberLength= " + getAccountNumberLength() + ", country= " + getCountry() + ", city= " + getCity() + ", phone= " + getPhone() + ", email= " + getEmail() + ", address= " + getAddress() + ", sendSuspFlag= " + getSendSuspFlag() + ", receiveSuspFlag= " + getReceiveSuspFlag() + ", defaultFlag= " + getDefaultFlag() + ", terminateFlag= " + getTerminateFlag() + ", isOnusAllowed= " + getIsOnusAllowed() + ", chrgExclusionFlag= " + getChrgExclusionFlag() + ", format= " + getFormat() + ", prefix= " + getPrefix() + ", pacsMessageVersion= " + getPacsMessageVersion() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        result = prime * result + ((getHeadOfficeBranchCode() == null) ? 0 : getHeadOfficeBranchCode().hashCode());
        int AccountNumberLength = new Long("null".equals(getAccountNumberLength() + "") ? 0 : getAccountNumberLength()).intValue();
        result = prime * result + (int) (AccountNumberLength ^ AccountNumberLength >>> 32);
        result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
        result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
        result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
        result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
        result = prime * result + ((getPrefix() == null) ? 0 : getPrefix().hashCode());
        result = prime * result + ((getPacsMessageVersion() == null) ? 0 : getPacsMessageVersion().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSPRT_Participant other = (ATSPRT_Participant) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}