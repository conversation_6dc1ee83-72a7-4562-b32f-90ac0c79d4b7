package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSBDS_Templates",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"CURRENCYID","Z_TENANT_ID"})
})
@XmlRootElement(name="BDS_Templates")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSBDS_Template extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_Template(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PREP_START = "prepStart";
@Column(name="PREPSTART", nullable=false, length=20)
private long prepStart;
public long getPrepStart(){
return this.prepStart;
}
public void setPrepStart(long prepStart){
this.prepStart = prepStart;
}

public static final String PREP_STOP = "prepStop";
@Column(name="PREPSTOP", nullable=false, length=20)
private long prepStop;
public long getPrepStop(){
    return this.prepStop;
}

    public void setPrepStop(long prepStop) {
        this.prepStop = prepStop;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }


    @Override
    public String toString() {
        return "ATSBDS_Template [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", prepStart= " + getPrepStart() + ", prepStop= " + getPrepStop() + "]";
    }

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int PrepStart= new Long("null".equals(getPrepStart() + "") ? 0 : getPrepStart()).intValue();
result = prime * result + (int) (PrepStart ^ PrepStart >>> 32);
int PrepStop= new Long("null".equals(getPrepStop() + "") ? 0 : getPrepStop()).intValue();
result = prime * result + (int) (PrepStop ^ PrepStop >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_Template other = (ATSBDS_Template) obj;
return this.hashCode() == other.hashCode();}
}


}