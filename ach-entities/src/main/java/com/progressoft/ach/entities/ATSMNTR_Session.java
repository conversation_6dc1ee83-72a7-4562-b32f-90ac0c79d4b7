package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;

@Entity

@Table(name="ATSMNTR_Sessions")
@XmlRootElement(name="MNTR_Sessions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMNTR_Session extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMNTR_Session(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String SESSION_SEQ = "sessionSeq";
@Column(name="SESSIONSEQ", nullable=true, length=255)
private String sessionSeq;
public String getSessionSeq(){
return this.sessionSeq;
}
public void setSessionSeq(String sessionSeq){
this.sessionSeq = sessionSeq;
}

public static final String CURRENCY_CODE = "currencyCode";
@Column(name="CURRENCYCODE", nullable=true, length=4)
private String currencyCode;
public String getCurrencyCode(){
return this.currencyCode;
}
public void setCurrencyCode(String currencyCode){
this.currencyCode = currencyCode;
}

public static final String BUSINESS_D_T = "businessDT";
@Column(name="BUSINESSDT", nullable=true, length=25)
@Temporal(TemporalType.DATE)
private java.util.Date businessDT;
public java.util.Date getBusinessDT(){
return this.businessDT;
}
public void setBusinessDT(java.util.Date businessDT){
this.businessDT = businessDT;
}

public static final String PERIOD_NAME = "periodName";
@Column(name="PERIODNAME", nullable=true, length=21)
private String periodName;
public String getPeriodName(){
return this.periodName;
}
public void setPeriodName(String periodName){
this.periodName = periodName;
}

public static final String PERIOD_START = "periodStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODSTART", nullable=true, length=25)
private java.sql.Timestamp periodStart;
public java.sql.Timestamp getPeriodStart(){
return this.periodStart;
}
public void setPeriodStart(java.sql.Timestamp periodStart){
this.periodStart = periodStart;
}

public static final String PERIOD_END = "periodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODEND", nullable=true, length=25)
private java.sql.Timestamp periodEnd;
public java.sql.Timestamp getPeriodEnd(){
return this.periodEnd;
}
public void setPeriodEnd(java.sql.Timestamp periodEnd){
this.periodEnd = periodEnd;
}

public static final String READY_FLAG = "readyFlag";
@Column(name="READYFLAG", nullable=true, length=1)
private boolean readyFlag;
public boolean getReadyFlag(){
return this.readyFlag;
}
public void setReadyFlag(boolean readyFlag){
this.readyFlag = readyFlag;
}

@Override
public String toString() {
return "ATSMNTR_Session [id= " + getId() + ", sessionSeq= " + getSessionSeq() + ", currencyCode= " + getCurrencyCode() + ", businessDT= " + getBusinessDT() + ", periodName= " + getPeriodName() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", readyFlag= " + getReadyFlag() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getSessionSeq() == null) ? 0 : getSessionSeq().hashCode());
result = prime * result + ((getCurrencyCode() == null) ? 0 : getCurrencyCode().hashCode());
result = prime * result + ((getBusinessDT() == null) ? 0 : getBusinessDT().hashCode());
result = prime * result + ((getPeriodName() == null) ? 0 : getPeriodName().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMNTR_Session other = (ATSMNTR_Session) obj;
return this.hashCode() == other.hashCode();}
}


}