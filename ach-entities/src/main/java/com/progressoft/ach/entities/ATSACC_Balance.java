package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSACC_Balances",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"REFACCOUNTID","REFSESSIONID","SETTLEMENTDT","CURRENCYID","Z_TENANT_ID"})
})
@XmlRootElement(name="ACC_Balances")
@XmlAccessorType(XmlAccessType.FIELD)
@Searchable({
        @Searchable.SearchableField(fieldPath = "refAccount.participant.name", label = "Participant", searchType = SearchType.EQUAL_STRING)
})
public class ATSACC_Balance extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSACC_Balance(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String SETTLEMENT_DT = "settlementDt";
@Column(name="SETTLEMENTDT", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date settlementDt;
public java.util.Date getSettlementDt(){
return this.settlementDt;
}
public void setSettlementDt(java.util.Date settlementDt){
this.settlementDt = settlementDt;
}

public static final String START_BALANCE = "startBalance";
@Column(name="STARTBALANCE", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal startBalance;
public java.math.BigDecimal getStartBalance(){
return this.startBalance;
}
public void setStartBalance(java.math.BigDecimal startBalance){
this.startBalance = startBalance;
}

public static final String DEBIT = "debit";
@Column(name="DEBIT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal debit;
public java.math.BigDecimal getDebit(){
return this.debit;
}
public void setDebit(java.math.BigDecimal debit){
this.debit = debit;
}

public static final String CREDIT = "credit";
@Column(name="CREDIT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal credit;
public java.math.BigDecimal getCredit(){
return this.credit;
}
public void setCredit(java.math.BigDecimal credit){
this.credit = credit;
}

public static final String BALANCE = "balance";
@Column(name="BALANCE", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal balance;
public java.math.BigDecimal getBalance(){
return this.balance;
}
public void setBalance(java.math.BigDecimal balance){
this.balance = balance;
}

public static final String DEBIT_CAP = "debitCap";
@Column(name="DEBITCAP", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal debitCap;
public java.math.BigDecimal getDebitCap(){
return this.debitCap;
}
public void setDebitCap(java.math.BigDecimal debitCap){
this.debitCap = debitCap;
}

public static final String CREDIT_CAP = "creditCap";
@Column(name="CREDITCAP", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal creditCap;
public java.math.BigDecimal getCreditCap(){
return this.creditCap;
}
public void setCreditCap(java.math.BigDecimal creditCap){
this.creditCap = creditCap;
}

public static final String MIN_BALANCE = "minBalance";
@Column(name="MINBALANCE", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal minBalance;
public java.math.BigDecimal getMinBalance(){
return this.minBalance;
}
public void setMinBalance(java.math.BigDecimal minBalance){
this.minBalance = minBalance;
}

public static final String NOTE = "note";
@Column(name="NOTE", nullable=true, length=100)
private String note;
public String getNote(){
return this.note;
}
public void setNote(String note){
this.note = note;
}

public static final String REF_ACCOUNT = "refAccount";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFACCOUNTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "accountsProvider", keyProperty = "id")
private ATSACC_Account refAccount;
public ATSACC_Account getRefAccount(){
return this.refAccount;
}
public void setRefAccount(ATSACC_Account refAccount){
this.refAccount = refAccount;
}

public static final String REF_SESSION = "refSession";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
    return this.refSession;
}

public void setRefSession(ATSBDI_Session refSession) {
    this.refSession = refSession;
}

public static final String CURRENCY = "currency";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name = "CURRENCYID", nullable = false)
@WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

public static final String REF_ACC_BALANCE_A_C_C_CAPS_UPDATE_REQ = "refAccBalanceACC_CapsUpdateReq";
@OneToMany(mappedBy = "refAccBalance")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@WithValueProvider(jupiterValueProviderBean = "capsUpdateReqProvider", keyProperty = "id")
private List<ATSACC_CapsUpdateReq> refAccBalanceACC_CapsUpdateReq;
public List<ATSACC_CapsUpdateReq> getRefAccBalanceACCCapsUpdateReq(){
return this.refAccBalanceACC_CapsUpdateReq;
}
public void setRefAccBalanceACCCapsUpdateReq(List<ATSACC_CapsUpdateReq> refAccBalanceACC_CapsUpdateReq){
this.refAccBalanceACC_CapsUpdateReq = refAccBalanceACC_CapsUpdateReq;
}

@Override
public String toString() {
return "ATSACC_Balance [id= " + getId() + ", settlementDt= " + getSettlementDt() + ", startBalance= " + getStartBalance() + ", debit= " + getDebit() + ", credit= " + getCredit() + ", balance= " + getBalance() + ", debitCap= " + getDebitCap() + ", creditCap= " + getCreditCap() + ", minBalance= " + getMinBalance() + ", note= " + getNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
result = prime * result + ((getNote() == null) ? 0 : getNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSACC_Balance other = (ATSACC_Balance) obj;
return this.hashCode() == other.hashCode();}
}


}