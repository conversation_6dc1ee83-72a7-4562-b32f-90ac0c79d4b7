package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_Suspensions")
@XmlRootElement(name="PRT_Suspensions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_Suspension extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_Suspension(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String SEND_SUSP_FLAG = "sendSuspFlag";
@Column(name="SENDSUSPFLAG", nullable=false, length=1)
private boolean sendSuspFlag;
public boolean getSendSuspFlag(){
return this.sendSuspFlag;
}
public void setSendSuspFlag(boolean sendSuspFlag){
this.sendSuspFlag = sendSuspFlag;
}

public static final String RECEIVE_SUSP_FLAG = "receiveSuspFlag";
@Column(name="RECEIVESUSPFLAG", nullable=false, length=1)
private boolean receiveSuspFlag;
public boolean getReceiveSuspFlag(){
return this.receiveSuspFlag;
}
public void setReceiveSuspFlag(boolean receiveSuspFlag){
this.receiveSuspFlag = receiveSuspFlag;
}

public static final String EFFECTIVE_DT = "effectiveDt";
@Column(name="EFFECTIVEDT", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date effectiveDt;
public java.util.Date getEffectiveDt(){
return this.effectiveDt;
}
public void setEffectiveDt(java.util.Date effectiveDt){
this.effectiveDt = effectiveDt;
}

public static final String SUSP_NOTE = "suspNote";
@Column(name="SUSPNOTE", nullable=false, length=1024)
private String suspNote;
public String getSuspNote(){
return this.suspNote;
}
public void setSuspNote(String suspNote){
this.suspNote = suspNote;
}

public static final String CURR_SEND_SUSP_FLAG = "currSendSuspFlag";
@Column(name="CURRSENDSUSPFLAG", nullable=true, length=1)
@Transient
private boolean currSendSuspFlag;
public boolean getCurrSendSuspFlag(){
return this.currSendSuspFlag;
}
public void setCurrSendSuspFlag(boolean currSendSuspFlag){
this.currSendSuspFlag = currSendSuspFlag;
}

public static final String CURR_RECEIVE_SUSP_FLAG = "currReceiveSuspFlag";
@Column(name="CURRRECEIVESUSPFLAG", nullable=true, length=1)
@Transient
private boolean currReceiveSuspFlag;
public boolean getCurrReceiveSuspFlag(){
return this.currReceiveSuspFlag;
}
public void setCurrReceiveSuspFlag(boolean currReceiveSuspFlag){
this.currReceiveSuspFlag = currReceiveSuspFlag;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "prtSuspensionsParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

@Override
public String toString() {
return "ATSPRT_Suspension [id= " + getId() + ", sendSuspFlag= " + getSendSuspFlag() + ", receiveSuspFlag= " + getReceiveSuspFlag() + ", effectiveDt= " + getEffectiveDt() + ", suspNote= " + getSuspNote() + ", currSendSuspFlag= " + getCurrSendSuspFlag() + ", currReceiveSuspFlag= " + getCurrReceiveSuspFlag() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getEffectiveDt() == null) ? 0 : getEffectiveDt().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_Suspension other = (ATSPRT_Suspension) obj;
return this.hashCode() == other.hashCode();}
}


}