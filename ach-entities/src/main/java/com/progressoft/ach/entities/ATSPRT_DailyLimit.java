package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_DailyLimits",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"PARTICIPANTID","SESSIONID","SETTLEMENTDT","CURRENCYID","Z_TENANT_ID"})
})
@XmlRootElement(name="PRT_DailyLimits")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_DailyLimit extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_DailyLimit(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String SETTLEMENT_DT = "settlementDt";
@Column(name="SETTLEMENTDT", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date settlementDt;
public java.util.Date getSettlementDt(){
return this.settlementDt;
}
public void setSettlementDt(java.util.Date settlementDt){
this.settlementDt = settlementDt;
}

public static final String DBATCHCOUNT = "dbatchcount";
@Column(name="DBATCHCOUNT", nullable=false, length=10)
private long dbatchcount;
public long getDbatchcount(){
return this.dbatchcount;
}
public void setDbatchcount(long dbatchcount){
this.dbatchcount = dbatchcount;
}

public static final String DBATCHLIMIT = "dbatchlimit";
@Column(name="DBATCHLIMIT", nullable=false, length=10)
private long dbatchlimit;
public long getDbatchlimit(){
return this.dbatchlimit;
}
public void setDbatchlimit(long dbatchlimit){
this.dbatchlimit = dbatchlimit;
}

public static final String CBATCHCOUNT = "cbatchcount";
@Column(name="CBATCHCOUNT", nullable=false, length=10)
private long cbatchcount;
public long getCbatchcount(){
return this.cbatchcount;
}
public void setCbatchcount(long cbatchcount){
this.cbatchcount = cbatchcount;
}

public static final String CBATCHLIMIT = "cbatchlimit";
@Column(name="CBATCHLIMIT", nullable=false, length=10)
private long cbatchlimit;
public long getCbatchlimit(){
return this.cbatchlimit;
}
public void setCbatchlimit(long cbatchlimit){
this.cbatchlimit = cbatchlimit;
}

public static final String CTXLIMIT = "ctxlimit";
@Column(name="CTXLIMIT", nullable=false, length=10)
private long ctxlimit;
public long getCtxlimit(){
return this.ctxlimit;
}
public void setCtxlimit(long ctxlimit){
this.ctxlimit = ctxlimit;
}

public static final String DTXLIMIT = "dtxlimit";
@Column(name="DTXLIMIT", nullable=false, length=10)
private long dtxlimit;
public long getDtxlimit(){
return this.dtxlimit;
}
public void setDtxlimit(long dtxlimit){
this.dtxlimit = dtxlimit;
}

public static final String CTXCOUNT = "ctxcount";
@Column(name="CTXCOUNT", nullable=false, length=10)
private long ctxcount;
public long getCtxcount(){
return this.ctxcount;
}
public void setCtxcount(long ctxcount){
this.ctxcount = ctxcount;
}

public static final String DTXCOUNT = "dtxcount";
@Column(name="DTXCOUNT", nullable=false, length=10)
private long dtxcount;
public long getDtxcount(){
return this.dtxcount;
}
public void setDtxcount(long dtxcount){
this.dtxcount = dtxcount;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String SESSION = "session";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="SESSIONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
private ATSBDI_Session session;
public ATSBDI_Session getSession(){
    return this.session;
}

public void setSession(ATSBDI_Session session) {
    this.session = session;
}

public static final String CURRENCY = "currency";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name = "CURRENCYID", nullable = false)
@WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

@Override
public String toString() {
    return "ATSPRT_DailyLimit [id= " + getId() + ", settlementDt= " + getSettlementDt() + ", dbatchcount= " + getDbatchcount() + ", dbatchlimit= " + getDbatchlimit() + ", cbatchcount= " + getCbatchcount() + ", cbatchlimit= " + getCbatchlimit() + ", ctxlimit= " + getCtxlimit() + ", dtxlimit= " + getDtxlimit() + ", ctxcount= " + getCtxcount() + ", dtxcount= " + getDtxcount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
int Dbatchcount= new Long("null".equals(getDbatchcount() + "") ? 0 : getDbatchcount()).intValue();
result = prime * result + (int) (Dbatchcount ^ Dbatchcount >>> 32);
int Dbatchlimit= new Long("null".equals(getDbatchlimit() + "") ? 0 : getDbatchlimit()).intValue();
result = prime * result + (int) (Dbatchlimit ^ Dbatchlimit >>> 32);
int Cbatchcount= new Long("null".equals(getCbatchcount() + "") ? 0 : getCbatchcount()).intValue();
result = prime * result + (int) (Cbatchcount ^ Cbatchcount >>> 32);
int Cbatchlimit= new Long("null".equals(getCbatchlimit() + "") ? 0 : getCbatchlimit()).intValue();
result = prime * result + (int) (Cbatchlimit ^ Cbatchlimit >>> 32);
int Ctxlimit= new Long("null".equals(getCtxlimit() + "") ? 0 : getCtxlimit()).intValue();
result = prime * result + (int) (Ctxlimit ^ Ctxlimit >>> 32);
int Dtxlimit= new Long("null".equals(getDtxlimit() + "") ? 0 : getDtxlimit()).intValue();
result = prime * result + (int) (Dtxlimit ^ Dtxlimit >>> 32);
int Ctxcount= new Long("null".equals(getCtxcount() + "") ? 0 : getCtxcount()).intValue();
result = prime * result + (int) (Ctxcount ^ Ctxcount >>> 32);
int Dtxcount= new Long("null".equals(getDtxcount() + "") ? 0 : getDtxcount()).intValue();
result = prime * result + (int) (Dtxcount ^ Dtxcount >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_DailyLimit other = (ATSPRT_DailyLimit) obj;
return this.hashCode() == other.hashCode();}
}


}