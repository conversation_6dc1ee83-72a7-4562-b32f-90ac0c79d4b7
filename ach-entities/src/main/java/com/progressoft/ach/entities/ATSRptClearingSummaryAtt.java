package com.progressoft.ach.entities;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;
import com.progressoft.jfw.model.bussinessobject.attachments.AttachmentItem;

@Entity
@Table(name="ATSRptClearingSummaryATT")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRptClearingSummaryAtt extends AttachmentItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRptClearingSummaryAtt(){/*Default Constructor*/}


}