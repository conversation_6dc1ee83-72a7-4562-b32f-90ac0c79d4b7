package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name = "ATSBDI_Sessions",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"SESSIONSEQ", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "BDI_Sessions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_Session extends JFWLookableEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSBDI_Session() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String SESSION_SEQ = "sessionSeq";
    @Column(name = "SESSIONSEQ", nullable = true, length = 19)
    private String sessionSeq;

    public String getSessionSeq() {
        return this.sessionSeq;
    }

    public void setSessionSeq(String sessionSeq) {
        this.sessionSeq = sessionSeq;
    }

    public static final String OPEN_DT = "openDt";
    @Column(name = "OPENDT", nullable = false, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date openDt;

    public java.util.Date getOpenDt() {
        return this.openDt;
    }

    public void setOpenDt(java.util.Date openDt) {
        this.openDt = openDt;
    }

    public static final String BUSINESS_DT = "businessDt";
    @Column(name = "BUSINESSDT", nullable = false, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date businessDt;

    public java.util.Date getBusinessDt() {
        return this.businessDt;
    }

    public void setBusinessDt(java.util.Date businessDt) {
        this.businessDt = businessDt;
    }

    public static final String START_STAMP = "startStamp";
    @XmlJavaTypeAdapter(TimestampAdapter.class)
    @Column(name = "STARTSTAMP", nullable = true, length = 34)
    private java.sql.Timestamp startStamp;

    public java.sql.Timestamp getStartStamp() {
        return this.startStamp;
    }

    public void setStartStamp(java.sql.Timestamp startStamp) {
        this.startStamp = startStamp;
    }

    public static final String WEIGHT = "weight";
    @Column(name = "WEIGHT", nullable = true, length = 2)
    private long weight;

    public long getWeight() {
        return this.weight;
    }

    public void setWeight(long weight) {
        this.weight = weight;
    }

    public static final String RETURN_CREDIT_DURATION = "returnCreditDuration";
    @Column(name = "RETURNCREDITDURATION", length = 20)
    private long returnCreditDuration;

    public long getReturnCreditDuration() {
        return returnCreditDuration;
    }

    public void setReturnCreditDuration(long returnCreditDuration) {
        this.returnCreditDuration = returnCreditDuration;
    }

    public static final String RETURN_DEBIT_DURATION = "returnDebitDuration";
    @Column(name = "RETURNDEBITDURATION", length = 20)
    private long returnDebitDuration;

    public long getReturnDebitDuration() {
        return returnDebitDuration;
    }

    public void setReturnDebitDuration(long returnDebitDuration) {
        this.returnDebitDuration = returnDebitDuration;
    }

    public static final String REVERSAL_CREDIT_DURATION = "reversalCreditDuration";
    @Column(name = "REVERSALCREDITDURATION", length = 20)
    private long reversalCreditDuration;

    public long getReversalCreditDuration() {
        return reversalCreditDuration;
    }

    public void setReversalCreditDuration(long reversalCreditDuration) {
        this.reversalCreditDuration = reversalCreditDuration;
    }

    public static final String REVERSAL_DEBIT_DURATION = "reversalDebitDuration";
    @Column(name = "REVERSALDEBITDURATION", length = 20)
    private long reversalDebitDuration;

    public long getReversalDebitDuration() {
        return reversalDebitDuration;
    }

    public void setReversalDebitDuration(long reversalDebitDuration) {
        this.reversalDebitDuration = reversalDebitDuration;
    }

    public static final String CURR_PERIOD = "currPeriod";
    @Column(name = "CURRPERIOD", nullable = true, length = 128)
    private String currPeriod;

    public String getCurrPeriod() {
        return this.currPeriod;
    }

    public void setCurrPeriod(String currPeriod) {
        this.currPeriod = currPeriod;
    }

    public static final String PERIOD_START = "periodStart";
    @XmlJavaTypeAdapter(TimestampAdapter.class)
    @Column(name = "PERIODSTART", nullable = true, length = 34)
    private java.sql.Timestamp periodStart;

    public java.sql.Timestamp getPeriodStart() {
        return this.periodStart;
    }

    public void setPeriodStart(java.sql.Timestamp periodStart) {
        this.periodStart = periodStart;
    }

    public static final String PERIOD_END = "periodEnd";
    @XmlJavaTypeAdapter(TimestampAdapter.class)
    @Column(name = "PERIODEND", nullable = true, length = 34)
    private java.sql.Timestamp periodEnd;

    public java.sql.Timestamp getPeriodEnd() {
        return this.periodEnd;
    }

    public void setPeriodEnd(java.sql.Timestamp periodEnd) {
        this.periodEnd = periodEnd;
    }

    public static final String DETAILS = "details";
    @Column(name = "DETAILS", nullable = true, length = 4000)
    private String details;

    public String getDetails() {
        return this.details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public static final String READY_FLAG = "readyFlag";
    @Column(name = "READYFLAG", nullable = true, length = 1)
    private boolean readyFlag;

    public boolean getReadyFlag() {
        return this.readyFlag;
    }

    public void setReadyFlag(boolean readyFlag) {
        this.readyFlag = readyFlag;
    }

    public static final String SETT_RETRY = "settRetry";
    @Column(name = "SETTRETRY", nullable = true, length = 10)
    private long settRetry;

    public long getSettRetry() {
        return this.settRetry;
    }

    public void setSettRetry(long settRetry) {
        this.settRetry = settRetry;
    }

    public static final String EXTENSION_DURATION = "extensionDuration";
    @Column(name = "EXTENSIONDURATION", nullable = true, length = 5)
    private long extensionDuration;

    public long getExtensionDuration() {
        return this.extensionDuration;
    }

    public void setExtensionDuration(long extensionDuration) {
        this.extensionDuration = extensionDuration;
    }

    public static final String PRIORITY_VALUE = "priorityValue";
    @Column(name = "PRIORITYVALUE", nullable = true, length = 6)
    private String priorityValue;

    public String getPriorityValue() {
        return this.priorityValue;
    }

    public void setPriorityValue(String priorityValue) {
        this.priorityValue = priorityValue;
    }

    public static final String IS_SPECIAL_CLEARING = "isSpecialClearing";
    @Column(name = "ISSPECIALCLEARING", nullable = true, length = 1)
    private boolean isSpecialClearing;

    public boolean getIsSpecialClearing() {
        return this.isSpecialClearing;
    }

    public void setIsSpecialClearing(boolean isSpecialClearing) {
        this.isSpecialClearing = isSpecialClearing;
    }

    public static final String TEMPLATE = "template";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "TEMPLATEID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "templateProvider", keyProperty = "id")
    private ATSBDS_Template template;

    public ATSBDS_Template getTemplate() {
        return this.template;
    }

    public void setTemplate(ATSBDS_Template template) {
        this.template = template;
    }

    public static final String REF_SESSION = "refSession";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REFSESSIONID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "bdsSessionProvider", keyProperty = "id")
    private ATSBDS_Session refSession;

    public ATSBDS_Session getRefSession() {
        return this.refSession;
    }

    public void setRefSession(ATSBDS_Session refSession) {
        this.refSession = refSession;
    }

    public static final String REF_SESSION_A_C_C_BALANCES = "refSessionACC_Balances";
    @OneToMany(mappedBy = "refSession")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @WithValueProvider(jupiterValueProviderBean = "accBalanceProvider", keyProperty = "id")
    private List<ATSACC_Balance> refSessionACC_Balances;

    public List<ATSACC_Balance> getRefSessionACCBalances() {
        return this.refSessionACC_Balances;
    }

    public void setRefSessionACCBalances(List<ATSACC_Balance> refSessionACC_Balances) {
        this.refSessionACC_Balances = refSessionACC_Balances;
    }

    public static final String REF_SESSION_B_D_I_PERIODS = "refSessionBDI_Periods";
    @OneToMany(mappedBy = "refSession")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @WithValueProvider(jupiterValueProviderBean = "periodProvider", keyProperty = "id")
    private List<ATSBDI_Period> refSessionBDI_Periods;

    public List<ATSBDI_Period> getRefSessionBDIPeriods() {
        return this.refSessionBDI_Periods;
    }

    public void setRefSessionBDIPeriods(List<ATSBDI_Period> refSessionBDI_Periods) {
        this.refSessionBDI_Periods = refSessionBDI_Periods;
    }


    @Override
    public String toString() {
        return "ATSBDI_Session [id= " + getId() + ", sessionSeq= " + getSessionSeq() + ", openDt= " + getOpenDt() + ", businessDt= " + getBusinessDt() + ", startStamp= " + getStartStamp() + ", weight= " + getWeight() + ", returnCreditDuration= " + getReturnCreditDuration() + ", returnDebitDuration= " + getReturnDebitDuration() + ", reversalCreditDuration= " + getReversalCreditDuration() + ", reversalDebitDuration= " + getReversalDebitDuration() + ", currPeriod= " + getCurrPeriod() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", details= " + getDetails() + ", readyFlag= " + getReadyFlag() + ", settRetry= " + getSettRetry() + ", extensionDuration= " + getExtensionDuration() + ", priorityValue= " + getPriorityValue() + ", isSpecialClearing= " + getIsSpecialClearing() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getSessionSeq() == null) ? 0 : getSessionSeq().hashCode());
        result = prime * result + ((getOpenDt() == null) ? 0 : getOpenDt().hashCode());
        result = prime * result + ((getBusinessDt() == null) ? 0 : getBusinessDt().hashCode());
        int Weight = new Long("null".equals(getWeight() + "") ? 0 : getWeight()).intValue();
        result = prime * result + (int) (Weight ^ Weight >>> 32);
        int ReturnCreditDuration = new Long("null".equals(getReturnCreditDuration() + "") ? 0 : getReturnCreditDuration()).intValue();
        result = prime * result + (int) (ReturnCreditDuration ^ ReturnCreditDuration >>> 32);
        int ReturnDebitDuration = new Long("null".equals(getReturnDebitDuration() + "") ? 0 : getReturnDebitDuration()).intValue();
        result = prime * result + (int) (ReturnDebitDuration ^ ReturnDebitDuration >>> 32);
        int ReversalCreditDuration = new Long("null".equals(getReversalCreditDuration() + "") ? 0 : getReversalCreditDuration()).intValue();
        result = prime * result + (int) (ReversalCreditDuration ^ ReversalCreditDuration >>> 32);
        int ReversalDebitDuration = new Long("null".equals(getReversalDebitDuration() + "") ? 0 : getReversalDebitDuration()).intValue();
        result = prime * result + (int) (ReversalDebitDuration ^ ReversalDebitDuration >>> 32);
        result = prime * result + ((getCurrPeriod() == null) ? 0 : getCurrPeriod().hashCode());
        result = prime * result + ((getDetails() == null) ? 0 : getDetails().hashCode());
        int SettRetry = new Long("null".equals(getSettRetry() + "") ? 0 : getSettRetry()).intValue();
        result = prime * result + (int) (SettRetry ^ SettRetry >>> 32);
        int ExtensionDuration = new Long("null".equals(getExtensionDuration() + "") ? 0 : getExtensionDuration()).intValue();
        result = prime * result + (int) (ExtensionDuration ^ ExtensionDuration >>> 32);
        result = prime * result + ((getPriorityValue() == null) ? 0 : getPriorityValue().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSBDI_Session other = (ATSBDI_Session) obj;
            return this.hashCode() == other.hashCode();
    }
}

}