package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRptAnalystQuery")
@XmlRootElement(name="RptAnalystQuery")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSRptAnalystQuery extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRptAnalystQuery(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String FROM_DATE = "fromDate";
@Column(name="FROMDATE", nullable=false, length=30)
@Temporal(TemporalType.DATE)
private java.util.Date fromDate;
public java.util.Date getFromDate(){
return this.fromDate;
}
public void setFromDate(java.util.Date fromDate){
this.fromDate = fromDate;
}

public static final String TO_DATE = "toDate";
@Column(name="TODATE", nullable=false, length=30)
@Temporal(TemporalType.DATE)
private java.util.Date toDate;
public java.util.Date getToDate(){
return this.toDate;
}
public void setToDate(java.util.Date toDate){
this.toDate = toDate;
}

public static final String INSTRUCTING = "instructing";
@Column(name="INSTRUCTING", nullable=true, length=256)
@Transient
private String instructing;
public String getInstructing(){
return this.instructing;
}
public void setInstructing(String instructing){
this.instructing = instructing;
}

public static final String INSTRUCTED = "instructed";
@Column(name="INSTRUCTED", nullable=true, length=256)
@Transient
private String instructed;
public String getInstructed(){
return this.instructed;
}
public void setInstructed(String instructed){
this.instructed = instructed;
}

public static final String TX_ID = "txId";
@Column(name="TXID", nullable=true, length=256)
@Transient
private String txId;
public String getTxId(){
return this.txId;
}
public void setTxId(String txId){
this.txId = txId;
}

public static final String BATCH_ID = "batchId";
@Column(name="BATCHID", nullable=true, length=256)
@Transient
private String batchId;
public String getBatchId(){
return this.batchId;
}
public void setBatchId(String batchId){
this.batchId = batchId;
}

public static final String MESSAGE_TYPE = "messageType";
@Column(name="MESSAGETYPE", nullable=true, length=256)
@Transient
private String messageType;
public String getMessageType(){
return this.messageType;
}
public void setMessageType(String messageType){
this.messageType = messageType;
}

public static final String TRN_TYPE_CODE = "trnTypeCode";
@Column(name="TRNTYPECODE", nullable=true, length=256)
@Transient
private String trnTypeCode;
public String getTrnTypeCode(){
return this.trnTypeCode;
}
public void setTrnTypeCode(String trnTypeCode){
this.trnTypeCode = trnTypeCode;
}

public static final String SETTLEMENT_DATE = "settlementDate";
@Column(name="SETTLEMENTDATE", nullable=true, length=256)
@Transient
private String settlementDate;
public String getSettlementDate(){
return this.settlementDate;
}
public void setSettlementDate(String settlementDate){
this.settlementDate = settlementDate;
}

public static final String STATUS = "status";
@Column(name="STATUS", nullable=true, length=256)
@Transient
private String status;
public String getStatus(){
return this.status;
}
public void setStatus(String status){
this.status = status;
}

public static final String AMOUNT = "amount";
@Column(name="AMOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal amount;
public java.math.BigDecimal getAmount(){
return this.amount;
}
public void setAmount(java.math.BigDecimal amount){
this.amount = amount;
}

public static final String TOTAL_TX = "totalTx";
@Column(name="TOTALTX", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal totalTx;
public java.math.BigDecimal getTotalTx(){
return this.totalTx;
}
public void setTotalTx(java.math.BigDecimal totalTx){
this.totalTx = totalTx;
}

public static final String TOTAL_BCH = "totalBch";
@Column(name="TOTALBCH", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal totalBch;
public java.math.BigDecimal getTotalBch(){
return this.totalBch;
}
public void setTotalBch(java.math.BigDecimal totalBch){
this.totalBch = totalBch;
}

public static final String TOTAL_AMOUNT = "totalAmount";
@Column(name="TOTALAMOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal totalAmount;
public java.math.BigDecimal getTotalAmount(){
return this.totalAmount;
}
public void setTotalAmount(java.math.BigDecimal totalAmount){
this.totalAmount = totalAmount;
}

public static final String BANK = "bank";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="BANKID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant bank;
public ATSPRT_Participant getBank(){
return this.bank;
}
public void setBank(ATSPRT_Participant bank){
this.bank = bank;
}

@Override
public String toString() {
return "ATSRptAnalystQuery [id= " + getId() + ", fromDate= " + getFromDate() + ", toDate= " + getToDate() + ", instructing= " + getInstructing() + ", instructed= " + getInstructed() + ", txId= " + getTxId() + ", batchId= " + getBatchId() + ", messageType= " + getMessageType() + ", trnTypeCode= " + getTrnTypeCode() + ", settlementDate= " + getSettlementDate() + ", status= " + getStatus() + ", amount= " + getAmount() + ", totalTx= " + getTotalTx() + ", totalBch= " + getTotalBch() + ", totalAmount= " + getTotalAmount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getFromDate() == null) ? 0 : getFromDate().hashCode());
result = prime * result + ((getToDate() == null) ? 0 : getToDate().hashCode());
result = prime * result + ((getInstructing() == null) ? 0 : getInstructing().hashCode());
result = prime * result + ((getInstructed() == null) ? 0 : getInstructed().hashCode());
result = prime * result + ((getTxId() == null) ? 0 : getTxId().hashCode());
result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
result = prime * result + ((getMessageType() == null) ? 0 : getMessageType().hashCode());
result = prime * result + ((getTrnTypeCode() == null) ? 0 : getTrnTypeCode().hashCode());
result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRptAnalystQuery other = (ATSRptAnalystQuery) obj;
return this.hashCode() == other.hashCode();}
}


}