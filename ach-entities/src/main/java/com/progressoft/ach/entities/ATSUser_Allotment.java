package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.security.User;
import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSUser_Allotment")
@XmlRootElement(name="ATSUser_Allotment")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE )
@Searchable({
        @Searchable.SearchableField(fieldPath = "user.name", label = "User", searchType = SearchType.LIKE)
})
public class ATSUser_Allotment extends JFWEntity implements Serializable {

    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;

    @Column(name="USERID", nullable=false)
    @WithValueProvider(jupiterValueProviderBean = "userAllotmentUsersProvider")
    private String user;


    @OneToMany(mappedBy = "userAllotment")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSUser_AllotmentSub> allotmentSubs;

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public List<ATSUser_AllotmentSub> getAllotmentSubs() {
        return allotmentSubs;
    }

    public void setAllotmentSubs(List<ATSUser_AllotmentSub> allotmentSubs) {
        this.allotmentSubs = allotmentSubs;
    }
}
