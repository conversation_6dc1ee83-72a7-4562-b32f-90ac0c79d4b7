package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSLKP_MandateReasons",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"})
})
@XmlRootElement(name="LKP_MandateReasons")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSLKP_MandateReason extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSLKP_MandateReason(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String IS_INITATION_REPLY = "isInitationReply";
@Column(name="ISINITATIONREPLY", nullable=true, length=10)
private boolean isInitationReply;
public boolean getIsInitationReply(){
return this.isInitationReply;
}
public void setIsInitationReply(boolean isInitationReply){
this.isInitationReply = isInitationReply;
}

public static final String IS_TERMINIATION_REQUEST = "isTerminiationRequest";
@Column(name="ISTERMINIATIONREQUEST", nullable=true, length=10)
private boolean isTerminiationRequest;
public boolean getIsTerminiationRequest(){
return this.isTerminiationRequest;
}
public void setIsTerminiationRequest(boolean isTerminiationRequest){
this.isTerminiationRequest = isTerminiationRequest;
}

public static final String IS_TERMINIATION_REPLY = "isTerminiationReply";
@Column(name="ISTERMINIATIONREPLY", nullable=true, length=10)
private boolean isTerminiationReply;
public boolean getIsTerminiationReply(){
return this.isTerminiationReply;
}
public void setIsTerminiationReply(boolean isTerminiationReply){
this.isTerminiationReply = isTerminiationReply;
}

public static final String IS_AMENDMENT_REQUEST = "isAmendmentRequest";
@Column(name="ISAMENDMENTREQUEST", nullable=true, length=10)
private boolean isAmendmentRequest;
public boolean getIsAmendmentRequest(){
return this.isAmendmentRequest;
}
public void setIsAmendmentRequest(boolean isAmendmentRequest){
this.isAmendmentRequest = isAmendmentRequest;
}

public static final String IS_AMENDMENT_REPLY = "isAmendmentReply";
@Column(name="ISAMENDMENTREPLY", nullable=true, length=10)
private boolean isAmendmentReply;
public boolean getIsAmendmentReply(){
return this.isAmendmentReply;
}
public void setIsAmendmentReply(boolean isAmendmentReply){
this.isAmendmentReply = isAmendmentReply;
}

public static final String IS_C_P_AMENDMENT_REQUEST = "isCPAmendmentRequest";
@Column(name="ISCPAMENDMENTREQUEST", nullable=true, length=10)
private boolean isCPAmendmentRequest;
public boolean getIsCPAmendmentRequest(){
return this.isCPAmendmentRequest;
}
public void setIsCPAmendmentRequest(boolean isCPAmendmentRequest){
this.isCPAmendmentRequest = isCPAmendmentRequest;
}

public static final String IS_C_P_AMENDMENT_REPLY = "isCPAmendmentReply";
@Column(name="ISCPAMENDMENTREPLY", nullable=true, length=10)
private boolean isCPAmendmentReply;
public boolean getIsCPAmendmentReply(){
return this.isCPAmendmentReply;
}
public void setIsCPAmendmentReply(boolean isCPAmendmentReply){
this.isCPAmendmentReply = isCPAmendmentReply;
}

public static final String IS_C_P_STOP_REQUEST = "isCPStopRequest";
@Column(name="ISCPSTOPREQUEST", nullable=true, length=10)
private boolean isCPStopRequest;
public boolean getIsCPStopRequest(){
return this.isCPStopRequest;
}
public void setIsCPStopRequest(boolean isCPStopRequest){
this.isCPStopRequest = isCPStopRequest;
}

public static final String IS_C_P_STOP_REPLY = "isCPStopReply";
@Column(name="ISCPSTOPREPLY", nullable=true, length=10)
private boolean isCPStopReply;
public boolean getIsCPStopReply(){
return this.isCPStopReply;
}
public void setIsCPStopReply(boolean isCPStopReply){
this.isCPStopReply = isCPStopReply;
}

public static final String IS_SYSTEM = "isSystem";
@Column(name="ISSYSTEM", nullable=true, length=10)
private boolean isSystem;
public boolean getIsSystem(){
return this.isSystem;
}
public void setIsSystem(boolean isSystem){
this.isSystem = isSystem;
}

public static final String AUTO_REJECT_REASON_ID_MNDT_POLICY_AUTO_REPLY = "autoRejectReasonIdMndt_Policy_AutoReply";
@OneToMany(mappedBy = "autoRejectReasonId")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSMndt_Policy_AutoReply> autoRejectReasonIdMndt_Policy_AutoReply;
public List<ATSMndt_Policy_AutoReply> getAutoRejectReasonIdMndtPolicyAutoReply(){
return this.autoRejectReasonIdMndt_Policy_AutoReply;
}
public void setAutoRejectReasonIdMndtPolicyAutoReply(List<ATSMndt_Policy_AutoReply> autoRejectReasonIdMndt_Policy_AutoReply){
this.autoRejectReasonIdMndt_Policy_AutoReply = autoRejectReasonIdMndt_Policy_AutoReply;
}

@Override
public String toString() {
return "ATSLKP_MandateReason [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", isInitationReply= " + getIsInitationReply() + ", isTerminiationRequest= " + getIsTerminiationRequest() + ", isTerminiationReply= " + getIsTerminiationReply() + ", isAmendmentRequest= " + getIsAmendmentRequest() + ", isAmendmentReply= " + getIsAmendmentReply() + ", isCPAmendmentRequest= " + getIsCPAmendmentRequest() + ", isCPAmendmentReply= " + getIsCPAmendmentReply() + ", isCPStopRequest= " + getIsCPStopRequest() + ", isCPStopReply= " + getIsCPStopReply() + ", isSystem= " + getIsSystem() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSLKP_MandateReason other = (ATSLKP_MandateReason) obj;
return this.hashCode() == other.hashCode();}
}


}