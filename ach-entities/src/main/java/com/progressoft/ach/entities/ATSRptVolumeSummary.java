package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRptVolumeSummary")
@XmlRootElement(name="RptVolumeSummary")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSRptVolumeSummary extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRptVolumeSummary(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@SequenceGenerator(name = "SEQ_ATSRptVolumeSummary", sequenceName = "SEQ_ATSRptVolumeSummary", allocationSize = 1)
@GeneratedValue(strategy = GenerationType.SEQUENCE, generator="SEQ_ATSRptVolumeSummary")
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String FROM_DATE = "fromDate";
@Column(name="FROMDATE", nullable=true, length=20)
@Temporal(TemporalType.DATE)
private java.util.Date fromDate;
public java.util.Date getFromDate(){
return this.fromDate;
}
public void setFromDate(java.util.Date fromDate){
this.fromDate = fromDate;
}

public static final String TO_DATE = "toDate";
@Column(name="TODATE", nullable=true, length=20)
@Temporal(TemporalType.DATE)
private java.util.Date toDate;
public java.util.Date getToDate(){
return this.toDate;
}
public void setToDate(java.util.Date toDate){
this.toDate = toDate;
}

public static final String SESSION_SEQ = "sessionSeq";
@Column(name="SESSIONSEQ", nullable=true, length=19)
private long sessionSeq;
public long getSessionSeq(){
return this.sessionSeq;
}
public void setSessionSeq(long sessionSeq){
this.sessionSeq = sessionSeq;
}

public static final String TRANSACTION_STATUS = "transactionStatus";
@Column(name="TRANSACTIONSTATUS", nullable=false, length=20)
private String transactionStatus;
public String getTransactionStatus(){
return this.transactionStatus;
}
public void setTransactionStatus(String transactionStatus){
this.transactionStatus = transactionStatus;
}

public static final String AMOUNT_FROM = "amountFrom";
@Column(name="AMOUNTFROM", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal amountFrom;
public java.math.BigDecimal getAmountFrom(){
return this.amountFrom;
}
public void setAmountFrom(java.math.BigDecimal amountFrom){
this.amountFrom = amountFrom;
}

public static final String AMOUNT_TO = "amountTo";
@Column(name="AMOUNTTO", nullable=true, precision=14, scale=5, length=20)
private java.math.BigDecimal amountTo;
public java.math.BigDecimal getAmountTo(){
return this.amountTo;
}
public void setAmountTo(java.math.BigDecimal amountTo){
this.amountTo = amountTo;
}

public static final String DIRECTION = "direction";
@Column(name="DIRECTION", nullable=false, length=20)
private String direction;
public String getDirection(){
return this.direction;
}
public void setDirection(String direction){
this.direction = direction;
}

public static final String MESSAGE_TYPE = "messageType";
@Column(name="MESSAGETYPE", nullable=false, length=20)
private String messageType;
public String getMessageType(){
return this.messageType;
}
public void setMessageType(String messageType){
this.messageType = messageType;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "rejectionReasonProvider", keyProperty = "id")
private ATSMSG_Reason rejectionReason;
public ATSMSG_Reason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSMSG_Reason rejectionReason){
this.rejectionReason = rejectionReason;
}

public static final String CATEGORY_PURPOSE = "categoryPurpose";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CATEGORYPURPOSEID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private ATSMSG_CtgPurp categoryPurpose;
public ATSMSG_CtgPurp getCategoryPurpose(){
    return this.categoryPurpose;
}

    public void setCategoryPurpose(ATSMSG_CtgPurp categoryPurpose) {
        this.categoryPurpose = categoryPurpose;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = true)
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;
    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }
    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "ATSRptVolumeSummary [id= " + getId() + ", fromDate= " + getFromDate() + ", toDate= " + getToDate() + ", sessionSeq= " + getSessionSeq() + ", transactionStatus= " + getTransactionStatus() + ", amountFrom= " + getAmountFrom() + ", amountTo= " + getAmountTo() + ", direction= " + getDirection() + ", messageType= " + getMessageType() + "]";
    }

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getFromDate() == null) ? 0 : getFromDate().hashCode());
result = prime * result + ((getToDate() == null) ? 0 : getToDate().hashCode());
int SessionSeq= new Long("null".equals(getSessionSeq() + "") ? 0 : getSessionSeq()).intValue();
result = prime * result + (int) (SessionSeq ^ SessionSeq >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRptVolumeSummary other = (ATSRptVolumeSummary) obj;
return this.hashCode() == other.hashCode();}
}


}