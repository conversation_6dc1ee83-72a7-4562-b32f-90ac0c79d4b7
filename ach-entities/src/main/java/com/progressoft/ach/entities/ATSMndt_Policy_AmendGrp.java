package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;

import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name="ATSMndt_Policy_AmendGRP")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMndt_Policy_AmendGrp extends ChangeGroup implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_AmendGrp(){/*Default Constructor*/}

@OneToMany(mappedBy="changeGroup")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private Set<ATSMndt_Policy_AmendItm> changeItems;

public void setChangeItems(Set<ATSMndt_Policy_AmendItm> changeItems) {
this.changeItems = changeItems;
}

@Override
public Set<? extends ChangeItem> getChangeItems() {
return this.changeItems;
}


}