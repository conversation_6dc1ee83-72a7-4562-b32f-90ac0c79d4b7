package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ACH_INTG_TRNS",
uniqueConstraints=
{
@UniqueConstraint(columnNames={"BTCH_ID","TRNS_ID","TRNS_SEQ","Z_TENANT_ID"})
})
@XmlRootElement(name="INTG_TRNS")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ACH_INTG_TRN extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ACH_INTG_TRN(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="BTCH_ID", nullable=true, length=35)
private String BTCH_ID;
public String getBTCHID(){
return this.BTCH_ID;
}
public void setBTCHID(String BTCH_ID){
this.BTCH_ID = BTCH_ID;
}

@Column(name="TRNS_ID", nullable=true, length=35)
private String TRNS_ID;
public String getTRNSID(){
return this.TRNS_ID;
}
public void setTRNSID(String TRNS_ID){
this.TRNS_ID = TRNS_ID;
}

@Column(name="TRNS_SEQ", nullable=true, length=16)
private String TRNS_SEQ;
public String getTRNSSEQ(){
return this.TRNS_SEQ;
}
public void setTRNSSEQ(String TRNS_SEQ){
this.TRNS_SEQ = TRNS_SEQ;
}

@Column(name="ORGNL_TRNS_ID", nullable=true, length=35)
private String ORGNL_TRNS_ID;
public String getORGNLTRNSID(){
return this.ORGNL_TRNS_ID;
}
public void setORGNLTRNSID(String ORGNL_TRNS_ID){
this.ORGNL_TRNS_ID = ORGNL_TRNS_ID;
}

@Column(name="AMOUNT", nullable=true, length=20)
private long AMOUNT;
public long getAMOUNT(){
return this.AMOUNT;
}
public void setAMOUNT(long AMOUNT){
this.AMOUNT = AMOUNT;
}

@Column(name="CURR_CD", nullable=true, length=16)
private String CURR_CD;
public String getCURRCD(){
return this.CURR_CD;
}
public void setCURRCD(String CURR_CD){
this.CURR_CD = CURR_CD;
}

@Column(name="CRDTR_AGNT_ID", nullable=true, length=16)
private String CRDTR_AGNT_ID;
public String getCRDTRAGNTID(){
return this.CRDTR_AGNT_ID;
}
public void setCRDTRAGNTID(String CRDTR_AGNT_ID){
this.CRDTR_AGNT_ID = CRDTR_AGNT_ID;
}

@Column(name="CRDTR_AGNT_ACC", nullable=true, length=16)
private String CRDTR_AGNT_ACC;
public String getCRDTRAGNTACC(){
return this.CRDTR_AGNT_ACC;
}
public void setCRDTRAGNTACC(String CRDTR_AGNT_ACC){
this.CRDTR_AGNT_ACC = CRDTR_AGNT_ACC;
}

@Column(name="DBTR_AGNT_ID", nullable=true, length=16)
private String DBTR_AGNT_ID;
public String getDBTRAGNTID(){
return this.DBTR_AGNT_ID;
}
public void setDBTRAGNTID(String DBTR_AGNT_ID){
this.DBTR_AGNT_ID = DBTR_AGNT_ID;
}

@Column(name="DBTR_AGNT_ACC", nullable=true, length=35)
private String DBTR_AGNT_ACC;
public String getDBTRAGNTACC(){
return this.DBTR_AGNT_ACC;
}
public void setDBTRAGNTACC(String DBTR_AGNT_ACC){
this.DBTR_AGNT_ACC = DBTR_AGNT_ACC;
}

@Column(name="MSGTYP_CD", nullable=true, length=16)
private String MSGTYP_CD;
public String getMSGTYPCD(){
return this.MSGTYP_CD;
}
public void setMSGTYPCD(String MSGTYP_CD){
this.MSGTYP_CD = MSGTYP_CD;
}

@Column(name="STL_DATE", nullable=true, length=16)
@Temporal(TemporalType.DATE)
private java.util.Date STL_DATE;
public java.util.Date getSTLDATE(){
return this.STL_DATE;
}
public void setSTLDATE(java.util.Date STL_DATE){
this.STL_DATE = STL_DATE;
}

@Column(name="WNDO_ID", nullable=true, length=16)
private String WNDO_ID;
public String getWNDOID(){
return this.WNDO_ID;
}
public void setWNDOID(String WNDO_ID){
this.WNDO_ID = WNDO_ID;
}

@Column(name="EXCHANGE_AGNT_ID", nullable=true, length=35)
private String EXCHANGE_AGNT_ID;
public String getEXCHANGEAGNTID(){
return this.EXCHANGE_AGNT_ID;
}
public void setEXCHANGEAGNTID(String EXCHANGE_AGNT_ID){
this.EXCHANGE_AGNT_ID = EXCHANGE_AGNT_ID;
}

@Column(name="EXCHANGE_DT", nullable=true, length=16)
@Temporal(TemporalType.DATE)
private java.util.Date EXCHANGE_DT;
public java.util.Date getEXCHANGEDT(){
return this.EXCHANGE_DT;
}
public void setEXCHANGEDT(java.util.Date EXCHANGE_DT){
this.EXCHANGE_DT = EXCHANGE_DT;
}

@Column(name="VALID_FLAG", nullable=true, length=16)
private long VALID_FLAG;
public long getVALIDFLAG(){
return this.VALID_FLAG;
}
public void setVALIDFLAG(long VALID_FLAG){
this.VALID_FLAG = VALID_FLAG;
}

@Column(name="PROCESS_FLAG", nullable=true, length=16)
private long PROCESS_FLAG;
public long getPROCESSFLAG(){
return this.PROCESS_FLAG;
}
public void setPROCESSFLAG(long PROCESS_FLAG){
this.PROCESS_FLAG = PROCESS_FLAG;
}

@Column(name="CLRG_REF_NO", nullable=true, length=16)
private String CLRG_REF_NO;
public String getCLRGREFNO(){
return this.CLRG_REF_NO;
}
public void setCLRGREFNO(String CLRG_REF_NO){
this.CLRG_REF_NO = CLRG_REF_NO;
}

@Override
public String toString() {
return "ACH_INTG_TRN [id= " + getId() + ", BTCH_ID= " + getBTCHID() + ", TRNS_ID= " + getTRNSID() + ", TRNS_SEQ= " + getTRNSSEQ() + ", ORGNL_TRNS_ID= " + getORGNLTRNSID() + ", AMOUNT= " + getAMOUNT() + ", CURR_CD= " + getCURRCD() + ", CRDTR_AGNT_ID= " + getCRDTRAGNTID() + ", CRDTR_AGNT_ACC= " + getCRDTRAGNTACC() + ", DBTR_AGNT_ID= " + getDBTRAGNTID() + ", DBTR_AGNT_ACC= " + getDBTRAGNTACC() + ", MSGTYP_CD= " + getMSGTYPCD() + ", STL_DATE= " + getSTLDATE() + ", WNDO_ID= " + getWNDOID() + ", EXCHANGE_AGNT_ID= " + getEXCHANGEAGNTID() + ", EXCHANGE_DT= " + getEXCHANGEDT() + ", VALID_FLAG= " + getVALIDFLAG() + ", PROCESS_FLAG= " + getPROCESSFLAG() + ", CLRG_REF_NO= " + getCLRGREFNO() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getBTCHID() == null) ? 0 : getBTCHID().hashCode());
result = prime * result + ((getTRNSID() == null) ? 0 : getTRNSID().hashCode());
result = prime * result + ((getTRNSSEQ() == null) ? 0 : getTRNSSEQ().hashCode());
result = prime * result + ((getORGNLTRNSID() == null) ? 0 : getORGNLTRNSID().hashCode());
int AMOUNT= new Long("null".equals(getAMOUNT() + "") ? 0 : getAMOUNT()).intValue();
result = prime * result + (int) (AMOUNT ^ AMOUNT >>> 32);
result = prime * result + ((getCURRCD() == null) ? 0 : getCURRCD().hashCode());
result = prime * result + ((getCRDTRAGNTID() == null) ? 0 : getCRDTRAGNTID().hashCode());
result = prime * result + ((getCRDTRAGNTACC() == null) ? 0 : getCRDTRAGNTACC().hashCode());
result = prime * result + ((getDBTRAGNTID() == null) ? 0 : getDBTRAGNTID().hashCode());
result = prime * result + ((getDBTRAGNTACC() == null) ? 0 : getDBTRAGNTACC().hashCode());
result = prime * result + ((getMSGTYPCD() == null) ? 0 : getMSGTYPCD().hashCode());
result = prime * result + ((getSTLDATE() == null) ? 0 : getSTLDATE().hashCode());
result = prime * result + ((getWNDOID() == null) ? 0 : getWNDOID().hashCode());
result = prime * result + ((getEXCHANGEAGNTID() == null) ? 0 : getEXCHANGEAGNTID().hashCode());
result = prime * result + ((getEXCHANGEDT() == null) ? 0 : getEXCHANGEDT().hashCode());
int VALID_FLAG= new Long("null".equals(getVALIDFLAG() + "") ? 0 : getVALIDFLAG()).intValue();
result = prime * result + (int) (VALID_FLAG ^ VALID_FLAG >>> 32);
int PROCESS_FLAG= new Long("null".equals(getPROCESSFLAG() + "") ? 0 : getPROCESSFLAG()).intValue();
result = prime * result + (int) (PROCESS_FLAG ^ PROCESS_FLAG >>> 32);
result = prime * result + ((getCLRGREFNO() == null) ? 0 : getCLRGREFNO().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ACH_INTG_TRN other = (ACH_INTG_TRN) obj;
return this.hashCode() == other.hashCode();}
}


}