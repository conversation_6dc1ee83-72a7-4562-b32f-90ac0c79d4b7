package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name = "ATSPRT_SettParticipants",
        uniqueConstraints =
                {
                        @UniqueConstraint(columnNames = {"REFPARTICIPANTID", "SETTPARTICIPANTID", "Z_TENANT_ID"})
                })
@XmlRootElement(name = "PRT_SettParticipants")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_SettParticipant extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSPRT_SettParticipant() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String REF_PARTICIPANT = "refParticipant";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REFPARTICIPANTID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "refParticipantsProvider", keyProperty = "id")
    private ATSPRT_Participant refParticipant;

    public ATSPRT_Participant getRefParticipant() {
        return this.refParticipant;
    }

    public void setRefParticipant(ATSPRT_Participant refParticipant) {
        this.refParticipant = refParticipant;
    }

    public static final String SETT_PARTICIPANT = "settParticipant";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SETTPARTICIPANTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "settlementParticipantsProvider", keyProperty = "id")
    private ATSPRT_Participant settParticipant;

    public ATSPRT_Participant getSettParticipant() {
        return this.settParticipant;
    }

    public void setSettParticipant(ATSPRT_Participant settParticipant) {
        this.settParticipant = settParticipant;
    }

    @Override
    public String toString() {
        return "ATSPRT_SettParticipant [id= " + getId() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSPRT_SettParticipant other = (ATSPRT_SettParticipant) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}