package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import org.hibernate.annotations.Cascade;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static jakarta.persistence.CascadeType.*;
import static jakarta.persistence.CascadeType.MERGE;
import static jakarta.persistence.CascadeType.REFRESH;
import static jakarta.persistence.GenerationType.AUTO;
import static org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE;
import static org.hibernate.annotations.CascadeType.REPLICATE;

@Entity
@Table(name="ATSSubscriptionFees")
@XmlRootElement(name="SubscriptionFees")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = READ_WRITE )
public class ATSSubscriptionFee extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSSubscriptionFee(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy= AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String AMOUNT = "amount";
@Column(name="AMOUNT", nullable=false, precision=38, scale=20, length=16)
private BigDecimal amount;
public BigDecimal getAmount(){
return this.amount;
}
public void setAmount(BigDecimal amount){
this.amount = amount;
}

public static final String DISCOUNT = "discount";
@Column(name="DISCOUNT", precision=38, scale=20, length=16)
private BigDecimal discount;
public BigDecimal getDiscount(){
    return this.discount;
}
public void setDiscount(BigDecimal discount){
        this.discount = discount;
    }

public static final String CHARGE_CURRENCY = "chargeCurrency";
@ManyToOne
@Cascade(REPLICATE)
@JoinColumn(name="CHARGECURRENCYID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
private JFWCurrency chargeCurrency;
public JFWCurrency getChargeCurrency(){
    return this.chargeCurrency;
}
public void setChargeCurrency(JFWCurrency chargeCurrency){
    this.chargeCurrency = chargeCurrency;
}

public static final String LAST_CALCULATION_DATE = "lastCalculationDate";
@Column(name="LASTCALCULATIONDATE", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date lastCalculationDate;
public java.util.Date getLastCalculationDate(){
    return this.lastCalculationDate;
}
public void setLastCalculationDate(java.util.Date lastCalculationDate){
    this.lastCalculationDate = lastCalculationDate;
}

public static final String PARTICIPANTS = "participants";
@ManyToMany(cascade = { PERSIST, DETACH, REMOVE, REFRESH, MERGE })
@Cascade(REPLICATE)
@JoinTable(name="ATSSUBSCRIPTIONFEEPARTICIPANTS", joinColumns = @JoinColumn(name = "SUBSCRIPTIONFEES_ID"), inverseJoinColumns = @JoinColumn(name = "PARTICIPANTS_ID"))
@WithValueProvider(jupiterValueProviderBean = "feeParticipantsProvider", keyProperty = "id")
private List<ATSPRT_Participant> participants = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getParticipants(){
return this.participants;
}
public void setParticipants(List<ATSPRT_Participant> participants){
this.participants = participants;
}

@Override
public String toString() {
return "ATSSubscriptionFee [id= " + getId() + ", amount= " + getAmount() + ", discount= " + getDiscount() + ", chargeCurrency= " + getChargeCurrency()+ "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSSubscriptionFee other = (ATSSubscriptionFee) obj;
return this.hashCode() == other.hashCode();}
}

}