package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ACH_INTG_TRNS_RSNS",
uniqueConstraints=
{
@UniqueConstraint(columnNames={"RSN_CD","TRNS_ID","TRNS_SEQ","Z_TENANT_ID"})
})
@XmlRootElement(name="INTG_TRNS_RSNS")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ACH_INTG_TRNS_RSN extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ACH_INTG_TRNS_RSN(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="TRNS_ID", nullable=true, length=35)
private String TRNS_ID;
public String getTRNSID(){
return this.TRNS_ID;
}
public void setTRNSID(String TRNS_ID){
this.TRNS_ID = TRNS_ID;
}

@Column(name="TRNS_SEQ", nullable=true, length=16)
private String TRNS_SEQ;
public String getTRNSSEQ(){
return this.TRNS_SEQ;
}
public void setTRNSSEQ(String TRNS_SEQ){
this.TRNS_SEQ = TRNS_SEQ;
}

@Column(name="RSN_CD", nullable=true, length=16)
private String RSN_CD;
public String getRSNCD(){
return this.RSN_CD;
}
public void setRSNCD(String RSN_CD){
this.RSN_CD = RSN_CD;
}

@Column(name="NARRATIVE", nullable=true, length=4000)
private String NARRATIVE;
public String getNARRATIVE(){
return this.NARRATIVE;
}
public void setNARRATIVE(String NARRATIVE){
this.NARRATIVE = NARRATIVE;
}

@Override
public String toString() {
return "ACH_INTG_TRNS_RSN [id= " + getId() + ", TRNS_ID= " + getTRNSID() + ", TRNS_SEQ= " + getTRNSSEQ() + ", RSN_CD= " + getRSNCD() + ", NARRATIVE= " + getNARRATIVE() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getTRNSID() == null) ? 0 : getTRNSID().hashCode());
result = prime * result + ((getTRNSSEQ() == null) ? 0 : getTRNSSEQ().hashCode());
result = prime * result + ((getRSNCD() == null) ? 0 : getRSNCD().hashCode());
result = prime * result + ((getNARRATIVE() == null) ? 0 : getNARRATIVE().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ACH_INTG_TRNS_RSN other = (ACH_INTG_TRNS_RSN) obj;
return this.hashCode() == other.hashCode();}
}


}