package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSCapExceededActions",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CREDITORPARTICIPANTID","DEBTORPARTICIPANTID","Z_TENANT_ID"})
})
@XmlRootElement(name="CapExceededActions")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSCapExceededAction extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSCapExceededAction(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String ACTION = "action";
@Column(name="ACTION", nullable=true, length=20)
private String action;
public String getAction(){
return this.action;
}
public void setAction(String action){
this.action = action;
}

public static final String CREDITOR_PARTICIPANT = "creditorParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CREDITORPARTICIPANTID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "capsParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant creditorParticipant;
public ATSPRT_Participant getCreditorParticipant(){
return this.creditorParticipant;
}
public void setCreditorParticipant(ATSPRT_Participant creditorParticipant){
this.creditorParticipant = creditorParticipant;
}

public static final String DEBTOR_PARTICIPANT = "debtorParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="DEBTORPARTICIPANTID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "capsParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant debtorParticipant;
public ATSPRT_Participant getDebtorParticipant(){
return this.debtorParticipant;
}
public void setDebtorParticipant(ATSPRT_Participant debtorParticipant){
this.debtorParticipant = debtorParticipant;
}

@Override
public String toString() {
return "ATSCapExceededAction [id= " + getId() + ", action= " + getAction() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSCapExceededAction other = (ATSCapExceededAction) obj;
return this.hashCode() == other.hashCode();}
}


}