package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
	import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSTechPartCreationReqs",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"PREFIX","Z_TENANT_ID"})
})
@XmlRootElement(name="TechPartCreationReqs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSTechPartCreationReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSTechPartCreationReq(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_ID = "requestId";
@Column(name="REQUESTID", nullable=false, length=4000)
private String requestId;
public String getRequestId(){
return this.requestId;
}
public void setRequestId(String requestId){
this.requestId = requestId;
}

public static final String CODE = "code";
@Column(name="CODE", nullable=false, length=18)
private String code;
public String getCode(){
return this.code;
}
public void setCode(String code){
this.code = code;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=128)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=false, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String PREFIX = "prefix";
@Column(name="PREFIX", nullable=false, length=20)
private String prefix;
public String getPrefix(){
return this.prefix;
}
public void setPrefix(String prefix){
this.prefix = prefix;
}

public static final String HEAD_OFFICE_BRANCH = "headOfficeBranch";
@Column(name="HEADOFFICEBRANCH", nullable=false, length=3)
private String headOfficeBranch;
public String getHeadOfficeBranch(){
return this.headOfficeBranch;
}
public void setHeadOfficeBranch(String headOfficeBranch){
this.headOfficeBranch = headOfficeBranch;
}

public static final String ACCOUNT_NUMBER_LENGTH = "accountNumberLength";
@Column(name="ACCOUNTNUMBERLENGTH", nullable=false, length=2)
private long accountNumberLength;
public long getAccountNumberLength(){
return this.accountNumberLength;
}
public void setAccountNumberLength(long accountNumberLength){
this.accountNumberLength = accountNumberLength;
}

public static final String COUNTRY = "country";
@Column(name="COUNTRY", nullable=true, length=150)
private String country;
public String getCountry(){
return this.country;
}
public void setCountry(String country){
this.country = country;
}

public static final String CITY = "city";
@Column(name="CITY", nullable=true, length=150)
private String city;
public String getCity(){
return this.city;
}
public void setCity(String city){
this.city = city;
}

public static final String PHONE = "phone";
@Column(name="PHONE", nullable=true, length=32)
private String phone;
public String getPhone(){
return this.phone;
}
public void setPhone(String phone){
this.phone = phone;
}

public static final String EMAIL = "email";
@Column(name="EMAIL", nullable=true, length=128)
private String email;
public String getEmail(){
return this.email;
}
public void setEmail(String email){
this.email = email;
}

public static final String ADDRESS = "address";
@Column(name="ADDRESS", nullable=true, length=4000)
private String address;
public String getAddress(){
return this.address;
}
public void setAddress(String address){
this.address = address;
}

public static final String SEND_SUSP_FLAG = "sendSuspFlag";
@Column(name="SENDSUSPFLAG", nullable=true, length=1)
private boolean sendSuspFlag;
public boolean getSendSuspFlag(){
return this.sendSuspFlag;
}
public void setSendSuspFlag(boolean sendSuspFlag){
this.sendSuspFlag = sendSuspFlag;
}

public static final String RECEIVE_SUSP_FLAG = "receiveSuspFlag";
@Column(name="RECEIVESUSPFLAG", nullable=true, length=1)
private boolean receiveSuspFlag;
public boolean getReceiveSuspFlag(){
return this.receiveSuspFlag;
}
public void setReceiveSuspFlag(boolean receiveSuspFlag){
this.receiveSuspFlag = receiveSuspFlag;
}

public static final String REJECTION_NOTE = "rejectionNote";
@Column(name="REJECTIONNOTE", nullable=true, length=4000)
private String rejectionNote;
public String getRejectionNote(){
return this.rejectionNote;
}
public void setRejectionNote(String rejectionNote){
this.rejectionNote = rejectionNote;
}

public static final String REQUESTING_PARTICIPANT = "requestingParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTINGPARTICIPANTID", nullable=false)
private ATSPRT_Participant requestingParticipant;
public ATSPRT_Participant getRequestingParticipant(){
return this.requestingParticipant;
}
public void setRequestingParticipant(ATSPRT_Participant requestingParticipant){
this.requestingParticipant = requestingParticipant;
}

public static final String PARTICIPANT_TYPE = "participantType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTTYPEID", nullable=false)
private ATSPRT_Type participantType;
public ATSPRT_Type getParticipantType(){
return this.participantType;
}
public void setParticipantType(ATSPRT_Type participantType){
this.participantType = participantType;
}

public static final String INSTITUTION_TYPE = "institutionType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="INSTITUTIONTYPEID", nullable=false)
private ATSPRT_Institution institutionType;
public ATSPRT_Institution getInstitutionType(){
return this.institutionType;
}
public void setInstitutionType(ATSPRT_Institution institutionType){
this.institutionType = institutionType;
}

public static final String LIMITS_PROFILE = "limitsProfile";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="LIMITSPROFILEID", nullable=true)
private ATSPRT_LimitsProfile limitsProfile;
public ATSPRT_LimitsProfile getLimitsProfile(){
return this.limitsProfile;
}
public void setLimitsProfile(ATSPRT_LimitsProfile limitsProfile){
this.limitsProfile = limitsProfile;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "requestRReasonProvider", keyProperty = "id")
private ATSRequestRReason rejectionReason;
public ATSRequestRReason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSRequestRReason rejectionReason){
this.rejectionReason = rejectionReason;
}

@Override
public String toString() {
return "ATSTechPartCreationReq [id= " + getId() + ", requestId= " + getRequestId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", prefix= " + getPrefix() + ", headOfficeBranch= " + getHeadOfficeBranch() + ", accountNumberLength= " + getAccountNumberLength() + ", country= " + getCountry() + ", city= " + getCity() + ", phone= " + getPhone() + ", email= " + getEmail() + ", address= " + getAddress() + ", sendSuspFlag= " + getSendSuspFlag() + ", receiveSuspFlag= " + getReceiveSuspFlag() + ", rejectionNote= " + getRejectionNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getRequestId() == null) ? 0 : getRequestId().hashCode());
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
result = prime * result + ((getPrefix() == null) ? 0 : getPrefix().hashCode());
result = prime * result + ((getHeadOfficeBranch() == null) ? 0 : getHeadOfficeBranch().hashCode());
int AccountNumberLength= new Long("null".equals(getAccountNumberLength() + "") ? 0 : getAccountNumberLength()).intValue();
result = prime * result + (int) (AccountNumberLength ^ AccountNumberLength >>> 32);
result = prime * result + ((getCountry() == null) ? 0 : getCountry().hashCode());
result = prime * result + ((getCity() == null) ? 0 : getCity().hashCode());
result = prime * result + ((getPhone() == null) ? 0 : getPhone().hashCode());
result = prime * result + ((getEmail() == null) ? 0 : getEmail().hashCode());
result = prime * result + ((getRejectionNote() == null) ? 0 : getRejectionNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSTechPartCreationReq other = (ATSTechPartCreationReq) obj;
return this.hashCode() == other.hashCode();}
}


}