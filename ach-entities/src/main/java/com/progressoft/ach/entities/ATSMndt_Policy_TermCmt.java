package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.comments.CommentItem;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Entity
@Table(name="ATSMndt_Policy_TermCMT")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMndt_Policy_TermCmt extends CommentItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_TermCmt(){/*Default Constructor*/}


}