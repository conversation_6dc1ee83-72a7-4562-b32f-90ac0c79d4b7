package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;

@Entity

@Table(name="ATSBDI_WindowManagements")
@XmlRootElement(name="BDI_WindowManagements")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_WindowManagement extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_WindowManagement(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PERIOD_START = "periodStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODSTART", nullable=true, length=34)
private java.sql.Timestamp periodStart;
public java.sql.Timestamp getPeriodStart(){
return this.periodStart;
}
public void setPeriodStart(java.sql.Timestamp periodStart){
this.periodStart = periodStart;
}

public static final String PERIOD_END = "periodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODEND", nullable=true, length=34)
private java.sql.Timestamp periodEnd;
public java.sql.Timestamp getPeriodEnd(){
return this.periodEnd;
}
public void setPeriodEnd(java.sql.Timestamp periodEnd){
this.periodEnd = periodEnd;
}

public static final String ORIGIN_WINDOW_START = "originWindowStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="ORIGINWINDOWSTART", nullable=false, length=34)
private java.sql.Timestamp originWindowStart;
public java.sql.Timestamp getOriginWindowStart(){
return this.originWindowStart;
}
public void setOriginWindowStart(java.sql.Timestamp originWindowStart){
this.originWindowStart = originWindowStart;
}

public static final String ORIGIN_WINDOW_END = "originWindowEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="ORIGINWINDOWEND", nullable=false, length=34)
private java.sql.Timestamp originWindowEnd;
public java.sql.Timestamp getOriginWindowEnd(){
return this.originWindowEnd;
}
public void setOriginWindowEnd(java.sql.Timestamp originWindowEnd){
this.originWindowEnd = originWindowEnd;
}

public static final String ORIGIN_WINDOW_GRACE_PERIOD = "originWindowGracePeriod";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="ORIGINWINDOWGRACEPERIOD", nullable=true, length=34)
private java.sql.Timestamp originWindowGracePeriod;
public java.sql.Timestamp getOriginWindowGracePeriod(){
return this.originWindowGracePeriod;
}
public void setOriginWindowGracePeriod(java.sql.Timestamp originWindowGracePeriod){
this.originWindowGracePeriod = originWindowGracePeriod;
}

public static final String NEW_WINDOW_START = "newWindowStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="NEWWINDOWSTART", nullable=false, length=34)
private java.sql.Timestamp newWindowStart;
public java.sql.Timestamp getNewWindowStart(){
return this.newWindowStart;
}
public void setNewWindowStart(java.sql.Timestamp newWindowStart){
this.newWindowStart = newWindowStart;
}

public static final String NEW_WINDOW_END = "newWindowEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="NEWWINDOWEND", nullable=false, length=34)
private java.sql.Timestamp newWindowEnd;
public java.sql.Timestamp getNewWindowEnd(){
return this.newWindowEnd;
}
public void setNewWindowEnd(java.sql.Timestamp newWindowEnd){
this.newWindowEnd = newWindowEnd;
}

public static final String NEW_GRACE_PERIOD = "newGracePeriod";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="NEWGRACEPERIOD", nullable=false, length=34)
private java.sql.Timestamp newGracePeriod;
public java.sql.Timestamp getNewGracePeriod(){
return this.newGracePeriod;
}
public void setNewGracePeriod(java.sql.Timestamp newGracePeriod){
this.newGracePeriod = newGracePeriod;
}

public static final String REF_SESSION = "refSession";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowManagementSessionProvider", keyProperty = "id")
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDI_Session refSession){
this.refSession = refSession;
}

public static final String REF_PERIOD = "refPeriod";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPERIODID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowManagementPeriodProvider", keyProperty = "id")
private ATSBDI_Period refPeriod;
public ATSBDI_Period getRefPeriod(){
return this.refPeriod;
}
public void setRefPeriod(ATSBDI_Period refPeriod){
this.refPeriod = refPeriod;
}

public static final String REF_WINDOW = "refWindow";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFWINDOWID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowManagementWindowsProvider", keyProperty = "id")
private ATSBDI_Window refWindow;
public ATSBDI_Window getRefWindow(){
return this.refWindow;
}
public void setRefWindow(ATSBDI_Window refWindow){
this.refWindow = refWindow;
}

public static final String REQUEST_PARTICIPANT = "requestParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowAdditionParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant requestParticipant;
public ATSPRT_Participant getRequestParticipant(){
return this.requestParticipant;
}
public void setRequestParticipant(ATSPRT_Participant requestParticipant){
this.requestParticipant = requestParticipant;
}

@Override
public String toString() {
return "ATSBDI_WindowManagement [id= " + getId() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", originWindowStart= " + getOriginWindowStart() + ", originWindowEnd= " + getOriginWindowEnd() + ", originWindowGracePeriod= " + getOriginWindowGracePeriod() + ", newWindowStart= " + getNewWindowStart() + ", newWindowEnd= " + getNewWindowEnd() + ", newGracePeriod= " + getNewGracePeriod() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_WindowManagement other = (ATSBDI_WindowManagement) obj;
return this.hashCode() == other.hashCode();}
}


}