package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRpt_NCP_Details")
@XmlRootElement(name="Rpt_NCP_Details")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_NCP_Detail extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRpt_NCP_Detail(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String TOTAL_CREDIT = "totalCredit";
@Column(name="TOTALCREDIT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal totalCredit;
public java.math.BigDecimal getTotalCredit(){
return this.totalCredit;
}
public void setTotalCredit(java.math.BigDecimal totalCredit){
this.totalCredit = totalCredit;
}

public static final String TOTAL_DEBIT = "totalDebit";
@Column(name="TOTALDEBIT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal totalDebit;
public java.math.BigDecimal getTotalDebit(){
return this.totalDebit;
}
public void setTotalDebit(java.math.BigDecimal totalDebit){
this.totalDebit = totalDebit;
}

public static final String NET_AMOUNT = "netAmount";
@Column(name="NETAMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal netAmount;
public java.math.BigDecimal getNetAmount(){
return this.netAmount;
}
public void setNetAmount(java.math.BigDecimal netAmount){
this.netAmount = netAmount;
}

public static final String REF_N_C_P = "refNCP";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFNCPID", nullable=true)
private ATSRpt_NCP refNCP;
public ATSRpt_NCP getRefNCP(){
return this.refNCP;
}
public void setRefNCP(ATSRpt_NCP refNCP){
this.refNCP = refNCP;
}

public static final String AGENT = "agent";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="AGENTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant agent;
public ATSPRT_Participant getAgent(){
return this.agent;
}
public void setAgent(ATSPRT_Participant agent){
this.agent = agent;
}

@Override
public String toString() {
return "ATSRpt_NCP_Detail [id= " + getId() + ", totalCredit= " + getTotalCredit() + ", totalDebit= " + getTotalDebit() + ", netAmount= " + getNetAmount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRpt_NCP_Detail other = (ATSRpt_NCP_Detail) obj;
return this.hashCode() == other.hashCode();}
}


}