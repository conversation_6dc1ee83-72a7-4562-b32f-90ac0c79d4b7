package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMndt_Policy_CP_Stop",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"STOPINITIATEDBY","ORGMNDTTYPE","Z_TENANT_ID"})
})
@XmlRootElement(name="Mndt_Policy_CP_Stop")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMndt_Policy_CP_Stop extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_CP_Stop(){/*Default Constructor*/}

@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="STOPINITIATEDBY", nullable=false, length=1)
private String stopInitiatedBy;
public String getStopInitiatedBy(){
return this.stopInitiatedBy;
}
public void setStopInitiatedBy(String stopInitiatedBy){
this.stopInitiatedBy = stopInitiatedBy;
}

@Column(name="ORGMNDTTYPE", nullable=false, length=1)
private String orgMndtType;
public String getOrgMndtType(){
return this.orgMndtType;
}
public void setOrgMndtType(String orgMndtType){
this.orgMndtType = orgMndtType;
}

@Column(name="ISRECAPPROVALREQUIRED", nullable=true, length=1)
private boolean isRecApprovalRequired;
public boolean getIsRecApprovalRequired(){
return this.isRecApprovalRequired;
}
public void setIsRecApprovalRequired(boolean isRecApprovalRequired){
this.isRecApprovalRequired = isRecApprovalRequired;
}

@Column(name="POLICYDESC", nullable=true, length=1000)
private String policyDesc;
public String getPolicyDesc(){
return this.policyDesc;
}
public void setPolicyDesc(String policyDesc){
this.policyDesc = policyDesc;
}

@Override
public String toString() {
return "ATSMndt_Policy_CP_Stop [id= " + getId() + ", stopInitiatedBy= " + getStopInitiatedBy() + ", orgMndtType= " + getOrgMndtType() + ", isRecApprovalRequired= " + getIsRecApprovalRequired() + ", policyDesc= " + getPolicyDesc() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getPolicyDesc() == null) ? 0 : getPolicyDesc().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMndt_Policy_CP_Stop other = (ATSMndt_Policy_CP_Stop) obj;
return this.hashCode() == other.hashCode();}
}


}