package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSSpecClearingSet")
@XmlRootElement(name="SpecClearingSet")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSSpecClearingSet extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSSpecClearingSet(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=128)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String MAX_AMOUNT_THRESHOLD = "maxAmountThreshold";
@Column(name="MAXAMOUNTTHRESHOLD", nullable=false, length=6)
private long maxAmountThreshold;
public long getMaxAmountThreshold(){
return this.maxAmountThreshold;
}
public void setMaxAmountThreshold(long maxAmountThreshold){
this.maxAmountThreshold = maxAmountThreshold;
}

public static final String MIN_AMOUNT_THRESHOLD = "minAmountThreshold";
@Column(name="MINAMOUNTTHRESHOLD", nullable=false, length=6)
private long minAmountThreshold;
public long getMinAmountThreshold(){
return this.minAmountThreshold;
}
public void setMinAmountThreshold(long minAmountThreshold){
this.minAmountThreshold = minAmountThreshold;
}

public static final String CATEGORY_PURPOSE = "categoryPurpose";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CATEGORYPURPOSEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private ATSMSG_CtgPurp categoryPurpose;
public ATSMSG_CtgPurp getCategoryPurpose(){
return this.categoryPurpose;
}
public void setCategoryPurpose(ATSMSG_CtgPurp categoryPurpose){
this.categoryPurpose = categoryPurpose;
}

public static final String PARTICIPANTS = "participants";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSSpecClearingSetParticipants", joinColumns = @JoinColumn(name = "SPECCLEARINGSET_ID"), inverseJoinColumns = @JoinColumn(name = "PARTICIPANTS_ID"))
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private List<ATSPRT_Participant> participants = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getParticipants(){
return this.participants;
}
public void setParticipants(List<ATSPRT_Participant> participants){
this.participants = participants;
}

@Override
public String toString() {
return "ATSSpecClearingSet [id= " + getId() + ", name= " + getName() + ", description= " + getDescription() + ", maxAmountThreshold= " + getMaxAmountThreshold() + ", minAmountThreshold= " + getMinAmountThreshold() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int MaxAmountThreshold= new Long("null".equals(getMaxAmountThreshold() + "") ? 0 : getMaxAmountThreshold()).intValue();
result = prime * result + (int) (MaxAmountThreshold ^ MaxAmountThreshold >>> 32);
int MinAmountThreshold= new Long("null".equals(getMinAmountThreshold() + "") ? 0 : getMinAmountThreshold()).intValue();
result = prime * result + (int) (MinAmountThreshold ^ MinAmountThreshold >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSSpecClearingSet other = (ATSSpecClearingSet) obj;
return this.hashCode() == other.hashCode();}
}


}