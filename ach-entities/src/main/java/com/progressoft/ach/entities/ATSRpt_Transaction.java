package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name = "ATSRpt_Transactions")
@XmlRootElement(name = "Rpt_Transactions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_Transaction extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSRpt_Transaction() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public static final String SETTLEMENT_DT = "settlementDt";
    @Column(name = "SETTLEMENTDT", nullable = false, length = 34)
    @Temporal(TemporalType.DATE)
    private java.util.Date settlementDt;

    public java.util.Date getSettlementDt() {
        return this.settlementDt;
    }

    public void setSettlementDt(java.util.Date settlementDt) {
        this.settlementDt = settlementDt;
    }

    public static final String SETT_RETRY = "settRetry";
    @Column(name = "SETTRETRY", nullable = false, length = 10)
    private long settRetry;

    public long getSettRetry() {
        return this.settRetry;
    }

    public void setSettRetry(long settRetry) {
        this.settRetry = settRetry;
    }

    public static final String NOTES = "notes";
    @Column(name = "NOTES", nullable = true, length = 100)
    private String notes;

    public String getNotes() {
        return this.notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public static final String COMM_AGENT = "commAgent";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "COMMAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant commAgent;

    public ATSPRT_Participant getCommAgent() {
        return this.commAgent;
    }

    public void setCommAgent(ATSPRT_Participant commAgent) {
        this.commAgent = commAgent;
    }

    public static final String SETT_AGENT = "settAgent";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SETTAGENTID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private ATSPRT_Participant settAgent;

    public ATSPRT_Participant getSettAgent() {
        return this.settAgent;
    }

    public void setSettAgent(ATSPRT_Participant settAgent) {
        this.settAgent = settAgent;
    }

    public static final String SESSION = "session";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "SESSIONID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
    private ATSBDI_Session session;

    public ATSBDI_Session getSession() {
        return this.session;
    }

    public void setSession(ATSBDI_Session session) {
        this.session = session;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    public static final String IS_PULLED = "isPulled";
    @Column(name = "ISPULLED", nullable = true, length = 1)
    private boolean isPulled;

    public boolean isPulled() {
        return isPulled;
    }

    public void setPulled(boolean pulled) {
        isPulled = pulled;
    }

    @Override
    public String toString() {
        return "ATSRpt_Transaction [id= " + getId() + ", settlementDt= " + getSettlementDt() + ", settRetry= " + getSettRetry() + ", notes= " + getNotes() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
        int SettRetry = new Long("null".equals(getSettRetry() + "") ? 0 : getSettRetry()).intValue();
        result = prime * result + (int) (SettRetry ^ SettRetry >>> 32);
        result = prime * result + ((getNotes() == null) ? 0 : getNotes().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {
            ATSRpt_Transaction other = (ATSRpt_Transaction) obj;
            return this.hashCode() == other.hashCode();
        }
    }


}