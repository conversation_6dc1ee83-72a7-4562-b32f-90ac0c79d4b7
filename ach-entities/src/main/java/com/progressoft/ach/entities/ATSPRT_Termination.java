package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_Terminations")
@XmlRootElement(name="PRT_Terminations")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSPRT_Termination extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_Termination(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String TERMINATE_FLAG = "terminateFlag";
@Column(name="TERMINATEFLAG", nullable=true, length=1)
private boolean terminateFlag;
public boolean getTerminateFlag(){
return this.terminateFlag;
}
public void setTerminateFlag(boolean terminateFlag){
this.terminateFlag = terminateFlag;
}

public static final String NOTE = "note";
@Column(name="NOTE", nullable=false, length=1024)
private String note;
public String getNote(){
return this.note;
}
public void setNote(String note){
this.note = note;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "prtTerminationParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

@Override
public String toString() {
return "ATSPRT_Termination [id= " + getId() + ", terminateFlag= " + getTerminateFlag() + ", note= " + getNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_Termination other = (ATSPRT_Termination) obj;
return this.hashCode() == other.hashCode();}
}


}