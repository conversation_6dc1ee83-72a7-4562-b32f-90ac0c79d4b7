package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;

import jakarta.persistence.Entity;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;
import java.util.Set;

@Entity
@Table(name="ATSBDS_SessionGRP")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDS_SessionGrp extends ChangeGroup implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_SessionGrp(){/*Default Constructor*/}

@OneToMany(mappedBy="changeGroup")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private Set<ATSBDS_SessionItm> changeItems;

public void setChangeItems(Set<ATSBDS_SessionItm> changeItems) {
this.changeItems = changeItems;
}

@Override
public Set<? extends ChangeItem> getChangeItems() {
return this.changeItems;
}


}