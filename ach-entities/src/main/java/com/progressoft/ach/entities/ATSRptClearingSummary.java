package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRptClearingSummary")
@XmlRootElement(name="RptClearingSummary")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSRptClearingSummary extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRptClearingSummary(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String CLEARING_DATE = "clearingDate";
@Column(name="CLEARINGDATE", nullable=false, length=30)
@Temporal(TemporalType.DATE)
private java.util.Date clearingDate;
public java.util.Date getClearingDate(){
return this.clearingDate;
}
public void setClearingDate(java.util.Date clearingDate){
this.clearingDate = clearingDate;
}

public static final String DIRECTION = "direction";
@Column(name="DIRECTION", nullable=false, length=20)
private String direction;
public String getDirection(){
return this.direction;
}
public void setDirection(String direction){
this.direction = direction;
}

public static final String MESSAGE_TP = "messageTp";
@Column(name="MESSAGETP", nullable=false, length=20)
private String messageTp;
public String getMessageTp(){
return this.messageTp;
}
public void setMessageTp(String messageTp){
this.messageTp = messageTp;
}

public static final String MESSAGE_TYPE = "messageType";
@Column(name="MESSAGETYPE", nullable=true, length=256)
@Transient
private String messageType;
public String getMessageType(){
return this.messageType;
}
public void setMessageType(String messageType){
this.messageType = messageType;
}

public static final String INSTRUCTING = "instructing";
@Column(name="INSTRUCTING", nullable=true, length=256)
@Transient
private String instructing;
public String getInstructing(){
return this.instructing;
}
public void setInstructing(String instructing){
this.instructing = instructing;
}

public static final String INSTRUCTED = "instructed";
@Column(name="INSTRUCTED", nullable=true, length=256)
@Transient
private String instructed;
public String getInstructed(){
return this.instructed;
}
public void setInstructed(String instructed){
this.instructed = instructed;
}

public static final String BATCH_ID = "batchId";
@Column(name="BATCHID", nullable=true, length=256)
@Transient
private String batchId;
public String getBatchId(){
return this.batchId;
}
public void setBatchId(String batchId){
this.batchId = batchId;
}

public static final String TX_COUNT = "txCount";
@Column(name="TXCOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal txCount;
public java.math.BigDecimal getTxCount(){
return this.txCount;
}
public void setTxCount(java.math.BigDecimal txCount){
this.txCount = txCount;
}

public static final String ACCEPTED_TX_COUNT = "acceptedTxCount";
@Column(name="ACCEPTEDTXCOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal acceptedTxCount;
public java.math.BigDecimal getAcceptedTxCount(){
return this.acceptedTxCount;
}
public void setAcceptedTxCount(java.math.BigDecimal acceptedTxCount){
this.acceptedTxCount = acceptedTxCount;
}

public static final String ACCEPTED_TX_AMOUNT = "acceptedTxAmount";
@Column(name="ACCEPTEDTXAMOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal acceptedTxAmount;
public java.math.BigDecimal getAcceptedTxAmount(){
return this.acceptedTxAmount;
}
public void setAcceptedTxAmount(java.math.BigDecimal acceptedTxAmount){
this.acceptedTxAmount = acceptedTxAmount;
}

public static final String REJECTED_TX_COUNT = "rejectedTxCount";
@Column(name="REJECTEDTXCOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal rejectedTxCount;
public java.math.BigDecimal getRejectedTxCount(){
return this.rejectedTxCount;
}
public void setRejectedTxCount(java.math.BigDecimal rejectedTxCount){
this.rejectedTxCount = rejectedTxCount;
}

public static final String REJECTED_TX_AMOUNT = "rejectedTxAmount";
@Column(name="REJECTEDTXAMOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal rejectedTxAmount;
public java.math.BigDecimal getRejectedTxAmount(){
return this.rejectedTxAmount;
}
public void setRejectedTxAmount(java.math.BigDecimal rejectedTxAmount){
this.rejectedTxAmount = rejectedTxAmount;
}

public static final String TX_ID = "txId";
@Column(name="TXID", nullable=true, length=256)
@Transient
private String txId;
public String getTxId(){
return this.txId;
}
public void setTxId(String txId){
this.txId = txId;
}

public static final String DEBTING_PARTY = "debtingParty";
@Column(name="DEBTINGPARTY", nullable=true, length=256)
@Transient
private String debtingParty;
public String getDebtingParty(){
return this.debtingParty;
}
public void setDebtingParty(String debtingParty){
this.debtingParty = debtingParty;
}

public static final String CREDITING_PARTY = "creditingParty";
@Column(name="CREDITINGPARTY", nullable=true, length=256)
@Transient
private String creditingParty;
public String getCreditingParty(){
return this.creditingParty;
}
public void setCreditingParty(String creditingParty){
this.creditingParty = creditingParty;
}

public static final String SETTLEMENT_DATE = "settlementDate";
@Column(name="SETTLEMENTDATE", nullable=true, length=256)
@Transient
private String settlementDate;
public String getSettlementDate(){
return this.settlementDate;
}
public void setSettlementDate(String settlementDate){
this.settlementDate = settlementDate;
}

public static final String AMOUNT = "amount";
@Column(name="AMOUNT", nullable=true, precision=14, scale=5, length=256)
@Transient
private java.math.BigDecimal amount;
public java.math.BigDecimal getAmount(){
return this.amount;
}
public void setAmount(java.math.BigDecimal amount){
this.amount = amount;
}

public static final String TRN_TYPE_CODE = "trnTypeCode";
@Column(name="TRNTYPECODE", nullable=true, length=256)
@Transient
private String trnTypeCode;
public String getTrnTypeCode(){
return this.trnTypeCode;
}
public void setTrnTypeCode(String trnTypeCode){
this.trnTypeCode = trnTypeCode;
}

public static final String STATUS = "status";
@Column(name="STATUS", nullable=true, length=256)
@Transient
private String status;
public String getStatus(){
return this.status;
}
public void setStatus(String status){
this.status = status;
}

public static final String BANK = "bank";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="BANKID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant bank;
public ATSPRT_Participant getBank(){
return this.bank;
}
public void setBank(ATSPRT_Participant bank){
this.bank = bank;
}

@Override
public String toString() {
return "ATSRptClearingSummary [id= " + getId() + ", clearingDate= " + getClearingDate() + ", direction= " + getDirection() + ", messageTp= " + getMessageTp() + ", messageType= " + getMessageType() + ", instructing= " + getInstructing() + ", instructed= " + getInstructed() + ", batchId= " + getBatchId() + ", txCount= " + getTxCount() + ", acceptedTxCount= " + getAcceptedTxCount() + ", acceptedTxAmount= " + getAcceptedTxAmount() + ", rejectedTxCount= " + getRejectedTxCount() + ", rejectedTxAmount= " + getRejectedTxAmount() + ", txId= " + getTxId() + ", debtingParty= " + getDebtingParty() + ", creditingParty= " + getCreditingParty() + ", settlementDate= " + getSettlementDate() + ", amount= " + getAmount() + ", trnTypeCode= " + getTrnTypeCode() + ", status= " + getStatus() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getClearingDate() == null) ? 0 : getClearingDate().hashCode());
result = prime * result + ((getMessageType() == null) ? 0 : getMessageType().hashCode());
result = prime * result + ((getInstructing() == null) ? 0 : getInstructing().hashCode());
result = prime * result + ((getInstructed() == null) ? 0 : getInstructed().hashCode());
result = prime * result + ((getBatchId() == null) ? 0 : getBatchId().hashCode());
result = prime * result + ((getTxId() == null) ? 0 : getTxId().hashCode());
result = prime * result + ((getDebtingParty() == null) ? 0 : getDebtingParty().hashCode());
result = prime * result + ((getCreditingParty() == null) ? 0 : getCreditingParty().hashCode());
result = prime * result + ((getSettlementDate() == null) ? 0 : getSettlementDate().hashCode());
result = prime * result + ((getTrnTypeCode() == null) ? 0 : getTrnTypeCode().hashCode());
result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRptClearingSummary other = (ATSRptClearingSummary) obj;
return this.hashCode() == other.hashCode();}
}


}