package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Entity

@Table(name = "ATSRECON_MESSAGES")
@XmlRootElement(name = "ATSRECON_MESSAGES")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRecon_Message extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSRecon_Message() {/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    private long id;

    public long getId() {
        return this.id;
    }

    public void setId(long id) {
        this.id = id;
    }

    @Column(name = "MESSAGEID")
    private String messageId;
    public String getMessageId(){
        return this.messageId;
    }
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }


    @Column(name = "RECONID")
    private String reconId;
    public String getReconId(){
        return this.reconId;
    }
    public void setReconId(String reconId) {
        this.reconId = reconId;
    }

    @OneToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="ATTACHMENTID")
    private ATSRpt_ReconAtt attachment;
    public ATSRpt_ReconAtt getAttachment() {
        return attachment;
    }
    public void setAttachment(ATSRpt_ReconAtt attachment) {
        this.attachment = attachment;
    }

    @Column(name = "ISPULLED")
    private Boolean isPulled;

    public Boolean getIsPulled() {
        return isPulled;
    }

    public void setIsPulled(Boolean isPulled) {
        this.isPulled = isPulled;
    }

    @Override
    public String toString() {
        return "ATSRecon_Message{" +
                "id=" + id +
                ", messageId='" + messageId + '\'' +
                ", reconId='" + reconId + '\'' +
                ", attachment=" + attachment + '\'' +
                ", isPulled=" + isPulled +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (!(o instanceof ATSRecon_Message that)) return false;
        return id == that.id && Objects.equals(messageId, that.messageId) && Objects.equals(reconId, that.reconId) && Objects.equals(attachment, that.attachment);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, messageId, reconId, attachment);
    }
}