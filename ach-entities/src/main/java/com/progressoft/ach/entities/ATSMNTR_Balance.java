package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMNTR_Balances")
@XmlRootElement(name="MNTR_Balances")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMNTR_Balance extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMNTR_Balance(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String PART_NAME = "partName";
@Column(name="PARTNAME", nullable=true, length=255)
private String partName;
public String getPartName(){
return this.partName;
}
public void setPartName(String partName){
this.partName = partName;
}

public static final String ACC_NUMBER = "accNumber";
@Column(name="ACCNUMBER", nullable=true, length=20)
private String accNumber;
public String getAccNumber(){
return this.accNumber;
}
public void setAccNumber(String accNumber){
this.accNumber = accNumber;
}

public static final String BALANCE = "balance";
@Column(name="BALANCE", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal balance;
public java.math.BigDecimal getBalance(){
return this.balance;
}
public void setBalance(java.math.BigDecimal balance){
this.balance = balance;
}

public static final String TOTAL_CREDIT = "totalCredit";
@Column(name="TOTALCREDIT", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal totalCredit;
public java.math.BigDecimal getTotalCredit(){
return this.totalCredit;
}
public void setTotalCredit(java.math.BigDecimal totalCredit){
this.totalCredit = totalCredit;
}

public static final String CREDIT_CAP = "creditCap";
@Column(name="CREDITCAP", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal creditCap;
public java.math.BigDecimal getCreditCap(){
return this.creditCap;
}
public void setCreditCap(java.math.BigDecimal creditCap){
this.creditCap = creditCap;
}

public static final String TOTAL_DEBIT = "totalDebit";
@Column(name="TOTALDEBIT", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal totalDebit;
public java.math.BigDecimal getTotalDebit(){
return this.totalDebit;
}
public void setTotalDebit(java.math.BigDecimal totalDebit){
this.totalDebit = totalDebit;
}

public static final String DEBIT_CAP = "debitCap";
@Column(name="DEBITCAP", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal debitCap;
public java.math.BigDecimal getDebitCap(){
return this.debitCap;
}
public void setDebitCap(java.math.BigDecimal debitCap){
this.debitCap = debitCap;
}

public static final String SESSION_SEQ = "sessionSeq";
@Column(name="SESSIONSEQ", nullable=true, length=19)
private long sessionSeq;
public long getSessionSeq(){
return this.sessionSeq;
}
public void setSessionSeq(long sessionSeq){
this.sessionSeq = sessionSeq;
}

public static final String SETTLEMENT_DT = "settlementDt";
@Column(name="SETTLEMENTDT", nullable=true, length=30)
@Temporal(TemporalType.DATE)
private java.util.Date settlementDt;
public java.util.Date getSettlementDt(){
return this.settlementDt;
}
public void setSettlementDt(java.util.Date settlementDt){
this.settlementDt = settlementDt;
}

public static final String BALANCE_STATUS = "balanceStatus";
@Column(name="BALANCESTATUS", nullable=true, length=100)
private String balanceStatus;
public String getBalanceStatus(){
    return this.balanceStatus;
}

public void setBalanceStatus(String balanceStatus) {
    this.balanceStatus = balanceStatus;
}

public static final String CURRENCY_CODE = "currencyCode";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name = "CURRENCYCODEID", nullable = true)
@WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currencyCode;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrencyCode() {
        return this.currencyCode;
    }

    public void setCurrencyCode(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currencyCode) {
        this.currencyCode = currencyCode;
    }

@Override
public String toString() {
    return "ATSMNTR_Balance [id= " + getId() + ", partName= " + getPartName() + ", accNumber= " + getAccNumber() + ", balance= " + getBalance() + ", totalCredit= " + getTotalCredit() + ", creditCap= " + getCreditCap() + ", totalDebit= " + getTotalDebit() + ", debitCap= " + getDebitCap() + ", sessionSeq= " + getSessionSeq() + ", settlementDt= " + getSettlementDt() + ", balanceStatus= " + getBalanceStatus() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getPartName() == null) ? 0 : getPartName().hashCode());
result = prime * result + ((getAccNumber() == null) ? 0 : getAccNumber().hashCode());
int SessionSeq= new Long("null".equals(getSessionSeq() + "") ? 0 : getSessionSeq()).intValue();
result = prime * result + (int) (SessionSeq ^ SessionSeq >>> 32);
result = prime * result + ((getSettlementDt() == null) ? 0 : getSettlementDt().hashCode());
result = prime * result + ((getBalanceStatus() == null) ? 0 : getBalanceStatus().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMNTR_Balance other = (ATSMNTR_Balance) obj;
return this.hashCode() == other.hashCode();}
}


}