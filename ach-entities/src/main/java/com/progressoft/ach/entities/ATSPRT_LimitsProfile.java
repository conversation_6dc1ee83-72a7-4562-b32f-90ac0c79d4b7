package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSPRT_LimitsProfile",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="PRT_LimitsProfile")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSPRT_LimitsProfile extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_LimitsProfile(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String CODE = "code";
@Column(name="CODE", nullable=false, length=12)
private String code;
public String getCode(){
return this.code;
}
public void setCode(String code){
this.code = code;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=128)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=false, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String LIMITS_PER = "limitsPer";
@Column(name="LIMITSPER", nullable=true, length=12)
private String limitsPer;
public String getLimitsPer(){
return this.limitsPer;
}
public void setLimitsPer(String limitsPer){
this.limitsPer = limitsPer;
}

public static final String DBATCH_COUNT = "dbatchCount";
@Column(name="DBATCHCOUNT", nullable=false, length=10)
private long dbatchCount;
public long getDbatchCount(){
return this.dbatchCount;
}
public void setDbatchCount(long dbatchCount){
this.dbatchCount = dbatchCount;
}

public static final String DTRANS_PER_BATCH = "dtransPerBatch";
@Column(name="DTRANSPERBATCH", nullable=false, length=10)
private long dtransPerBatch;
public long getDtransPerBatch(){
return this.dtransPerBatch;
}
public void setDtransPerBatch(long dtransPerBatch){
this.dtransPerBatch = dtransPerBatch;
}

public static final String DTX_LIMIT = "dtxLimit";
@Column(name="DTXLIMIT", nullable=false, length=10)
private long dtxLimit;
public long getDtxLimit(){
return this.dtxLimit;
}
public void setDtxLimit(long dtxLimit){
this.dtxLimit = dtxLimit;
}

public static final String CBATCH_COUNT = "cbatchCount";
@Column(name="CBATCHCOUNT", nullable=false, length=10)
private long cbatchCount;
public long getCbatchCount(){
return this.cbatchCount;
}
public void setCbatchCount(long cbatchCount){
this.cbatchCount = cbatchCount;
}

public static final String CTRANS_PER_BATCH = "ctransPerBatch";
@Column(name="CTRANSPERBATCH", nullable=false, length=10)
private long ctransPerBatch;
public long getCtransPerBatch(){
return this.ctransPerBatch;
}
public void setCtransPerBatch(long ctransPerBatch){
this.ctransPerBatch = ctransPerBatch;
}

public static final String CTX_LIMIT = "ctxLimit";
@Column(name="CTXLIMIT", nullable=false, length=10)
private long ctxLimit;
public long getCtxLimit(){
return this.ctxLimit;
}
public void setCtxLimit(long ctxLimit){
this.ctxLimit = ctxLimit;
}

public static final String LIMITS_PROFILE_P_R_T_LIMITS_AMOUNTS = "limitsProfilePRT_LimitsAmounts";
@OneToMany(mappedBy = "limitsProfile")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSPRT_LimitsAmount> limitsProfilePRT_LimitsAmounts;
public List<ATSPRT_LimitsAmount> getLimitsProfilePRTLimitsAmounts(){
return this.limitsProfilePRT_LimitsAmounts;
}
public void setLimitsProfilePRTLimitsAmounts(List<ATSPRT_LimitsAmount> limitsProfilePRT_LimitsAmounts){
this.limitsProfilePRT_LimitsAmounts = limitsProfilePRT_LimitsAmounts;
}

@Override
public String toString() {
return "ATSPRT_LimitsProfile [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", limitsPer= " + getLimitsPer() + ", dbatchCount= " + getDbatchCount() + ", dtransPerBatch= " + getDtransPerBatch() + ", dtxLimit= " + getDtxLimit() + ", cbatchCount= " + getCbatchCount() + ", ctransPerBatch= " + getCtransPerBatch() + ", ctxLimit= " + getCtxLimit() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
result = prime * result + ((getLimitsPer() == null) ? 0 : getLimitsPer().hashCode());
int DbatchCount= new Long("null".equals(getDbatchCount() + "") ? 0 : getDbatchCount()).intValue();
result = prime * result + (int) (DbatchCount ^ DbatchCount >>> 32);
int DtransPerBatch= new Long("null".equals(getDtransPerBatch() + "") ? 0 : getDtransPerBatch()).intValue();
result = prime * result + (int) (DtransPerBatch ^ DtransPerBatch >>> 32);
int DtxLimit= new Long("null".equals(getDtxLimit() + "") ? 0 : getDtxLimit()).intValue();
result = prime * result + (int) (DtxLimit ^ DtxLimit >>> 32);
int CbatchCount= new Long("null".equals(getCbatchCount() + "") ? 0 : getCbatchCount()).intValue();
result = prime * result + (int) (CbatchCount ^ CbatchCount >>> 32);
int CtransPerBatch= new Long("null".equals(getCtransPerBatch() + "") ? 0 : getCtransPerBatch()).intValue();
result = prime * result + (int) (CtransPerBatch ^ CtransPerBatch >>> 32);
int CtxLimit= new Long("null".equals(getCtxLimit() + "") ? 0 : getCtxLimit()).intValue();
result = prime * result + (int) (CtxLimit ^ CtxLimit >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_LimitsProfile other = (ATSPRT_LimitsProfile) obj;
return this.hashCode() == other.hashCode();}
}


}