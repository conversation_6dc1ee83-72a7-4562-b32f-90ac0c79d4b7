package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSLKP_Creditors",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"COMPANYCODE","Z_TENANT_ID"})
})
@XmlRootElement(name="LKP_Creditors")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSLKP_Creditor extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSLKP_Creditor(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String COMPANY_CODE = "companyCode";
@Column(name="COMPANYCODE", nullable=false, length=12)
private String companyCode;
public String getCompanyCode(){
return this.companyCode;
}
public void setCompanyCode(String companyCode){
this.companyCode = companyCode;
}

public static final String COMPANY_NAME = "companyName";
@Column(name="COMPANYNAME", nullable=false, length=70)
private String companyName;
public String getCompanyName(){
return this.companyName;
}
public void setCompanyName(String companyName){
this.companyName = companyName;
}

public static final String COMPANY_ACC_NO = "companyAccNo";
@Column(name="COMPANYACCNO", nullable=false, length=34)
private String companyAccNo;
public String getCompanyAccNo(){
return this.companyAccNo;
}
public void setCompanyAccNo(String companyAccNo){
this.companyAccNo = companyAccNo;
}

public static final String COMPANY_ID_VALUE = "companyIdValue";
@Column(name="COMPANYIDVALUE", nullable=true, length=35)
private String companyIdValue;
public String getCompanyIdValue(){
return this.companyIdValue;
}
public void setCompanyIdValue(String companyIdValue){
this.companyIdValue = companyIdValue;
}

public static final String ADDRESS = "address";
@Column(name="ADDRESS", nullable=true, length=128)
private String address;
public String getAddress(){
return this.address;
}
public void setAddress(String address){
this.address = address;
}

public static final String IS_TECHNICAL_PARTICIPANT = "isTechnicalParticipant";
@Column(name="ISTECHNICALPARTICIPANT", nullable=true, length=1)
private boolean isTechnicalParticipant;
public boolean getIsTechnicalParticipant(){
return this.isTechnicalParticipant;
}
public void setIsTechnicalParticipant(boolean isTechnicalParticipant){
this.isTechnicalParticipant = isTechnicalParticipant;
}

public static final String EFFECTIVE_START_DT = "effectiveStartDt";
@Column(name="EFFECTIVESTARTDT", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date effectiveStartDt;
public java.util.Date getEffectiveStartDt(){
return this.effectiveStartDt;
}
public void setEffectiveStartDt(java.util.Date effectiveStartDt){
this.effectiveStartDt = effectiveStartDt;
}

public static final String EFFECTIVE_END_DT = "effectiveEndDt";
@Column(name="EFFECTIVEENDDT", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date effectiveEndDt;
public java.util.Date getEffectiveEndDt(){
return this.effectiveEndDt;
}
public void setEffectiveEndDt(java.util.Date effectiveEndDt){
this.effectiveEndDt = effectiveEndDt;
}

public static final String ACTIVE_STATUS = "activeStatus";
@Column(name="ACTIVESTATUS", nullable=true, length=1)
private boolean activeStatus;
public boolean getActiveStatus(){
return this.activeStatus;
}
public void setActiveStatus(boolean activeStatus){
this.activeStatus = activeStatus;
}

public static final String NON_CANCELABLE = "nonCancelable";
@Column(name="NONCANCELABLE", nullable=true, length=1)
private boolean nonCancelable;
public boolean getNonCancelable(){
return this.nonCancelable;
}
public void setNonCancelable(boolean nonCancelable){
this.nonCancelable = nonCancelable;
}

public static final String COMPANY_BANK = "companyBank";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="COMPANYBANKID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "creditorParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant companyBank;
public ATSPRT_Participant getCompanyBank(){
return this.companyBank;
}
public void setCompanyBank(ATSPRT_Participant companyBank){
this.companyBank = companyBank;
}

public static final String COMPANY_BANK_BRANCH = "companyBankBranch";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="COMPANYBANKBRANCHID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "creditorBranchProvider", keyProperty = "id")
private ATSPRT_Branch companyBankBranch;
public ATSPRT_Branch getCompanyBankBranch(){
return this.companyBankBranch;
}
public void setCompanyBankBranch(ATSPRT_Branch companyBankBranch){
this.companyBankBranch = companyBankBranch;
}

public static final String COMPANY_ID_TYPE = "companyIdType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="COMPANYIDTYPEID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "privateIdentificationProvider", keyProperty = "id")
private ATSLKP_PrivateIdentification companyIdType;
public ATSLKP_PrivateIdentification getCompanyIdType(){
return this.companyIdType;
}
public void setCompanyIdType(ATSLKP_PrivateIdentification companyIdType){
this.companyIdType = companyIdType;
}

public static final String PARTICIPANT = "participant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PARTICIPANTID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "periodManagementReqPrtProvider", keyProperty = "id")
private ATSPRT_Participant participant;
public ATSPRT_Participant getParticipant(){
return this.participant;
}
public void setParticipant(ATSPRT_Participant participant){
this.participant = participant;
}

public static final String CREDITOR_LOOKUP_MANDATES = "creditorLookupMandates";
@OneToMany(mappedBy = "creditorLookup")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
private List<ATSMandate> creditorLookupMandates;
public List<ATSMandate> getCreditorLookupMandates(){
return this.creditorLookupMandates;
}
public void setCreditorLookupMandates(List<ATSMandate> creditorLookupMandates){
this.creditorLookupMandates = creditorLookupMandates;
}

@Override
public String toString() {
return "ATSLKP_Creditor [id= " + getId() + ", companyCode= " + getCompanyCode() + ", companyName= " + getCompanyName() + ", companyAccNo= " + getCompanyAccNo() + ", companyIdValue= " + getCompanyIdValue() + ", address= " + getAddress() + ", isTechnicalParticipant= " + getIsTechnicalParticipant() + ", effectiveStartDt= " + getEffectiveStartDt() + ", effectiveEndDt= " + getEffectiveEndDt() + ", activeStatus= " + getActiveStatus() + ", nonCancelable= " + getNonCancelable() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCompanyCode() == null) ? 0 : getCompanyCode().hashCode());
result = prime * result + ((getCompanyName() == null) ? 0 : getCompanyName().hashCode());
result = prime * result + ((getCompanyAccNo() == null) ? 0 : getCompanyAccNo().hashCode());
result = prime * result + ((getCompanyIdValue() == null) ? 0 : getCompanyIdValue().hashCode());
result = prime * result + ((getAddress() == null) ? 0 : getAddress().hashCode());
result = prime * result + ((getEffectiveStartDt() == null) ? 0 : getEffectiveStartDt().hashCode());
result = prime * result + ((getEffectiveEndDt() == null) ? 0 : getEffectiveEndDt().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSLKP_Creditor other = (ATSLKP_Creditor) obj;
return this.hashCode() == other.hashCode();}
}


}