package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_Messages",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"REFPARTICIPANTID","MSGTYPEID","Z_TENANT_ID"})
})
@XmlRootElement(name="PRT_Messages")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSPRT_Message extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_Message(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String INWARD_FLAG = "inwardFlag";
@Column(name="INWARDFLAG", nullable=false, length=1)
private boolean inwardFlag;
public boolean getInwardFlag(){
return this.inwardFlag;
}
public void setInwardFlag(boolean inwardFlag){
this.inwardFlag = inwardFlag;
}

public static final String OUTWARD_FLAG = "outwardFlag";
@Column(name="OUTWARDFLAG", nullable=false, length=1)
private boolean outwardFlag;
public boolean getOutwardFlag(){
return this.outwardFlag;
}
public void setOutwardFlag(boolean outwardFlag){
this.outwardFlag = outwardFlag;
}

public static final String REF_PARTICIPANT = "refParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "capsParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant refParticipant;
public ATSPRT_Participant getRefParticipant(){
return this.refParticipant;
}
public void setRefParticipant(ATSPRT_Participant refParticipant){
this.refParticipant = refParticipant;
}

public static final String MSG_TYPE = "msgType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "allMessageTypeProvider", keyProperty = "id")
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

@Override
public String toString() {
return "ATSPRT_Message [id= " + getId() + ", inwardFlag= " + getInwardFlag() + ", outwardFlag= " + getOutwardFlag() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_Message other = (ATSPRT_Message) obj;
return this.hashCode() == other.hashCode();}
}


}