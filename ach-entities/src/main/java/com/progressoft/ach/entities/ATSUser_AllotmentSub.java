package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name="ATSUser_AllotmentSub")
@XmlRootElement(name="ATSUser_AllotmentSub")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE )
public class ATSUser_AllotmentSub  extends JFWEntity implements Serializable {

    @Id
    @GeneratedValue(strategy= GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;

    @Column(name = "STARTTIME")
    protected String startTime;

    @Column(name = "ENDTIME")
    protected String endTime;

    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="USERALLOTID", nullable=false)
    @WithValueProvider(jupiterValueProviderBean = "userAllotmentsProvider", keyProperty = "id")
    private ATSUser_Allotment userAllotment;

    @ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinTable(name="ATSUser_AllotmentViews", joinColumns = @JoinColumn(name = "ATS_USER_ALLOT_SUB_ID"), inverseJoinColumns = @JoinColumn(name = "VIEW_ID"))
    @WithValueProvider(jupiterValueProviderBean = "userAllotmentViewsProvider", keyProperty = "id")
    private List<ATSUser_AllotmentView> allotViews = new ArrayList<>();


    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public ATSUser_Allotment getUserAllotment() {
        return userAllotment;
    }

    public void setUserAllotment(ATSUser_Allotment userAllotment) {
        this.userAllotment = userAllotment;
    }

    public List<ATSUser_AllotmentView> getAllotViews() {
        return allotViews;
    }

    public void setAllotViews(List<ATSUser_AllotmentView> allotViews) {
        this.allotViews = allotViews;
    }

    @Override
    public boolean equals(Object o) {
        if (o == null || getClass() != o.getClass()) return false;
        ATSUser_AllotmentSub that = (ATSUser_AllotmentSub) o;
        return id == that.id && Objects.equals(startTime, that.startTime) && Objects.equals(endTime, that.endTime) && Objects.equals(userAllotment, that.userAllotment) && Objects.equals(allotViews, that.allotViews);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, startTime, endTime, userAllotment, allotViews);
    }
}
