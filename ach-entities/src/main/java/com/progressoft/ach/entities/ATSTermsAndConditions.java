package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;

@Entity

@Table(name="ATSTerms_And_Conditions")
@XmlRootElement(name="Terms_And_Conditions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSTermsAndConditions extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSTermsAndConditions(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String TERMS_AND_CONDITIONS = "termsAndConditions";
@Column(name="TERMSANDCONDITIONS", nullable=true, length=2000)
private String termsAndConditions;
public String getTermsAndConditions(){
return this.termsAndConditions;
}
public void setTermsAndConditions(String termsAndConditions){
this.termsAndConditions = termsAndConditions;
}

@Override
public String toString() {
return "ATSTermsAndConditions [id= " + getId() + ", termsAndConditions= " + getTermsAndConditions() +  "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getTermsAndConditions()== null) ? 0 : getTermsAndConditions().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {
    ATSTermsAndConditions other = (ATSTermsAndConditions) obj;
return this.hashCode() == other.hashCode();}
}


}