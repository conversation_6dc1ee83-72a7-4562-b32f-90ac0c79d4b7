package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMndt_Policy_AutoReply",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"REQUESTTYPEID","REQUESTINITIATEDBY","Z_TENANT_ID"})
})
@XmlRootElement(name="Mndt_Policy_AutoReply")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMndt_Policy_AutoReply extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_AutoReply(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_INITIATED_BY = "requestInitiatedBy";
@Column(name="REQUESTINITIATEDBY", nullable=false, length=1)
private String requestInitiatedBy;
public String getRequestInitiatedBy(){
return this.requestInitiatedBy;
}
public void setRequestInitiatedBy(String requestInitiatedBy){
this.requestInitiatedBy = requestInitiatedBy;
}

public static final String AUTO_REPLY_AFTER = "autoReplyAfter";
@Column(name="AUTOREPLYAFTER", nullable=false, length=5)
private long autoReplyAfter;
public long getAutoReplyAfter(){
return this.autoReplyAfter;
}
public void setAutoReplyAfter(long autoReplyAfter){
this.autoReplyAfter = autoReplyAfter;
}

public static final String AUTO_REPLY_TYPE = "autoReplyType";
@Column(name="AUTOREPLYTYPE", nullable=false, length=1)
private String autoReplyType;
public String getAutoReplyType(){
return this.autoReplyType;
}
public void setAutoReplyType(String autoReplyType){
this.autoReplyType = autoReplyType;
}

public static final String POLICY_DESC = "policyDesc";
@Column(name="POLICYDESC", nullable=true, length=1000)
private String policyDesc;
public String getPolicyDesc(){
return this.policyDesc;
}
public void setPolicyDesc(String policyDesc){
this.policyDesc = policyDesc;
}

public static final String REQUEST_TYPE_ID = "requestTypeId";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTTYPEID", nullable=false)
private ATSLKP_Mnd_RequestsType requestTypeId;
public ATSLKP_Mnd_RequestsType getRequestTypeId(){
return this.requestTypeId;
}
public void setRequestTypeId(ATSLKP_Mnd_RequestsType requestTypeId){
this.requestTypeId = requestTypeId;
}

public static final String AUTO_REJECT_REASON_ID = "autoRejectReasonId";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="AUTOREJECTREASONID", nullable=false)
private ATSLKP_MandateReason autoRejectReasonId;
public ATSLKP_MandateReason getAutoRejectReasonId(){
return this.autoRejectReasonId;
}
public void setAutoRejectReasonId(ATSLKP_MandateReason autoRejectReasonId){
this.autoRejectReasonId = autoRejectReasonId;
}

@Override
public String toString() {
return "ATSMndt_Policy_AutoReply [id= " + getId() + ", requestInitiatedBy= " + getRequestInitiatedBy() + ", autoReplyAfter= " + getAutoReplyAfter() + ", autoReplyType= " + getAutoReplyType() + ", policyDesc= " + getPolicyDesc() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
int AutoReplyAfter= new Long("null".equals(getAutoReplyAfter() + "") ? 0 : getAutoReplyAfter()).intValue();
result = prime * result + (int) (AutoReplyAfter ^ AutoReplyAfter >>> 32);
result = prime * result + ((getPolicyDesc() == null) ? 0 : getPolicyDesc().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMndt_Policy_AutoReply other = (ATSMndt_Policy_AutoReply) obj;
return this.hashCode() == other.hashCode();}
}


}