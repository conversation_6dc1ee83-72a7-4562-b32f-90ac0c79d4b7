package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSBDS_Sessions")
@XmlRootElement(name="BDS_Sessions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDS_Session extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_Session(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=128)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String START_TM = "startTm";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="STARTTM", nullable=false, length=30)
private java.sql.Timestamp startTm;
public java.sql.Timestamp getStartTm(){
return this.startTm;
}
public void setStartTm(java.sql.Timestamp startTm){
this.startTm = startTm;
}

public static final String MAX_START_TM = "maxStartTm";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="MAXSTARTTM", nullable=false, length=30)
private java.sql.Timestamp maxStartTm;
public java.sql.Timestamp getMaxStartTm(){
return this.maxStartTm;
}
public void setMaxStartTm(java.sql.Timestamp maxStartTm){
this.maxStartTm = maxStartTm;
}

public static final String OFFSET_DT = "offsetDt";
@Column(name="OFFSETDT", nullable=false, length=3)
private long offsetDt;
public long getOffsetDt(){
return this.offsetDt;
}
public void setOffsetDt(long offsetDt){
this.offsetDt = offsetDt;
}

public static final String ACTIVE_FLAG = "activeFlag";
@Column(name="ACTIVEFLAG", nullable=false, length=1)
private boolean activeFlag;
public boolean getActiveFlag(){
return this.activeFlag;
}
public void setActiveFlag(boolean activeFlag){
this.activeFlag = activeFlag;
}

public static final String PRIORITY_LEVEL = "priorityLevel";
@Column(name="PRIORITYLEVEL", nullable=false, length=1)
private long priorityLevel;
public long getPriorityLevel(){
return this.priorityLevel;
}
public void setPriorityLevel(long priorityLevel){
this.priorityLevel = priorityLevel;
}

public static final String WEIGHT = "weight";
@Column(name="WEIGHT", nullable=false, length=2)
private long weight;
public long getWeight(){
return this.weight;
}
public void setWeight(long weight){
this.weight = weight;
}

public static final String VALID_START = "validStart";
@Column(name="VALIDSTART", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date validStart;
public java.util.Date getValidStart(){
return this.validStart;
}
public void setValidStart(java.util.Date validStart){
this.validStart = validStart;
}

public static final String VALID_END = "validEnd";
@Column(name="VALIDEND", nullable=false, length=34)
@Temporal(TemporalType.DATE)
private java.util.Date validEnd;
public java.util.Date getValidEnd(){
return this.validEnd;
}
public void setValidEnd(java.util.Date validEnd){
this.validEnd = validEnd;
}

public static final String CRON_EXPRESSION = "cronExpression";
@Column(name="CRONEXPRESSION", nullable=true, length=255)
private String cronExpression;
public String getCronExpression(){
return this.cronExpression;
}
public void setCronExpression(String cronExpression){
this.cronExpression = cronExpression;
}

public static final String SKIP_IS_BZ_D = "skipIsBzD";
@Column(name="SKIPISBZD", nullable=true, length=1)
private boolean skipIsBzD;
public boolean getSkipIsBzD(){
return this.skipIsBzD;
}
public void setSkipIsBzD(boolean skipIsBzD){
this.skipIsBzD = skipIsBzD;
}

public static final String PRIORITY_VALUE = "priorityValue";
@Column(name="PRIORITYVALUE", nullable=false, length=6)
private String priorityValue;
public String getPriorityValue(){
return this.priorityValue;
}

    public void setPriorityValue(String priorityValue) {
        this.priorityValue = priorityValue;
    }

    public static final String IS_SPECIAL_CLEARING = "isSpecialClearing";
    @Column(name = "ISSPECIALCLEARING", nullable = true, length = 1)
    private boolean isSpecialClearing;

    public boolean getIsSpecialClearing() {
        return this.isSpecialClearing;
    }

    public void setIsSpecialClearing(boolean isSpecialClearing) {
        this.isSpecialClearing = isSpecialClearing;
    }

    public static final String TEMPLATE = "template";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "TEMPLATEID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "templateProvider", keyProperty = "id")
    private ATSBDS_Template template;

    public ATSBDS_Template getTemplate() {
        return this.template;
    }

    public void setTemplate(ATSBDS_Template template) {
        this.template = template;
    }

    public static final String REF_CALENDAR = "refCalendar";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "REFCALENDARID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "calendarProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.calendar.JFWCalendar refCalendar;

    public com.progressoft.jfw.model.bussinessobject.calendar.JFWCalendar getRefCalendar() {
        return this.refCalendar;
    }

    public void setRefCalendar(com.progressoft.jfw.model.bussinessobject.calendar.JFWCalendar refCalendar) {
        this.refCalendar = refCalendar;
    }

    public static final String REF_SESSION_B_D_S_PERIODS = "refSessionBDS_Periods";
    @OneToMany(mappedBy = "refSession")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @WithValueProvider(jupiterValueProviderBean = "bdsPeriodProvider", keyProperty = "id")
    private List<ATSBDS_Period> refSessionBDS_Periods;

    public List<ATSBDS_Period> getRefSessionBDSPeriods() {
        return this.refSessionBDS_Periods;
    }

    public void setRefSessionBDSPeriods(List<ATSBDS_Period> refSessionBDS_Periods) {
        this.refSessionBDS_Periods = refSessionBDS_Periods;
    }

    public static final String REF_SESSION_B_D_I_SESSIONS = "refSessionBDI_Sessions";
    @OneToMany(mappedBy = "refSession")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
    private List<ATSBDI_Session> refSessionBDI_Sessions;

    public List<ATSBDI_Session> getRefSessionBDISessions() {
        return this.refSessionBDI_Sessions;
    }

    public void setRefSessionBDISessions(List<ATSBDI_Session> refSessionBDI_Sessions) {
        this.refSessionBDI_Sessions = refSessionBDI_Sessions;
    }

    public static final String RETURN_CREDIT_DURATION = "returnCreditDuration";
    @Column(name = "RETURNCREDITDURATION", length = 20)
    private long returnCreditDuration;

    public long getReturnCreditDuration() {
        return returnCreditDuration;
    }

    public void setReturnCreditDuration(long returnCreditDuration) {
        this.returnCreditDuration = returnCreditDuration;
    }

    public static final String RETURN_DEBIT_DURATION = "returnDebitDuration";
    @Column(name = "RETURNDEBITDURATION", length = 20)
    private long returnDebitDuration;

    public long getReturnDebitDuration() {
        return returnDebitDuration;
    }

    public void setReturnDebitDuration(long returnDebitDuration) {
        this.returnDebitDuration = returnDebitDuration;
    }

    public static final String REVERSAL_CREDIT_DURATION = "reversalCreditDuration";
    @Column(name = "REVERSALCREDITDURATION", length = 20)
    private long reversalCreditDuration;

    public long getReversalCreditDuration() {
        return reversalCreditDuration;
    }

    public void setReversalCreditDuration(long reversalCreditDuration) {
        this.reversalCreditDuration = reversalCreditDuration;
    }

    public static final String REVERSAL_DEBIT_DURATION = "reversalDebitDuration";
    @Column(name = "REVERSALDEBITDURATION", length = 20)
    private long reversalDebitDuration;

    public long getReversalDebitDuration() {
        return reversalDebitDuration;
    }

    public void setReversalDebitDuration(long reversalDebitDuration) {
        this.reversalDebitDuration = reversalDebitDuration;
    }

    @Override
    public String toString() {
        return "ATSBDS_Session [id= " + getId() + ", name= " + getName() + ", description= " + getDescription() + ", startTm= " + getStartTm() + ", maxStartTm= " + getMaxStartTm() + ", offsetDt= " + getOffsetDt() + ", activeFlag= " + getActiveFlag() + ", priorityLevel= " + getPriorityLevel() + ", weight= " + getWeight() + ", validStart= " + getValidStart() + ", validEnd= " + getValidEnd() + ", cronExpression= " + getCronExpression() + ", returnCreditDuration= " + getReturnCreditDuration() + ", returnDebitDuration= " + getReturnDebitDuration() + ", reversalCreditDuration= " + getReversalCreditDuration() + ", reversalDebitDuration= " + getReversalDebitDuration() + ", skipIsBzD= " + getSkipIsBzD() + ", priorityValue= " + getPriorityValue() + ", isSpecialClearing= " + getIsSpecialClearing() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id = new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
        int OffsetDt = new Long("null".equals(getOffsetDt() + "") ? 0 : getOffsetDt()).intValue();
        result = prime * result + (int) (OffsetDt ^ OffsetDt >>> 32);
        int PriorityLevel = new Long("null".equals(getPriorityLevel() + "") ? 0 : getPriorityLevel()).intValue();
        result = prime * result + (int) (PriorityLevel ^ PriorityLevel >>> 32);
        int Weight = new Long("null".equals(getWeight() + "") ? 0 : getWeight()).intValue();
        result = prime * result + (int) (Weight ^ Weight >>> 32);
        result = prime * result + ((getValidStart() == null) ? 0 : getValidStart().hashCode());
        result = prime * result + ((getValidEnd() == null) ? 0 : getValidEnd().hashCode());
        result = prime * result + ((getCronExpression() == null) ? 0 : getCronExpression().hashCode());
        int ReturnCreditDuration = new Long("null".equals(getReturnCreditDuration() + "") ? 0 : getReturnCreditDuration()).intValue();
        result = prime * result + (int) (ReturnCreditDuration ^ ReturnCreditDuration >>> 32);
        int ReturnDebitDuration = new Long("null".equals(getReturnDebitDuration() + "") ? 0 : getReturnDebitDuration()).intValue();
        result = prime * result + (int) (ReturnDebitDuration ^ ReturnDebitDuration >>> 32);
        int ReversalCreditDuration = new Long("null".equals(getReversalCreditDuration() + "") ? 0 : getReversalCreditDuration()).intValue();
        result = prime * result + (int) (ReversalCreditDuration ^ ReversalCreditDuration >>> 32);
        int ReversalDebitDuration = new Long("null".equals(getReversalDebitDuration() + "") ? 0 : getReversalDebitDuration()).intValue();
        result = prime * result + (int) (ReversalDebitDuration ^ ReversalDebitDuration >>> 32);
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_Session other = (ATSBDS_Session) obj;
return this.hashCode() == other.hashCode();}
}


}