package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMSG_PurpProfs",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"MSGTYPEID","CATEGORYPURPOSEID","CURRENCYID","Z_TENANT_ID"})
})
@XmlRootElement(name="MSG_PurpProfs")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMSG_PurpProf extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMSG_PurpProf(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String MIN_AMOUNT = "minAmount";
@Column(name="MINAMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal minAmount;
public java.math.BigDecimal getMinAmount(){
return this.minAmount;
}
public void setMinAmount(java.math.BigDecimal minAmount){
this.minAmount = minAmount;
}

public static final String MAX_AMOUNT = "maxAmount";
@Column(name="MAXAMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal maxAmount;
public java.math.BigDecimal getMaxAmount(){
return this.maxAmount;
}
public void setMaxAmount(java.math.BigDecimal maxAmount){
this.maxAmount = maxAmount;
}

public static final String BATCH_TX_COUNT_LIMIT = "batchTxCountLimit";
@Column(name="BATCHTXCOUNTLIMIT", nullable=false, length=10)
private long batchTxCountLimit;
public long getBatchTxCountLimit(){
return this.batchTxCountLimit;
}
public void setBatchTxCountLimit(long batchTxCountLimit){
this.batchTxCountLimit = batchTxCountLimit;
}

public static final String AUTO_REPLY_MODE = "autoReplyMode";
@Column(name="AUTOREPLYMODE", nullable=false, length=20)
private String autoReplyMode;
public String getAutoReplyMode(){
return this.autoReplyMode;
}
public void setAutoReplyMode(String autoReplyMode){
this.autoReplyMode = autoReplyMode;
}

public static final String MSG_TYPE = "msgType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MSGTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "messageTypeProvider", keyProperty = "id")
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}
public void setMsgType(ATSMSG_Type msgType){
this.msgType = msgType;
}

public static final String CATEGORY_PURPOSE = "categoryPurpose";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CATEGORYPURPOSEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private ATSMSG_CtgPurp categoryPurpose;
public ATSMSG_CtgPurp getCategoryPurpose(){
    return this.categoryPurpose;
}

    public void setCategoryPurpose(ATSMSG_CtgPurp categoryPurpose) {
        this.categoryPurpose = categoryPurpose;
    }

    public static final String CURRENCY = "currency";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name = "CURRENCYID", nullable = false)
    @WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
    private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

    public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
        return this.currency;
    }

    public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
        this.currency = currency;
    }

    @Override
    public String toString() {
        return "ATSMSG_PurpProf [id= " + getId() + ", minAmount= " + getMinAmount() + ", maxAmount= " + getMaxAmount() + ", batchTxCountLimit= " + getBatchTxCountLimit() + ", autoReplyMode= " + getAutoReplyMode() + "]";
    }

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
int BatchTxCountLimit= new Long("null".equals(getBatchTxCountLimit() + "") ? 0 : getBatchTxCountLimit()).intValue();
result = prime * result + (int) (BatchTxCountLimit ^ BatchTxCountLimit >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMSG_PurpProf other = (ATSMSG_PurpProf) obj;
return this.hashCode() == other.hashCode();}
}


}