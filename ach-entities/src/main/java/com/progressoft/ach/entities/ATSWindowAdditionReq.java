package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSWindowAdditionReqs")
@XmlRootElement(name="WindowAdditionReqs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSWindowAdditionReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSWindowAdditionReq(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_ID = "requestId";
@Column(name="REQUESTID", nullable=false, length=4000)
private String requestId;
public String getRequestId(){
return this.requestId;
}
public void setRequestId(String requestId){
this.requestId = requestId;
}

public static final String PERIOD_START = "periodStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODSTART", nullable=true, length=34)
private java.sql.Timestamp periodStart;
public java.sql.Timestamp getPeriodStart(){
return this.periodStart;
}
public void setPeriodStart(java.sql.Timestamp periodStart){
this.periodStart = periodStart;
}

public static final String PERIOD_END = "periodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="PERIODEND", nullable=true, length=34)
private java.sql.Timestamp periodEnd;
public java.sql.Timestamp getPeriodEnd(){
return this.periodEnd;
}
public void setPeriodEnd(java.sql.Timestamp periodEnd){
this.periodEnd = periodEnd;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DURATION = "duration";
@Column(name="DURATION", nullable=false, length=19)
private String duration;
public String getDuration(){
return this.duration;
}
public void setDuration(String duration){
this.duration = duration;
}

public static final String GRACE_PERIOD = "gracePeriod";
@Column(name="GRACEPERIOD", nullable=true, length=34)
private long gracePeriod;
public long getGracePeriod(){
return this.gracePeriod;
}
public void setGracePeriod(long gracePeriod){
this.gracePeriod = gracePeriod;
}

public static final String WINDOW_START = "windowStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWSTART", nullable=false, length=34)
private java.sql.Timestamp windowStart;
public java.sql.Timestamp getWindowStart(){
return this.windowStart;
}
public void setWindowStart(java.sql.Timestamp windowStart){
this.windowStart = windowStart;
}

public static final String WINDOW_END = "windowEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="WINDOWEND", nullable=false, length=34)
private java.sql.Timestamp windowEnd;
public java.sql.Timestamp getWindowEnd(){
return this.windowEnd;
}
public void setWindowEnd(java.sql.Timestamp windowEnd){
this.windowEnd = windowEnd;
}

public static final String IS_INITIATED_BY_PARTICIPANT = "isInitiatedByParticipant";
@Column(name="ISINITIATEDBYPARTICIPANT", nullable=true, length=1)
private boolean isInitiatedByParticipant;
public boolean getIsInitiatedByParticipant(){
return this.isInitiatedByParticipant;
}
public void setIsInitiatedByParticipant(boolean isInitiatedByParticipant){
this.isInitiatedByParticipant = isInitiatedByParticipant;
}

public static final String REJECTION_NOTE = "rejectionNote";
@Column(name="REJECTIONNOTE", nullable=true, length=4000)
private String rejectionNote;
public String getRejectionNote(){
return this.rejectionNote;
}
public void setRejectionNote(String rejectionNote){
this.rejectionNote = rejectionNote;
}

public static final String REQUESTING_PARTICIPANT = "requestingParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTINGPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant requestingParticipant;
public ATSPRT_Participant getRequestingParticipant(){
return this.requestingParticipant;
}
public void setRequestingParticipant(ATSPRT_Participant requestingParticipant){
this.requestingParticipant = requestingParticipant;
}

public static final String REQUEST_REASON = "requestReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTREASONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "requestReasonProvider", keyProperty = "id")
private ATSRequestReason requestReason;
public ATSRequestReason getRequestReason(){
return this.requestReason;
}
public void setRequestReason(ATSRequestReason requestReason){
this.requestReason = requestReason;
}

public static final String SESSION = "session";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="SESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
private ATSBDI_Session session;
public ATSBDI_Session getSession(){
return this.session;
}
public void setSession(ATSBDI_Session session){
this.session = session;
}

public static final String PERIOD = "period";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PERIODID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodProvider", keyProperty = "id")
private ATSBDI_Period period;
public ATSBDI_Period getPeriod(){
return this.period;
}
public void setPeriod(ATSBDI_Period period){
this.period = period;
}

public static final String WINDOW_PARTICIPANT = "windowParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="WINDOWPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant windowParticipant;
public ATSPRT_Participant getWindowParticipant(){
return this.windowParticipant;
}
public void setWindowParticipant(ATSPRT_Participant windowParticipant){
this.windowParticipant = windowParticipant;
}

public static final String MESSAGE_TYPE = "messageType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="MESSAGETYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "windowMessageTypeProvider", keyProperty = "id")
private ATSMSG_Type messageType;
public ATSMSG_Type getMessageType(){
return this.messageType;
}
public void setMessageType(ATSMSG_Type messageType){
this.messageType = messageType;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "requestRReasonProvider", keyProperty = "id")
private ATSRequestRReason rejectionReason;
public ATSRequestRReason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSRequestRReason rejectionReason){
this.rejectionReason = rejectionReason;
}

public static final String PURPOSES = "purposes";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSWindowAdditionReqsPurposes", joinColumns = @JoinColumn(name = "WINDOWADDITIONREQS_ID"), inverseJoinColumns = @JoinColumn(name = "PURPOSES_ID"))
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private List<ATSMSG_CtgPurp> purposes = new ArrayList<ATSMSG_CtgPurp>();
public List<ATSMSG_CtgPurp> getPurposes(){
return this.purposes;
}
public void setPurposes(List<ATSMSG_CtgPurp> purposes){
this.purposes = purposes;
}

@Override
public String toString() {
return "ATSWindowAdditionReq [id= " + getId() + ", requestId= " + getRequestId() + ", periodStart= " + getPeriodStart() + ", periodEnd= " + getPeriodEnd() + ", name= " + getName() + ", duration= " + getDuration() + ", gracePeriod= " + getGracePeriod() + ", windowStart= " + getWindowStart() + ", windowEnd= " + getWindowEnd() + ", isInitiatedByParticipant= " + getIsInitiatedByParticipant() + ", rejectionNote= " + getRejectionNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getRequestId() == null) ? 0 : getRequestId().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
int GracePeriod= new Long("null".equals(getGracePeriod() + "") ? 0 : getGracePeriod()).intValue();
result = prime * result + (int) (GracePeriod ^ GracePeriod >>> 32);
result = prime * result + ((getRejectionNote() == null) ? 0 : getRejectionNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSWindowAdditionReq other = (ATSWindowAdditionReq) obj;
return this.hashCode() == other.hashCode();}
}


}