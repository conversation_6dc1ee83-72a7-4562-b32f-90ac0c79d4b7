package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jfw.model.bussinessobject.core.TimestampAdapter;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;

@Entity

@Table(name="ATSBDI_PeriodExtensions")
@XmlRootElement(name="BDI_PeriodExtensions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDI_PeriodExtension extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDI_PeriodExtension(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String ORIGIN_PERIOD_START = "originPeriodStart";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="ORIGINPERIODSTART", nullable=false, length=34)
private java.sql.Timestamp originPeriodStart;
public java.sql.Timestamp getOriginPeriodStart(){
return this.originPeriodStart;
}
public void setOriginPeriodStart(java.sql.Timestamp originPeriodStart){
this.originPeriodStart = originPeriodStart;
}

public static final String ORIGIN_PERIOD_END = "originPeriodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="ORIGINPERIODEND", nullable=false, length=34)
private java.sql.Timestamp originPeriodEnd;
public java.sql.Timestamp getOriginPeriodEnd(){
return this.originPeriodEnd;
}
public void setOriginPeriodEnd(java.sql.Timestamp originPeriodEnd){
this.originPeriodEnd = originPeriodEnd;
}

public static final String NEW_PERIOD_END = "newPeriodEnd";
@XmlJavaTypeAdapter(TimestampAdapter.class)
@Column(name="NEWPERIODEND", nullable=false, length=34)
private java.sql.Timestamp newPeriodEnd;
public java.sql.Timestamp getNewPeriodEnd(){
return this.newPeriodEnd;
}
public void setNewPeriodEnd(java.sql.Timestamp newPeriodEnd){
this.newPeriodEnd = newPeriodEnd;
}

public static final String EXTEND_WINDOWS = "extendWindows";
@Column(name="EXTENDWINDOWS", nullable=true, length=1)
private boolean extendWindows;
public boolean getExtendWindows(){
return this.extendWindows;
}
public void setExtendWindows(boolean extendWindows){
this.extendWindows = extendWindows;
}

public static final String IS_SYSTEM_REQUEST = "isSystemRequest";
@Column(name="ISSYSTEMREQUEST", nullable=true, length=1)
private boolean isSystemRequest;
public boolean getIsSystemRequest(){
return this.isSystemRequest;
}
public void setIsSystemRequest(boolean isSystemRequest){
this.isSystemRequest = isSystemRequest;
}

public static final String REF_SESSION = "refSession";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodExtensionSessionProvider", keyProperty = "id")
private ATSBDI_Session refSession;
public ATSBDI_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDI_Session refSession){
this.refSession = refSession;
}

public static final String REF_PERIOD = "refPeriod";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPERIODID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodExtensionPeriodProvider", keyProperty = "id")
private ATSBDI_Period refPeriod;
public ATSBDI_Period getRefPeriod(){
return this.refPeriod;
}
public void setRefPeriod(ATSBDI_Period refPeriod){
this.refPeriod = refPeriod;
}

public static final String REQUEST_PARTICIPANT = "requestParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodManagementReqPrtProvider", keyProperty = "id")
private ATSPRT_Participant requestParticipant;
public ATSPRT_Participant getRequestParticipant(){
return this.requestParticipant;
}
public void setRequestParticipant(ATSPRT_Participant requestParticipant){
this.requestParticipant = requestParticipant;
}

@Override
public String toString() {
return "ATSBDI_PeriodExtension [id= " + getId() + ", originPeriodStart= " + getOriginPeriodStart() + ", originPeriodEnd= " + getOriginPeriodEnd() + ", newPeriodEnd= " + getNewPeriodEnd() + ", extendWindows= " + getExtendWindows() + ", isSystemRequest= " + getIsSystemRequest() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDI_PeriodExtension other = (ATSBDI_PeriodExtension) obj;
return this.hashCode() == other.hashCode();}
}


}