package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRpt_NCP_Management",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"SESSIONID","Z_TENANT_ID"})
})
@XmlRootElement(name="Rpt_NCP_Management")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_NCP_Management extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRpt_NCP_Management(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String MESSAGE_ID = "messageId";
@Column(name="MESSAGEID", nullable=true, length=50)
private String messageId;
public String getMessageId(){
return this.messageId;
}
public void setMessageId(String messageId){
this.messageId = messageId;
}

public static final String TRANSACTION_ID = "transactionId";
@Column(name="TRANSACTIONID", nullable=true, length=35)
private String transactionId;
public String getTransactionId(){return this.transactionId;}
public void setTransactionId(String transactionId){
		this.transactionId = transactionId;
	}

public static final String SESSION = "session";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="SESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "sessionSequenceProvider", keyProperty = "id")
private ATSBDI_Session session;
public ATSBDI_Session getSession(){
return this.session;
}
public void setSession(ATSBDI_Session session){
this.session = session;
}

public static final String IS_POSTED = "isPosted";
@Column(name="ISPOSTED", length=1)
private boolean isPosted = false;
public boolean getIsPosted(){ return this.isPosted;}
public void setIsPosted(boolean isPosted){ this.isPosted = isPosted;}

@Override
public String toString() {
return "ATSRpt_NCP_Management [id= " + getId() + ", messageId= " + getMessageId() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getMessageId() == null) ? 0 : getMessageId().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRpt_NCP_Management other = (ATSRpt_NCP_Management) obj;
return this.hashCode() == other.hashCode();}
}


}