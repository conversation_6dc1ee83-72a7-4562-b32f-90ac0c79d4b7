package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSBDS_BusinessDay")
@XmlRootElement(name="BDS_BusinessDay")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSBDS_BusinessDay extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_BusinessDay(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String BUSINESS_DT = "businessDt";
@Column(name="BUSINESSDT", nullable=false, length=50)
@Temporal(TemporalType.DATE)
private java.util.Date businessDt;
public java.util.Date getBusinessDt(){
return this.businessDt;
}
public void setBusinessDt(java.util.Date businessDt){
this.businessDt = businessDt;
}

@Override
public String toString() {
return "ATSBDS_BusinessDay [id= " + getId() + ", businessDt= " + getBusinessDt() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getBusinessDt() == null) ? 0 : getBusinessDt().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_BusinessDay other = (ATSBDS_BusinessDay) obj;
return this.hashCode() == other.hashCode();}
}


}