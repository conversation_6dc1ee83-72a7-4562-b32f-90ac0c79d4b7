package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.attachments.AttachmentItem;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import java.io.Serializable;

@Entity
@Table(name = "ATSRpt_PSRATT")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRpt_PSRAtt extends AttachmentItem implements Serializable {

    public ATSRpt_PSRAtt() {
    }

}
