package com.progressoft.ach.entities;

public class WF_BranchCreationReqs
 {
    // Steps
    public static final String STEP_Initialization = "1000001";
    public static final String STEP_NEW = "1000002";
    public static final String STEP_APPROVE_AUTHORIZATION = "1000003";
    public static final String STEP_REJECT_AUTHORIZATION = "1000004";
    public static final String STEP_REPAIR = "1000005";
    public static final String STEP_APPROVED = "1000006";
    public static final String STEP_REJECTED = "1000007";
    // Statuses
    public static final String STEP_STATUS_Initialization = "";
    public static final String STEP_STATUS_NEW = "New";
    public static final String STEP_STATUS_APPROVE_AUTHORIZATION = "Approve Authorization";
    public static final String STEP_STATUS_REJECT_AUTHORIZATION = "Reject Authorization";
    public static final String STEP_STATUS_REPAIR = "Repair";
    public static final String STEP_STATUS_APPROVED = "Approved";
    public static final String STEP_STATUS_REJECTED = "Rejected";
    // Action Names
    public static final String ACTION_NAME_Initialize = "Initialize";
    public static final String ACTION_NAME_Create = "Create";
    public static final String ACTION_NAME_Approve = "Approve";
    public static final String ACTION_NAME_Reject = "Reject";
    public static final String ACTION_NAME_Cancel = "Cancel";
    public static final String ACTION_NAME_Repair = "Repair";
    // Action Codes
    public static final String ACTION_CODE_InitializeInitialization = "1";
    public static final String ACTION_CODE_CreateInitialization = "2";
    public static final String ACTION_CODE_ApproveNEW = "3";
    public static final String ACTION_CODE_RejectNEW = "4";
    public static final String ACTION_CODE_CancelNEW = "5";
    public static final String ACTION_CODE_ApproveAPPROVE_AUTHORIZATION = "6";
    public static final String ACTION_CODE_RejectAPPROVE_AUTHORIZATION = "7";
    public static final String ACTION_CODE_CancelAPPROVE_AUTHORIZATION = "8";
    public static final String ACTION_CODE_ApproveREJECT_AUTHORIZATION = "9";
    public static final String ACTION_CODE_RepairREJECT_AUTHORIZATION = "10";
    public static final String ACTION_CODE_CancelREJECT_AUTHORIZATION = "11";
    public static final String ACTION_CODE_ApproveREPAIR = "12";
    public static final String ACTION_CODE_RejectREPAIR = "13";
    public static final String ACTION_CODE_CancelREPAIR = "14";
    // Action Keys
    public static final String ACTION_KEY_InitializeInitialization = "10";
    public static final String ACTION_KEY_CreateInitialization = "1";
    public static final String ACTION_KEY_ApproveNEW = "2046458437";
    public static final String ACTION_KEY_RejectNEW = "2112402189";
    public static final String ACTION_KEY_CancelNEW = "620825636";
    public static final String ACTION_KEY_ApproveAPPROVE_AUTHORIZATION = "699689513";
    public static final String ACTION_KEY_RejectAPPROVE_AUTHORIZATION = "1844546914";
    public static final String ACTION_KEY_CancelAPPROVE_AUTHORIZATION = "1473906021";
    public static final String ACTION_KEY_ApproveREJECT_AUTHORIZATION = "116724737";
    public static final String ACTION_KEY_RepairREJECT_AUTHORIZATION = "496434707";
    public static final String ACTION_KEY_CancelREJECT_AUTHORIZATION = "575118441";
    public static final String ACTION_KEY_ApproveREPAIR = "417690623";
    public static final String ACTION_KEY_RejectREPAIR = "458971495";
    public static final String ACTION_KEY_CancelREPAIR = "626202005";

}