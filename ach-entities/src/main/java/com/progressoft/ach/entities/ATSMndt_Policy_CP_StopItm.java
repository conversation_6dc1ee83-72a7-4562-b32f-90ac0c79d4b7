package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlTransient;
import java.io.Serializable;

@Entity
@Table(name="ATSMndt_Policy_CP_StopITM")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMndt_Policy_CP_StopItm extends ChangeItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_CP_StopItm(){/*Default Constructor*/}

@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="G<PERSON><PERSON><PERSON>", nullable=true)
@XmlTransient
protected ATSMndt_Policy_CP_StopGrp changeGroup;

@Override
public void setChangeGroup(ChangeGroup changeGroup) {
if(changeGroup instanceof ATSMndt_Policy_CP_StopGrp){
this.changeGroup = (ATSMndt_Policy_CP_StopGrp) changeGroup;
}
}

@Override
public ChangeGroup getChangeGroup() {
return this.changeGroup;
}


}