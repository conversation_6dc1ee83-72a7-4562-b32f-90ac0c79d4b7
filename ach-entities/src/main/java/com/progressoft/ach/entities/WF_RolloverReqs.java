package com.progressoft.ach.entities;

public class WF_RolloverReqs
 {
    // Steps
    public static final String STEP_Initialization = "4000001";
    public static final String STEP_NEW = "4000002";
    public static final String STEP_NEW_AUTHORIZATION = "4000003";
    public static final String STEP_REPAIR = "4000004";
    public static final String STEP_REPAIR_AUTHORIZATION = "4000005";
    public static final String STEP_DELETE_AUTHORIZATION = "4000006";
    public static final String STEP_P_NEW = "4000007";
    public static final String STEP_P_APPROVE_AUTHORIZATION = "4000008";
    public static final String STEP_P_REJECT_AUTHORIZATION = "4000009";
    public static final String STEP_P_REPAIR = "4000010";
    public static final String STEP_DELETED = "4000011";
    public static final String STEP_APPROVED = "4000012";
    public static final String STEP_REJECTED = "4000013";
    // Statuses
    public static final String STEP_STATUS_Initialization = "";
    public static final String STEP_STATUS_NEW = "New";
    public static final String STEP_STATUS_NEW_AUTHORIZATION = "New Authorization";
    public static final String STEP_STATUS_REPAIR = "Repair";
    public static final String STEP_STATUS_REPAIR_AUTHORIZATION = "Repair Authorization";
    public static final String STEP_STATUS_DELETE_AUTHORIZATION = "Delete Authorization";
    public static final String STEP_STATUS_P_NEW = "New";
    public static final String STEP_STATUS_P_APPROVE_AUTHORIZATION = "Approve Authorization";
    public static final String STEP_STATUS_P_REJECT_AUTHORIZATION = "Reject Authorization";
    public static final String STEP_STATUS_P_REPAIR = "Repair";
    public static final String STEP_STATUS_DELETED = "Deleted";
    public static final String STEP_STATUS_APPROVED = "Approved";
    public static final String STEP_STATUS_REJECTED = "Rejected";
    // Action Names
    public static final String ACTION_NAME_Initialize = "Initialize";
    public static final String ACTION_NAME_Create = "Create";
    public static final String ACTION_NAME_Submit = "Submit";
    public static final String ACTION_NAME_Save = "Save";
    public static final String ACTION_NAME_Delete = "Delete";
    public static final String ACTION_NAME_Cancel = "Cancel";
    public static final String ACTION_NAME_SVC_NEW = "SVC_NEW";
    public static final String ACTION_NAME_Approve = "Approve";
    public static final String ACTION_NAME_Reject = "Reject";
    public static final String ACTION_NAME_Repair = "Repair";
    // Action Codes
    public static final String ACTION_CODE_InitializeInitialization = "1";
    public static final String ACTION_CODE_CreateInitialization = "2";
    public static final String ACTION_CODE_SubmitNEW = "3";
    public static final String ACTION_CODE_SaveNEW = "4";
    public static final String ACTION_CODE_DeleteNEW = "5";
    public static final String ACTION_CODE_CancelNEW = "6";
    public static final String ACTION_CODE_SVC_NEWNEW = "7";
    public static final String ACTION_CODE_ApproveNEW_AUTHORIZATION = "8";
    public static final String ACTION_CODE_RejectNEW_AUTHORIZATION = "9";
    public static final String ACTION_CODE_RepairNEW_AUTHORIZATION = "10";
    public static final String ACTION_CODE_CancelNEW_AUTHORIZATION = "11";
    public static final String ACTION_CODE_SubmitREPAIR = "12";
    public static final String ACTION_CODE_SaveREPAIR = "13";
    public static final String ACTION_CODE_DeleteREPAIR = "14";
    public static final String ACTION_CODE_CancelREPAIR = "15";
    public static final String ACTION_CODE_ApproveREPAIR_AUTHORIZATION = "16";
    public static final String ACTION_CODE_RejectREPAIR_AUTHORIZATION = "17";
    public static final String ACTION_CODE_RepairREPAIR_AUTHORIZATION = "18";
    public static final String ACTION_CODE_CancelREPAIR_AUTHORIZATION = "19";
    public static final String ACTION_CODE_ApproveDELETE_AUTHORIZATION = "20";
    public static final String ACTION_CODE_RejectDELETE_AUTHORIZATION = "21";
    public static final String ACTION_CODE_CancelDELETE_AUTHORIZATION = "22";
    public static final String ACTION_CODE_ApproveP_NEW = "23";
    public static final String ACTION_CODE_RejectP_NEW = "24";
    public static final String ACTION_CODE_CancelP_NEW = "25";
    public static final String ACTION_CODE_ApproveP_APPROVE_AUTHORIZATION = "26";
    public static final String ACTION_CODE_RejectP_APPROVE_AUTHORIZATION = "27";
    public static final String ACTION_CODE_CancelP_APPROVE_AUTHORIZATION = "28";
    public static final String ACTION_CODE_ApproveP_REJECT_AUTHORIZATION = "29";
    public static final String ACTION_CODE_RepairP_REJECT_AUTHORIZATION = "30";
    public static final String ACTION_CODE_CancelP_REJECT_AUTHORIZATION = "31";
    public static final String ACTION_CODE_ApproveP_REPAIR = "32";
    public static final String ACTION_CODE_RejectP_REPAIR = "33";
    public static final String ACTION_CODE_CancelP_REPAIR = "34";
    // Action Keys
    public static final String ACTION_KEY_InitializeInitialization = "10";
    public static final String ACTION_KEY_CreateInitialization = "1";
    public static final String ACTION_KEY_SubmitNEW = "2079353333";
    public static final String ACTION_KEY_SaveNEW = "1030624283";
    public static final String ACTION_KEY_DeleteNEW = "2056884579";
    public static final String ACTION_KEY_CancelNEW = "684481433";
    public static final String ACTION_KEY_SVC_NEWNEW = "2002421534";
    public static final String ACTION_KEY_ApproveNEW_AUTHORIZATION = "601882871";
    public static final String ACTION_KEY_RejectNEW_AUTHORIZATION = "406664967";
    public static final String ACTION_KEY_RepairNEW_AUTHORIZATION = "697952380";
    public static final String ACTION_KEY_CancelNEW_AUTHORIZATION = "693788586";
    public static final String ACTION_KEY_SubmitREPAIR = "1332813159";
    public static final String ACTION_KEY_SaveREPAIR = "1086199237";
    public static final String ACTION_KEY_DeleteREPAIR = "2110265780";
    public static final String ACTION_KEY_CancelREPAIR = "1270139470";
    public static final String ACTION_KEY_ApproveREPAIR_AUTHORIZATION = "1764736064";
    public static final String ACTION_KEY_RejectREPAIR_AUTHORIZATION = "2105937494";
    public static final String ACTION_KEY_RepairREPAIR_AUTHORIZATION = "275729150";
    public static final String ACTION_KEY_CancelREPAIR_AUTHORIZATION = "1374367724";
    public static final String ACTION_KEY_ApproveDELETE_AUTHORIZATION = "1483798289";
    public static final String ACTION_KEY_RejectDELETE_AUTHORIZATION = "1497373988";
    public static final String ACTION_KEY_CancelDELETE_AUTHORIZATION = "1528327241";
    public static final String ACTION_KEY_ApproveP_NEW = "1936317162";
    public static final String ACTION_KEY_RejectP_NEW = "34291821";
    public static final String ACTION_KEY_CancelP_NEW = "422643118";
    public static final String ACTION_KEY_ApproveP_APPROVE_AUTHORIZATION = "1900585380";
    public static final String ACTION_KEY_RejectP_APPROVE_AUTHORIZATION = "1853223223";
    public static final String ACTION_KEY_CancelP_APPROVE_AUTHORIZATION = "1880071398";
    public static final String ACTION_KEY_ApproveP_REJECT_AUTHORIZATION = "489331315";
    public static final String ACTION_KEY_RepairP_REJECT_AUTHORIZATION = "1088939111";
    public static final String ACTION_KEY_CancelP_REJECT_AUTHORIZATION = "1499628282";
    public static final String ACTION_KEY_ApproveP_REPAIR = "1288383586";
    public static final String ACTION_KEY_RejectP_REPAIR = "325968737";
    public static final String ACTION_KEY_CancelP_REPAIR = "885062671";

}