package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.Objects;

@Entity

@Table(name="ACH_INTG_SETTLEMENT",
uniqueConstraints=
{
@UniqueConstraint(columnNames={"WNDO_ID","STL_DATE","CURR_CD","AGNT_ACC","AGNT_ID","Z_TENANT_ID"})
})
@XmlRootElement(name="ACH_INTG_SETTLEMENT")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ACH_INTG_SETTLEMENT extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ACH_INTG_SETTLEMENT(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

@Column(name="WNDO_ID", nullable=true, length=16)
private String WNDO_ID;
public String getWNDOID(){
return this.WNDO_ID;
}
public void setWNDOID(String WNDO_ID){
this.WNDO_ID = WNDO_ID;
}

@Column(name="STL_DATE", nullable=true, length=16)
@Temporal(TemporalType.DATE)
private java.util.Date STL_DATE;
public java.util.Date getSTLDATE(){
return this.STL_DATE;
}
public void setSTLDATE(java.util.Date STL_DATE){
this.STL_DATE = STL_DATE;
}

@Column(name="AGNT_ID", nullable=true, length=16)
private String AGNT_ID;
public String getAGNTID(){
return this.AGNT_ID;
}
public void setAGNTID(String AGNT_ID){
this.AGNT_ID = AGNT_ID;
}

@Column(name="AGNT_ACC", nullable=true, length=16)
private String AGNT_ACC;
public String getAGNTACC(){
return this.AGNT_ACC;
}
public void setAGNTACC(String AGNT_ACC){
this.AGNT_ACC = AGNT_ACC;
}

@Column(name="CURR_CD", nullable=true, length=16)
private String CURR_CD;
public String getCURRCD(){
return this.CURR_CD;
}
public void setCURRCD(String CURR_CD){
this.CURR_CD = CURR_CD;
}

@Column(name="TOTAL_DEBIT_AMOUNT", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal TOTAL_DEBIT_AMOUNT;
public java.math.BigDecimal getTOTALDEBITAMOUNT(){
return this.TOTAL_DEBIT_AMOUNT;
}
public void setTOTALDEBITAMOUNT(java.math.BigDecimal TOTAL_DEBIT_AMOUNT){
this.TOTAL_DEBIT_AMOUNT = TOTAL_DEBIT_AMOUNT;
}

@Column(name="TOTAL_CREDIT_AMOUNT", nullable=true, precision=14, scale=5, length=16)
private java.math.BigDecimal TOTAL_CREDIT_AMOUNT;
public java.math.BigDecimal getTOTALCREDITAMOUNT(){
return this.TOTAL_CREDIT_AMOUNT;
}
public void setTOTALCREDITAMOUNT(java.math.BigDecimal TOTAL_CREDIT_AMOUNT){
this.TOTAL_CREDIT_AMOUNT = TOTAL_CREDIT_AMOUNT;
}

@Column(name="CRT_DT", nullable=true, length=16)
@Temporal(TemporalType.DATE)
private java.util.Date CRT_DT;
public java.util.Date getCRTDT(){
return this.CRT_DT;
}
public void setCRTDT(java.util.Date CRT_DT){
this.CRT_DT = CRT_DT;
}

@Override
public String toString() {
return "ACH_INTG_SETTLEMENT [id= " + getId() + ", WNDO_ID= " + getWNDOID() + ", STL_DATE= " + getSTLDATE() + ", AGNT_ID= " + getAGNTID() + ", AGNT_ACC= " + getAGNTACC() + ", CURR_CD= " + getCURRCD() + ", TOTAL_DEBIT_AMOUNT= " + getTOTALDEBITAMOUNT() + ", TOTAL_CREDIT_AMOUNT= " + getTOTALCREDITAMOUNT() + ", CRT_DT= " + getCRTDT() + "]";
}

 @Override
 public int hashCode() {
     return Objects.hash(id, WNDO_ID, STL_DATE, AGNT_ID, AGNT_ACC, CURR_CD, TOTAL_DEBIT_AMOUNT, TOTAL_CREDIT_AMOUNT, CRT_DT);
 }

    @Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ACH_INTG_SETTLEMENT other = (ACH_INTG_SETTLEMENT) obj;
return this.hashCode() == other.hashCode();}
}


}