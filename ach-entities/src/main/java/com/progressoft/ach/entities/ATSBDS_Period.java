package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.List;

@Entity

@Table(name="ATSBDS_Periods")
@XmlRootElement(name="BDS_Periods")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDS_Period extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_Period(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String DURATION = "duration";
@Column(name="DURATION", nullable=false, length=20)
private long duration;
public long getDuration(){
return this.duration;
}
public void setDuration(long duration){
this.duration = duration;
}

public static final String ALLOW_WINDOWS = "allowWindows";
@Column(name="ALLOWWINDOWS", nullable=false, length=1)
private boolean allowWindows;
public boolean getAllowWindows(){
return this.allowWindows;
}
public void setAllowWindows(boolean allowWindows){
this.allowWindows = allowWindows;
}

public static final String ALLOW_ACTIONS = "allowActions";
@Column(name="ALLOWACTIONS", nullable=false, length=1)
private boolean allowActions;
public boolean getAllowActions(){
return this.allowActions;
}
public void setAllowActions(boolean allowActions){
this.allowActions = allowActions;
}

public static final String REF_SESSION = "refSession";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFSESSIONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "bdsSessionProvider", keyProperty = "id")
private ATSBDS_Session refSession;
public ATSBDS_Session getRefSession(){
return this.refSession;
}
public void setRefSession(ATSBDS_Session refSession){
this.refSession = refSession;
}

public static final String PERIOD_TYPE = "periodType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PERIODTYPEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "periodTypeProvider", keyProperty = "id")
private ATSBDS_PeriodType periodType;
public ATSBDS_PeriodType getPeriodType(){
return this.periodType;
}
public void setPeriodType(ATSBDS_PeriodType periodType){
this.periodType = periodType;
}

public static final String REF_PERIOD_B_D_S_WINDOWS = "refPeriodBDS_Windows";
@OneToMany(mappedBy = "refPeriod")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@WithValueProvider(jupiterValueProviderBean = "bdsWindowProvider", keyProperty = "id")
private List<ATSBDS_Window> refPeriodBDS_Windows;
public List<ATSBDS_Window> getRefPeriodBDSWindows(){
return this.refPeriodBDS_Windows;
}
public void setRefPeriodBDSWindows(List<ATSBDS_Window> refPeriodBDS_Windows){
this.refPeriodBDS_Windows = refPeriodBDS_Windows;
}

public static final String REF_PERIOD_B_D_I_PERIODS = "refPeriodBDI_Periods";
@OneToMany(mappedBy = "refPeriod")
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@WithValueProvider(jupiterValueProviderBean = "periodProvider", keyProperty = "id")
private List<ATSBDI_Period> refPeriodBDI_Periods;
public List<ATSBDI_Period> getRefPeriodBDIPeriods(){
return this.refPeriodBDI_Periods;
}
public void setRefPeriodBDIPeriods(List<ATSBDI_Period> refPeriodBDI_Periods){
this.refPeriodBDI_Periods = refPeriodBDI_Periods;
}

@Override
public String toString() {
return "ATSBDS_Period [id= " + getId() + ", name= " + getName() + ", description= " + getDescription() + ", duration= " + getDuration() + ", allowWindows= " + getAllowWindows() + ", allowActions= " + getAllowActions() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int Duration= new Long("null".equals(getDuration() + "") ? 0 : getDuration()).intValue();
result = prime * result + (int) (Duration ^ Duration >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_Period other = (ATSBDS_Period) obj;
return this.hashCode() == other.hashCode();}
}


}