package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.sql.Timestamp;

@Entity
@Table(name="ATSKassipTimetableReqs",
uniqueConstraints=
{
    @UniqueConstraint(columnNames={"REQUEST_ID","Z_TENANT_ID"})
})
@XmlRootElement
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSKassipTimetableReq extends JFWEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    private static final int REQUEST_ID_LENGTH = 16;
    @Id
    @SequenceGenerator(name = "SEQ_ATSKASSIPTIMETABLEREQS", sequenceName = "SEQ_ATSKASSIPTIMETABLEREQS", allocationSize = 1)
    @GeneratedValue(generator = "SEQ_ATSKASSIPTIMETABLEREQS")
    @Column(name = "ID", nullable = false)
    private Long id;

    @Column(name = "REQUEST_ID", nullable = false)
    private String requestId;

    public ATSKassipTimetableReq() {
        super();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    @Override
    public String toString() {
        return "ATSKassipTimetableReq{" +
                "id=" + getId() +
                ", requestId='" + requestId + '\'' +
                ", creationDate=" + getCreationDate() +
                '}';
    }
}