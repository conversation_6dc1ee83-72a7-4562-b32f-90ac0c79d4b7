package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSRptMisPerformance")
@XmlRootElement(name="RptMisPerformance")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSRptMisPerformance extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSRptMisPerformance(){/*Default Constructor*/}

    public static final String ID = "id";
    @Id
    @GeneratedValue(strategy=GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    public long getId(){
        return this.id;
    }
    public void setId(long id){
        this.id = id;
    }

    public static final String FOR_DATE = "forDate";
    @Column(name="FORDATE", nullable=false, length=20)
    @Temporal(TemporalType.DATE)
    private java.util.Date forDate;
    public java.util.Date getForDate(){
        return this.forDate;
    }
    public void setForDate(java.util.Date forDate){
        this.forDate = forDate;
    }

    public static final String FROM_HOUR = "fromHour";
    @Column(name="FROMHOUR", nullable=false, length=2)
    private String fromHour;
    public String getFromHour(){
        return this.fromHour;
    }
    public void setFromHour(String fromHour){
        this.fromHour = fromHour;
    }

    public static final String TO_HOUR = "toHour";
    @Column(name="TOHOUR", nullable=false, length=2)
    private String toHour;
    public String getToHour(){
        return this.toHour;
    }
    public void setToHour(String toHour){
        this.toHour = toHour;
    }

    public static final String TIME_SLICE = "timeSlice";
    @Column(name="TIMESLICE", nullable=true, length=256)
    @Transient
    private String timeSlice;
    public String getTimeSlice(){
        return this.timeSlice;
    }
    public void setTimeSlice(String timeSlice){
        this.timeSlice = timeSlice;
    }

    public static final String TOTAL_TRANSACTIONS = "totalTransactions";
    @Column(name="TOTALTRANSACTIONS", nullable=true, length=256)
    @Transient
    private String totalTransactions;
    public String getTotalTransactions(){
        return this.totalTransactions;
    }
    public void setTotalTransactions(String totalTransactions){
        this.totalTransactions = totalTransactions;
    }

    public static final String PARTICIPANT = "participant";
    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="PARTICIPANTID", nullable=false)
    @WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
    private com.progressoft.ach.entities.ATSPRT_Participant participant;
    public com.progressoft.ach.entities.ATSPRT_Participant getParticipant(){
        return this.participant;
    }
    public void setParticipant(com.progressoft.ach.entities.ATSPRT_Participant participant){
        this.participant = participant;
    }

    @Override
    public String toString() {
        return "ATSRptMisPerformance [id= " + getId() + ", forDate= " + getForDate() + ", fromHour= " + getFromHour() + ", toHour= " + getToHour() + ", timeSlice= " + getTimeSlice() + ", totalTransactions= " + getTotalTransactions() + "]";
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
        result = prime * result + (int) (Id ^ Id >>> 32);
        result = prime * result + ((getForDate() == null) ? 0 : getForDate().hashCode());
        result = prime * result + ((getFromHour() == null) ? 0 : getFromHour().hashCode());
        result = prime * result + ((getToHour() == null) ? 0 : getToHour().hashCode());
        result = prime * result + ((getTimeSlice() == null) ? 0 : getTimeSlice().hashCode());
        result = prime * result + ((getTotalTransactions() == null) ? 0 : getTotalTransactions().hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        else if (obj == null)
            return false;
        else if (getClass() != obj.getClass())
            return false;
        else {ATSRptMisPerformance other = (ATSRptMisPerformance) obj;
            return this.hashCode() == other.hashCode();}
    }


}