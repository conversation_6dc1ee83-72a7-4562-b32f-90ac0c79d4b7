package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlTransient;
import java.io.Serializable;

@Entity
@Table(name="ATSUnwindRequestITM")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSUnwindRequestItm extends ChangeItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSUnwindRequestItm(){/*Default Constructor*/}

@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="GROUPID", nullable=true)
@XmlTransient
protected ATSUnwindRequestGrp changeGroup;

@Override
public void setChangeGroup(ChangeGroup changeGroup) {
if(changeGroup instanceof ATSUnwindRequestGrp){
this.changeGroup = (ATSUnwindRequestGrp) changeGroup;
}
}

@Override
public ChangeGroup getChangeGroup() {
return this.changeGroup;
}


}