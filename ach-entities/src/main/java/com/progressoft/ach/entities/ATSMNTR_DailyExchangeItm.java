package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeGroup;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeItem;

import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlTransient;
import java.io.Serializable;

@Entity
@Table(name="ATSMNTR_DailyExchangeITM")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSMNTR_DailyExchangeItm extends ChangeItem implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMNTR_DailyExchangeItm(){/*Default Constructor*/}

@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="GROUP<PERSON>", nullable=true)
@XmlTransient
protected ATSMNTR_DailyExchangeGrp changeGroup;

@Override
public void setChangeGroup(ChangeGroup changeGroup) {
if(changeGroup instanceof ATSMNTR_DailyExchangeGrp){
this.changeGroup = (ATSMNTR_DailyExchangeGrp) changeGroup;
}
}

@Override
public ChangeGroup getChangeGroup() {
return this.changeGroup;
}


}