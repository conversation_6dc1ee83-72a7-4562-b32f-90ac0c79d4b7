package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.search.SearchType;
import com.progressoft.jupiter.annotation.search.Searchable;
import com.progressoft.jupiter.annotation.WithValueProvider;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.List;

@Entity
@Table(name="ATSRecon_ScheduledTimes")
@XmlRootElement(name="ATSRecon_ScheduledTimes")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy.READ_WRITE )
@Searchable({
        @Searchable.SearchableField(fieldPath = "scheduledSession.name", label = "ATSBDS_Session", searchType = SearchType.LIKE)
})
public class ATSRecon_ScheduledTimes extends JFWEntity implements Serializable {

    @Id
    @GeneratedValue(strategy= GenerationType.AUTO)
    @Column(name="ID", nullable=false, insertable=false)
    private long id;

    @ManyToOne
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @JoinColumn(name="SCHEDULEDSESSION", nullable=false)
    @WithValueProvider(jupiterValueProviderBean = "scheduledSessionValueProvider", keyProperty = "id")
    private ATSBDS_Session scheduledSession;

    @OneToMany(mappedBy = "reconScheduledTimesId")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    private List<ATSRecon_ScheduledTimesSub> reconScheduledTimesId;



    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public ATSBDS_Session getScheduledSession() {
        return scheduledSession;
    }

    public void setScheduledSession(ATSBDS_Session scheduledSession) {
        this.scheduledSession = scheduledSession;
    }

    public List<ATSRecon_ScheduledTimesSub> getReconScheduledTimesId() {
        return reconScheduledTimesId;
    }

    public void setReconScheduledTimesId(List<ATSRecon_ScheduledTimesSub> reconScheduledTimesId) {
        this.reconScheduledTimesId = reconScheduledTimesId;
    }
}