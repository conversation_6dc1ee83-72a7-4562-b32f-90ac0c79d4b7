package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;

import java.io.Serializable;
import java.util.Objects;

@Entity
@Table(name="ATS_Notifications")
@XmlRootElement(name="ATS_Notifications")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSNotification extends JFWEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    public ATSNotification(){}

    public static final String ID = "id";
    @Id
    @SequenceGenerator(name = "SEQ_ATSNotifications", sequenceName = "SEQ_ATSNotifications", allocationSize = 1)
    @GeneratedValue(generator = "SEQ_ATSNotifications")
    @Column(name="ID", nullable=false, insertable=false)
    private long id;
    public long getId(){
        return this.id;
    }
    public void setId(long id){
        this.id = id;
    }

    @Column(name = "TYPE", nullable = false)
    private String type;
    public String getType(){
        return this.type;
    }
    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "MESSAGEID", nullable = false)
    private String messageId;
    public String getMessageId(){
        return this.messageId;
    }
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    @Column(name = "PARTICIPANT_CODE", nullable = false)
    private String participantCode;
    public String getParticipantCode() {
        return participantCode;
    }
    public void setParticipantCode(String participantCode) {
        this.participantCode = participantCode;
    }

    @Column(name="PULLED_FLAG", nullable = false)
    private Boolean isPulled;
    public Boolean getPulled() {
        return isPulled;
    }
    public void setPulled(Boolean pulled) {
        isPulled = pulled;
    }

    @Column(name = "CONTENT", nullable=false)
    private String messageContent;
    public String getMessageContent() {
        return messageContent;
    }
    public void setMessageContent(String messageContent) {
        this.messageContent = messageContent;
    }

    @Column(name = "MESSAGETYPE", nullable=false)
    private String messageType;
    public String getMessageType() {
        return messageType;
    }
    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ATSNotification that = (ATSNotification) o;
        return id == that.id && Objects.equals(type, that.type) && Objects.equals(participantCode, that.participantCode) && Objects.equals(isPulled, that.isPulled) && Objects.equals(messageContent, that.messageContent) && Objects.equals(messageType, that.messageType);
    }

    @Override
    public String toString() {
        return "SessionPeriodChangeNotification{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", participantCode='" + participantCode + '\'' +
                ", isPulled=" + isPulled +
                ", messageContent='" + messageContent + '\'' +
                ", messageType='" + messageType + '\'' +
                '}';
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, type, participantCode, isPulled, messageContent, messageType);
    }
}
