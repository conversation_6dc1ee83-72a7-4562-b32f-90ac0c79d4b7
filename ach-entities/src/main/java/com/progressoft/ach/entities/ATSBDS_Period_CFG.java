package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSBDS_Period_CFGs",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="BDS_Period_CFGs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBDS_Period_CFG extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBDS_Period_CFG(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String NAME = "name";
@Column(name="NAME", nullable=false, length=255)
private String name;
public String getName(){
return this.name;
}
public void setName(String name){
this.name = name;
}

public static final String DESCRIPTION = "description";
@Column(name="DESCRIPTION", nullable=true, length=255)
private String description;
public String getDescription(){
return this.description;
}
public void setDescription(String description){
this.description = description;
}

public static final String ALLOW_WINDOWS = "allowWindows";
@Column(name="ALLOWWINDOWS", nullable=false, length=1)
private boolean allowWindows;
public boolean getAllowWindows(){
return this.allowWindows;
}
public void setAllowWindows(boolean allowWindows){
this.allowWindows = allowWindows;
}

public static final String ALLOW_ACTIONS = "allowActions";
@Column(name="ALLOWACTIONS", nullable=false, length=1)
private boolean allowActions;
public boolean getAllowActions(){
return this.allowActions;
}
public void setAllowActions(boolean allowActions){
this.allowActions = allowActions;
}

public static final String DURATION = "duration";
@Column(name="DURATION", nullable=false, length=20)
private long duration;
public long getDuration(){
return this.duration;
}
public void setDuration(long duration){
this.duration = duration;
}

public static final String PERIOD_TYPE = "periodType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="PERIODTYPEID", nullable=false)
private ATSBDS_PeriodType periodType;
public ATSBDS_PeriodType getPeriodType(){
return this.periodType;
}
public void setPeriodType(ATSBDS_PeriodType periodType){
this.periodType = periodType;
}

@Override
public String toString() {
return "ATSBDS_Period_CFG [id= " + getId() + ", name= " + getName() + ", description= " + getDescription() + ", allowWindows= " + getAllowWindows() + ", allowActions= " + getAllowActions() + ", duration= " + getDuration() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
int Duration= new Long("null".equals(getDuration() + "") ? 0 : getDuration()).intValue();
result = prime * result + (int) (Duration ^ Duration >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBDS_Period_CFG other = (ATSBDS_Period_CFG) obj;
return this.hashCode() == other.hashCode();}
}


}