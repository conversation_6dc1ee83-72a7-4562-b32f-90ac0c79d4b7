package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSPRT_LimitsAmounts",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"LIMITSPROFILEID","CURRENCYID","MSGTYPEID","Z_TENANT_ID"})
})
@XmlRootElement(name="PRT_LimitsAmounts")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSPRT_LimitsAmount extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSPRT_LimitsAmount(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String MIN_AMOUNT = "minAmount";
@Column(name="MINAMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal minAmount;
public java.math.BigDecimal getMinAmount(){
return this.minAmount;
}
public void setMinAmount(java.math.BigDecimal minAmount){
this.minAmount = minAmount;
}

public static final String MAX_AMOUNT = "maxAmount";
@Column(name="MAXAMOUNT", nullable=false, precision=14, scale=5, length=16)
private java.math.BigDecimal maxAmount;
public java.math.BigDecimal getMaxAmount(){
return this.maxAmount;
}
public void setMaxAmount(java.math.BigDecimal maxAmount){
this.maxAmount = maxAmount;
}

public static final String LIMITS_PROFILE = "limitsProfile";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="LIMITSPROFILEID", nullable=false)
private ATSPRT_LimitsProfile limitsProfile;
public ATSPRT_LimitsProfile getLimitsProfile(){
return this.limitsProfile;
}
public void setLimitsProfile(ATSPRT_LimitsProfile limitsProfile){
this.limitsProfile = limitsProfile;
}

public static final String MSG_TYPE = "msgType";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@WithValueProvider(jupiterValueProviderBean = "messageTypeProvider", keyProperty = "id")
@JoinColumn(name="MSGTYPEID", nullable=false)
private ATSMSG_Type msgType;
public ATSMSG_Type getMsgType(){
return this.msgType;
}

public void setMsgType(ATSMSG_Type msgType) {
    this.msgType = msgType;
}

public static final String CURRENCY = "currency";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@WithValueProvider(jupiterValueProviderBean = "currenciesProvider", keyProperty = "id")
@JoinColumn(name = "CURRENCYID", nullable = false)
private com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency;

public com.progressoft.jfw.model.bussinessobject.core.JFWCurrency getCurrency() {
    return this.currency;
}

public void setCurrency(com.progressoft.jfw.model.bussinessobject.core.JFWCurrency currency) {
    this.currency = currency;
}

@Override
public String toString() {
    return "ATSPRT_LimitsAmount [id= " + getId() + ", minAmount= " + getMinAmount() + ", maxAmount= " + getMaxAmount() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSPRT_LimitsAmount other = (ATSPRT_LimitsAmount) obj;
return this.hashCode() == other.hashCode();}
}


}