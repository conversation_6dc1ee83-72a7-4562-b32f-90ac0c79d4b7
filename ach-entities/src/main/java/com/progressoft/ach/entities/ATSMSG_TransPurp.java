package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity;
import java.util.Map;
import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import jakarta.xml.bind.annotation.*;
import java.util.List;

@Entity

@Table(name="ATSMSG_TransPurps",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"CODE","Z_TENANT_ID"}),
	@UniqueConstraint(columnNames={"NAME","Z_TENANT_ID"})
})
@XmlRootElement(name="MSG_TransPurps")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMSG_TransPurp extends JFWLookableEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMSG_TransPurp(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String IS_ONUS_ALLOWED = "isOnusAllowed";
@Column(name="ISONUSALLOWED", nullable=true, length=1)
private boolean isOnusAllowed;
public boolean getIsOnusAllowed(){
return this.isOnusAllowed;
}
public void setIsOnusAllowed(boolean isOnusAllowed){
this.isOnusAllowed = isOnusAllowed;
}

public static final String IS_ALLOWED_TO_FORWARD_ONUS = "isAllowedToForwardOnus";
@Column(name="ISALLOWEDTOFORWARDONUS", nullable=true, length=1)
private boolean isAllowedToForwardOnus;
public boolean getIsAllowedToForwardOnus(){
	return this.isAllowedToForwardOnus;
}
public void setIsAllowedToForwardOnus(boolean isAllowedToForwardOnus){
	this.isAllowedToForwardOnus = isAllowedToForwardOnus;
}

public static final String CATEGORY_PURPOSE = "categoryPurpose";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="CATEGORYPURPOSEID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "categoryPurposeProvider", keyProperty = "id")
private ATSMSG_CtgPurp categoryPurpose;
public ATSMSG_CtgPurp getCategoryPurpose(){
return this.categoryPurpose;
}
public void setCategoryPurpose(ATSMSG_CtgPurp categoryPurpose){
this.categoryPurpose = categoryPurpose;
}

@Override
public String toString() {
	return "ATSMSG_TransPurp [id= " + getId() + ", code= " + getCode() + ", name= " + getName() + ", description= " + getDescription() + ", isOnusAllowed= " + getIsOnusAllowed() + ", isAllowedToForwardOnus= " + getIsAllowedToForwardOnus() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getCode() == null) ? 0 : getCode().hashCode());
result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
result = prime * result + ((getDescription() == null) ? 0 : getDescription().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMSG_TransPurp other = (ATSMSG_TransPurp) obj;
return this.hashCode() == other.hashCode();}
}


}