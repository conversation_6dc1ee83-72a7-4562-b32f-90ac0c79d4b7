package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Entity

@Table(name="ATSRolloverReqs")
@XmlRootElement(name="RolloverReqs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSRolloverReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSRolloverReq(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_ID = "requestId";
@Column(name="REQUESTID", nullable=false, length=4000)
private String requestId;
public String getRequestId(){
return this.requestId;
}
public void setRequestId(String requestId){
this.requestId = requestId;
}

public static final String IS_INITIATED_BY_PARTICIPANT = "isInitiatedByParticipant";
@Column(name="ISINITIATEDBYPARTICIPANT", nullable=true, length=1)
private boolean isInitiatedByParticipant;
public boolean getIsInitiatedByParticipant(){
return this.isInitiatedByParticipant;
}
public void setIsInitiatedByParticipant(boolean isInitiatedByParticipant){
this.isInitiatedByParticipant = isInitiatedByParticipant;
}

public static final String REJECTION_NOTE = "rejectionNote";
@Column(name="REJECTIONNOTE", nullable=true, length=4000)
private String rejectionNote;
public String getRejectionNote(){
return this.rejectionNote;
}
public void setRejectionNote(String rejectionNote){
this.rejectionNote = rejectionNote;
}

public static final String REQUESTING_PARTICIPANT = "requestingParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTINGPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private ATSPRT_Participant requestingParticipant;
public ATSPRT_Participant getRequestingParticipant(){
return this.requestingParticipant;
}
public void setRequestingParticipant(ATSPRT_Participant requestingParticipant){
this.requestingParticipant = requestingParticipant;
}

public static final String REQUEST_REASON = "requestReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTREASONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "requestReasonProvider", keyProperty = "id")
private ATSRequestReason requestReason;
public ATSRequestReason getRequestReason(){
return this.requestReason;
}
public void setRequestReason(ATSRequestReason requestReason){
this.requestReason = requestReason;
}

public static final String SESSION = "session";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="SESSIONID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "sessionProvider", keyProperty = "id")
private ATSBDI_Session session;
public ATSBDI_Session getSession(){
return this.session;
}
public void setSession(ATSBDI_Session session){
this.session = session;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "requestRReasonProvider", keyProperty = "id")
private ATSRequestRReason rejectionReason;
public ATSRequestRReason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSRequestRReason rejectionReason){
this.rejectionReason = rejectionReason;
}

public static final String PARTICIPANTS = "participants";
@ManyToMany(cascade = { CascadeType.PERSIST, CascadeType.DETACH, CascadeType.REMOVE, CascadeType.REFRESH, CascadeType.MERGE })
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinTable(name="ATSRolloverReqsParticipants", joinColumns = @JoinColumn(name = "ROLLOVERREQS_ID"), inverseJoinColumns = @JoinColumn(name = "PARTICIPANTS_ID"))
@WithValueProvider(jupiterValueProviderBean = "participantsProvider", keyProperty = "id")
private List<ATSPRT_Participant> participants = new ArrayList<ATSPRT_Participant>();
public List<ATSPRT_Participant> getParticipants(){
return this.participants;
}
public void setParticipants(List<ATSPRT_Participant> participants){
this.participants = participants;
}

@Override
public String toString() {
return "ATSRolloverReq [id= " + getId() + ", requestId= " + getRequestId() + ", isInitiatedByParticipant= " + getIsInitiatedByParticipant() + ", rejectionNote= " + getRejectionNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getRequestId() == null) ? 0 : getRequestId().hashCode());
result = prime * result + ((getRejectionNote() == null) ? 0 : getRejectionNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSRolloverReq other = (ATSRolloverReq) obj;
return this.hashCode() == other.hashCode();}
}


}