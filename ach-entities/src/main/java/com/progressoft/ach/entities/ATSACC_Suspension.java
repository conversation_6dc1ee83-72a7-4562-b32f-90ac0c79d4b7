package com.progressoft.ach.entities;

import com.progressoft.jupiter.annotation.WithValueProvider;
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSACC_Suspensions")
@XmlRootElement(name="ACC_Suspensions")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSACC_Suspension extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSACC_Suspension(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String DEBIT_SUSP_FLAG = "debitSuspFlag";
@Column(name="DEBITSUSPFLAG", nullable=true, length=1)
private boolean debitSuspFlag;
public boolean getDebitSuspFlag(){
return this.debitSuspFlag;
}
public void setDebitSuspFlag(boolean debitSuspFlag){
this.debitSuspFlag = debitSuspFlag;
}

public static final String CREDIT_SUSP_FLAG = "creditSuspFlag";
@Column(name="CREDITSUSPFLAG", nullable=true, length=1)
private boolean creditSuspFlag;
public boolean getCreditSuspFlag(){
return this.creditSuspFlag;
}
public void setCreditSuspFlag(boolean creditSuspFlag){
this.creditSuspFlag = creditSuspFlag;
}

public static final String SUSP_NOTE = "suspNote";
@Column(name="SUSPNOTE", nullable=false, length=1024)
private String suspNote;
public String getSuspNote(){
return this.suspNote;
}
public void setSuspNote(String suspNote){
this.suspNote = suspNote;
}

public static final String CURR_DEBIT_SUSP_FLAG = "currDebitSuspFlag";
@Column(name="CURRDEBITSUSPFLAG", nullable=true, length=1)
@Transient
private boolean currDebitSuspFlag;
public boolean getCurrDebitSuspFlag(){
return this.currDebitSuspFlag;
}
public void setCurrDebitSuspFlag(boolean currDebitSuspFlag){
this.currDebitSuspFlag = currDebitSuspFlag;
}

public static final String CURR_CREDIT_SUSP_FLAG = "currCreditSuspFlag";
@Column(name="CURRCREDITSUSPFLAG", nullable=true, length=1)
@Transient
private boolean currCreditSuspFlag;
public boolean getCurrCreditSuspFlag(){
return this.currCreditSuspFlag;
}
public void setCurrCreditSuspFlag(boolean currCreditSuspFlag){
this.currCreditSuspFlag = currCreditSuspFlag;
}

public static final String REF_PARTICIPANT = "refParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTICIPANTID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "accountsSuspensionsParticipantsProvider", keyProperty = "id")
private ATSPRT_Participant refParticipant;
public ATSPRT_Participant getRefParticipant(){
return this.refParticipant;
}
public void setRefParticipant(ATSPRT_Participant refParticipant){
this.refParticipant = refParticipant;
}

public static final String REF_PART_ACC = "refPartAcc";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REFPARTACCID", nullable=false)
@WithValueProvider(jupiterValueProviderBean = "partAccProvider" , keyProperty = "id")
private ATSACC_Account refPartAcc;
public ATSACC_Account getRefPartAcc(){
return this.refPartAcc;
}
public void setRefPartAcc(ATSACC_Account refPartAcc){
this.refPartAcc = refPartAcc;
}

@Override
public String toString() {
return "ATSACC_Suspension [id= " + getId() + ", debitSuspFlag= " + getDebitSuspFlag() + ", creditSuspFlag= " + getCreditSuspFlag() + ", suspNote= " + getSuspNote() + ", currDebitSuspFlag= " + getCurrDebitSuspFlag() + ", currCreditSuspFlag= " + getCurrCreditSuspFlag() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSACC_Suspension other = (ATSACC_Suspension) obj;
return this.hashCode() == other.hashCode();}
}


}