package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;
import com.progressoft.jupiter.annotation.WithValueProvider;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSBranchCreationReqs")
@XmlRootElement(name="BranchCreationReqs")
@XmlAccessorType(XmlAccessType.FIELD)
public class ATSBranchCreationReq extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSBranchCreationReq(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String REQUEST_ID = "requestId";
@Column(name="REQUESTID", nullable=false, length=4000)
private String requestId;
public String getRequestId(){
return this.requestId;
}
public void setRequestId(String requestId){
this.requestId = requestId;
}

public static final String BRANCH_CODE = "branchCode";
@Column(name="BRANCHCODE", nullable=false, length=3)
private String branchCode;
public String getBranchCode(){
return this.branchCode;
}
public void setBranchCode(String branchCode){
this.branchCode = branchCode;
}

public static final String BRANCH_NAME = "branchName";
@Column(name="BRANCHNAME", nullable=false, length=128)
private String branchName;
public String getBranchName(){
return this.branchName;
}
public void setBranchName(String branchName){
this.branchName = branchName;
}

public static final String BRANCH_DESCRIPTION = "branchDescription";
@Column(name="BRANCHDESCRIPTION", nullable=false, length=255)
private String branchDescription;
public String getBranchDescription(){
return this.branchDescription;
}
public void setBranchDescription(String branchDescription){
this.branchDescription = branchDescription;
}

public static final String REJECTION_NOTE = "rejectionNote";
@Column(name="REJECTIONNOTE", nullable=true, length=4000)
private String rejectionNote;
public String getRejectionNote(){
return this.rejectionNote;
}
public void setRejectionNote(String rejectionNote){
this.rejectionNote = rejectionNote;
}

public static final String REQUESTING_PARTICIPANT = "requestingParticipant";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REQUESTINGPARTICIPANTID", nullable=false)
private ATSPRT_Participant requestingParticipant;
public ATSPRT_Participant getRequestingParticipant(){
return this.requestingParticipant;
}
public void setRequestingParticipant(ATSPRT_Participant requestingParticipant){
this.requestingParticipant = requestingParticipant;
}

public static final String REJECTION_REASON = "rejectionReason";
@ManyToOne
@org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
@JoinColumn(name="REJECTIONREASONID", nullable=true)
@WithValueProvider(jupiterValueProviderBean = "requestRReasonProvider", keyProperty = "id")
private ATSRequestRReason rejectionReason;
public ATSRequestRReason getRejectionReason(){
return this.rejectionReason;
}
public void setRejectionReason(ATSRequestRReason rejectionReason){
this.rejectionReason = rejectionReason;
}

@Override
public String toString() {
return "ATSBranchCreationReq [id= " + getId() + ", requestId= " + getRequestId() + ", branchCode= " + getBranchCode() + ", branchName= " + getBranchName() + ", branchDescription= " + getBranchDescription() + ", rejectionNote= " + getRejectionNote() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getRequestId() == null) ? 0 : getRequestId().hashCode());
result = prime * result + ((getBranchCode() == null) ? 0 : getBranchCode().hashCode());
result = prime * result + ((getBranchName() == null) ? 0 : getBranchName().hashCode());
result = prime * result + ((getBranchDescription() == null) ? 0 : getBranchDescription().hashCode());
result = prime * result + ((getRejectionNote() == null) ? 0 : getRejectionNote().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSBranchCreationReq other = (ATSBranchCreationReq) obj;
return this.hashCode() == other.hashCode();}
}


}