package com.progressoft.ach.entities;

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity;

import jakarta.persistence.*;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

@Entity

@Table(name="ATSMndt_Policy_General",
uniqueConstraints=
{
	@UniqueConstraint(columnNames={"POLICYKEY","Z_TENANT_ID"})
})
@XmlRootElement(name="Mndt_Policy_General")
@XmlAccessorType(XmlAccessType.FIELD)
@org.hibernate.annotations.Cache(usage = org.hibernate.annotations.CacheConcurrencyStrategy .READ_WRITE )
public class ATSMndt_Policy_General extends JFWEntity implements Serializable {
private static final long serialVersionUID = 1L;

public ATSMndt_Policy_General(){/*Default Constructor*/}

public static final String ID = "id";
@Id
@GeneratedValue(strategy=GenerationType.AUTO)
@Column(name="ID", nullable=false, insertable=false)
private long id;
public long getId(){
return this.id;
}
public void setId(long id){
this.id = id;
}

public static final String POLICY_KEY = "policyKey";
@Column(name="POLICYKEY", nullable=false, length=255)
private String policyKey;
public String getPolicyKey(){
return this.policyKey;
}
public void setPolicyKey(String policyKey){
this.policyKey = policyKey;
}

public static final String POLICY_VALUE = "policyValue";
@Column(name="POLICYVALUE", nullable=true, length=255)
private String policyValue;
public String getPolicyValue(){
return this.policyValue;
}
public void setPolicyValue(String policyValue){
this.policyValue = policyValue;
}

@Override
public String toString() {
return "ATSMndt_Policy_General [id= " + getId() + ", policyKey= " + getPolicyKey() + ", policyValue= " + getPolicyValue() + "]";
}

@Override
public int hashCode() {
final int prime = 31;
int result = 1;
int Id= new Long("null".equals(getId() + "") ? 0 : getId()).intValue();
result = prime * result + (int) (Id ^ Id >>> 32);
result = prime * result + ((getPolicyKey() == null) ? 0 : getPolicyKey().hashCode());
result = prime * result + ((getPolicyValue() == null) ? 0 : getPolicyValue().hashCode());
return result;
}

@Override
public boolean equals(Object obj) {
if (this == obj)
return true;
else if (obj == null)
return false;
else if (getClass() != obj.getClass())
return false;
else {ATSMndt_Policy_General other = (ATSMndt_Policy_General) obj;
return this.hashCode() == other.hashCode();}
}


}