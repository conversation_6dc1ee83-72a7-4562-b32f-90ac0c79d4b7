<?xml version="1.0" encoding="UTF-8"?>
<!--Generated by Standards Editor (build:R1.6.16) on 2021 Feb 08 20:46:41, ISO 20022 version : 2013-->
<!--Copyright (c) SWIFT scrl, 2020.

 This is a licensed product, which may only be redistributed upon agreement with SWIFT scrl.

 This component is subject to the following terms of use:

 This component is provided 'AS IS'. SWIFT does not give and excludes any express or implied warranties with respect to this component such as but not limited to any guarantee as to its quality, supply or availability.

 The user  has no right, or right to authorise others, to:
	 - rent, lease, or sell this component;
	 - display publicly, distribute or otherwise provide this component;
	 - remove, alter, cover, obfuscate or cancel from view any copyright or other proprietary rights notices, marks or legends appearing in this physical medium.

Any and all rights, including title, ownership rights, copyright, trademark, patents, and any other intellectual property rights of whatever nature in this component will remain the exclusive property of SWIFT or its licensors. -->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.12" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.002.001.12">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AccountIdentification4Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the unique identification of an account as assigned by the account servicer.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="IBAN" type="IBAN2007Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IBAN</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">International Bank Account Number (IBAN) - identifier used internationally by financial institutions to uniquely identify the account of a customer. Further specifications of the format and content of the IBAN can be found in the standard ISO 13616 "Banking and related financial services - International Bank Account Number (IBAN)" version 1997-10-01, or later revisions.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Othr" type="GenericAccountIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of an account, as assigned by the account servicer, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AccountSchemeName1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AccountSchemeName1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalAccountIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ActiveOrHistoricCurrencyAndAmount</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A number of monetary units specified in an active or a historic currency where the unit of currency is explicit and compliant with ISO 4217.</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required">
                    <xs:annotation>
                        <xs:documentation source="Name" xml:lang="EN">Currency</xs:documentation>
                        <xs:documentation source="Definition" xml:lang="EN">Medium of exchange of currency.</xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ActiveOrHistoricCurrencyCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A code allocated to a currency by a Maintenance Agency under an international identification scheme, as described in the latest edition of the international standard ISO 4217 "Codes for the representation of currencies and funds".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AddressType2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AddressType2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of address.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Postal</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is the complete postal address.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PBOX">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">POBox</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is a postal office (PO) box.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HOME">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Residential</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is the home address.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BIZZ">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Business</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is the business address.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MLTO">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MailTo</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is the address to which mail is sent.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DLVY">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DeliveryTo</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address is the address to which delivery is to take place.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="AddressType3Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AddressType3Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of formats for the type of address.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="AddressType2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of address expressed as a code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="GenericIdentification30">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of address expressed as a proprietary code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AmendmentInformationDetails14">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AmendmentInformationDetails14</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details on the list of direct debit mandate elements that have been modified when the amendment indicator has been set.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlMndtId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMandateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the creditor, to unambiguously identify the original mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrSchmeId" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreditorSchemeIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original creditor scheme identification that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreditorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original creditor agent that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreditorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original creditor agent account that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtr" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalDebtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original debtor that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalDebtorAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original debtor account that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalDebtorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original debtor agent that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalDebtorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original debtor agent account that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlFnlColltnDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalFinalCollectionDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original final collection date that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlFrqcy" type="Frequency36Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalFrequency</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original frequency that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlRsn" type="MandateSetupReason1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalReason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original reason for the mandate to allow the user to distinguish between different mandates for the same creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTrckgDays" type="Exact2NumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalTrackingDays</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original number of tracking days that has been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountType4Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AmountType4Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.&#13;
&#13;
Usage: This amount has to be transported unchanged through the transaction chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="EqvtAmt" type="EquivalentAmount2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EquivalentAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="AnyBICDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">AnyBICDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial or non-financial institution by the ISO 9362 Registration Authority, as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BICFIDec2014Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362: 2014 - "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification6">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchAndFinancialInstitutionIdentification6</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution or a branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification18">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a financial institution, as assigned under an internationally recognised or proprietary identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BrnchId" type="BranchData3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BranchIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a specific branch of a financial institution.

Usage: This component should be used in case the identification information in the financial institution component does not provide identification up to branch level.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchData3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">BranchData3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific branch of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a branch of a financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identification for the branch of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which an agent is known and which is usually used to identify that agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostalAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccount40">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CashAccount40</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides the details to identify an account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="AccountIdentification4Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification for the account between the account owner and the account servicer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use of the account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Currency</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the currency in which the account is held. 

Usage: Currency should only be used in case one and the same account number covers several currencies
and the initiating party needs to identify which currency needs to be used for settlement on the account.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the account, as assigned by the account servicing institution, in agreement with the account owner in order to provide an additional means of identification of the account.

Usage: The account name is different from the account owner name. The account name is used in certain user communities to provide a means of identifying the account, in addition to the account owner's identity and the account number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Prxy" type="ProxyAccountIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proxy</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies an alternate assumed name for the identification of the account. </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CashAccountType2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashAccountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Account type, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CategoryPurpose1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CategoryPurpose1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the instruction based on a set of pre-defined categories.
Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalCategoryPurpose1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, as published in an external category purpose code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Charges7">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Charges7</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides information on the charges related to the payment transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Transaction charges to be paid by the charge bearer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Agent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that takes the transaction charges or to which the transaction charges are due.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ClearingChannel2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingChannel2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing channel for the routing of the transaction, as part of the payment type identification.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RTGS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RealTimeGrossSettlementSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a real-time gross settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RTNS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RealTimeNetSettlementSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a real-time net settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MPNS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MassPaymentNetSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing channel is a mass payment net settlement system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BOOK">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BookTransfer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Payment through internal book transfer.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of a clearing system identifier.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a clearing system, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification code for a clearing system, that has not yet been identified in the list of clearing systems.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemIdentification3Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification3Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing system identification.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashClearingSystem1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Infrastructure through which the payment instruction is processed, as published in an external clearing system identification code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Clearing system identification in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by a clearing system, to unambiguously identify a member of the clearing system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysId" type="ClearingSystemIdentification2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="MmbId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a member of a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Contact4">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Contact4</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details of the contact person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NmPrfx" type="NamePrefix2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">NamePrefix</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the terms used to formally address a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which a party is known and which is usually used to identify that party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PhneNb" type="PhoneNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PhoneNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Collection of information that identifies a phone number, as defined by telecom services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MobNb" type="PhoneNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MobileNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Collection of information that identifies a mobile phone number, as defined by telecom services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FaxNb" type="PhoneNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FaxNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Collection of information that identifies a FAX number, as defined by telecom services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailAdr" type="Max2048Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EmailAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Address for electronic mail (e-mail).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailPurp" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EmailPurpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Purpose for which an email address may be used.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="JobTitl" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">JobTitle</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the function.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rspnsblty" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Responsibility</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Role of a person in an organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Department</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="OtherContact1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Contact details in another form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrefrdMtd" type="PreferredContactMethod1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PreferredMethod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CountryCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Code to identify a country, a dependency, or another area of particular geopolitical interest, on the basis of country names obtained from the United Nations (ISO 3166, Alpha-2 code).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CreditDebitCode">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditDebitCode</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies if an operation is an increase or a decrease.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CRDT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Credit</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Operation is an increase.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DBIT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Debit</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Operation is a decrease.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CreditTransferMandateData1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditTransferMandateData1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details related to a credit transfer mandate signed between the creditor and the debtor.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MandateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the creditor, to unambiguously identify the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="MandateTypeInformation2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of mandate, such as paper, electronic or scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfSgntr" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateOfSignature</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which the credit transfer mandate has been signed by the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfVrfctn" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateOfVerification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which the credit transfer mandate has been verified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncSgntr" type="Max10KBinary">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ElectronicSignature</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Additional security provisions, such as a digital signature, as provided by the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FrstPmtDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FirstPaymentDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date of the first payment of a recurrent credit transfer as per the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FnlPmtDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinalPaymentDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date of the final payment of a recurrent credit transfer as per the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Frqcy" type="Frequency36Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Frequency</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Regularity with which credit transfer instructions are to be created and processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="MandateSetupReason1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the setup of the credit transfer mandate.&#13;
&#13;
Usage: &#13;
The reason will allow the user to distinguish between different mandates for the same creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceInformation2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditorReferenceInformation2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Reference information provided by the creditor to allow the identification of the underlying documents.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CreditorReferenceType2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of creditor reference.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ref" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique reference, as assigned by the creditor, to unambiguously refer to the payment transaction.

Usage: If available, the initiating party should provide this reference in the structured remittance information, to enable reconciliation by the creditor upon receipt of the amount of money.

If the business context requires the use of a creditor reference or a payment remit identification, and only one identifier can be passed through the end-to-end chain, the creditor's reference or payment remittance identification should be quoted in the end-to-end transaction identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditorReferenceType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of document referred by the creditor.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="DocumentType3Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of creditor reference, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Creditor reference type, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">CreditorReferenceType2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of creditor reference.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CodeOrProprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Coded or proprietary format creditor reference type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the credit reference type.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndDateTime2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DateAndDateTime2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice between a date or a date and time format.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Dt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Date</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specified date.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="DtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specified date and time.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DateAndPlaceOfBirth1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DateAndPlaceOfBirth1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Date and place of birth of a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="BirthDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BirthDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which a person is born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvcOfBirth" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ProvinceOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Province where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CityOfBirth" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CityOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">City where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CtryOfBirth" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountryOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Country where a person was born.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DatePeriod2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DatePeriod2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Range of time defined by a start date and an end date.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="FrDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FromDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Start date of the range.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="ToDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ToDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">End date of the range.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="DecimalNumber">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DecimalNumber</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Number of objects represented as a decimal number, for example 0.75 or 45.6.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DiscountAmountAndType1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DiscountAmountAndType1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the amount with a specific type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DiscountAmountType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money, which has been typed.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DiscountAmountType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DiscountAmountType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalDiscountAmountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type, in a free-text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FIToFIPmtStsRpt" type="FIToFIPaymentStatusReportV12"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAdjustment1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentAdjustment1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide information on the amount and reason of the document adjustment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money of the document adjustment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditDebitIndicator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies whether the adjustment must be subtracted or added to the total amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max4Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the adjustment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdditionalInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides further details on the document adjustment.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentLineIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Identifies the documents referred to in the remittance information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DocumentLineType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of referred document line identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Number</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the type specified for the referred document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RelatedDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date associated with the referred document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineInformation1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentLineInformation1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides document line information.&#13;
</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Id" type="DocumentLineIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides identification of the document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Desc" type="Max2048Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Description</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Description associated with the document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="RemittanceAmount3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides details on the amounts of the document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentLineType1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the document line identification.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="DocumentLineType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CodeOrProprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides the type details of the referred document line identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the issuer of the reference document line identificationtype.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentLineType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the document line identification.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalDocumentLineType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Line identification type in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Proprietary identification of the type of the remittance document.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="DocumentType3Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentType3Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a type of financial or commercial document.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="RADM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittanceAdviceMessage</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a remittance advice sent separately from the current transaction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RPIN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RelatedPaymentInstruction</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a linked payment instruction to which the current payment instruction is related, for example, in a cover scenario.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FXDR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ForeignExchangeDealReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a pre-agreed or pre-arranged foreign exchange transaction to which the payment transaction refers.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DISP">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DispatchAdvice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a dispatch advice.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PUOR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PurchaseOrder</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a purchase order.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SCOR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StructuredCommunicationReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a structured communication reference provided by the creditor to identify the referred transaction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocumentType6Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">DocumentType6Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a type of financial or commercial document.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MSIN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MeteredServiceInvoice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an invoice claiming payment for the supply of metered services, for example gas or electricity supplied to a fixed meter.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CNFA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditNoteRelatedToFinancialAdjustment</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a credit note for the final amount settled for a commercial transaction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DNFA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebitNoteRelatedToFinancialAdjustment</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a debit note for the final amount settled for a commercial transaction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CINV">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CommercialInvoice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an invoice.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CREN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditNote</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a credit note.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DEBN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebitNote</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a debit note.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HIRI">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">HireInvoice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an invoice for the hiring of human resources or renting goods or equipment.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SBIN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SelfBilledInvoice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an invoice issued by the debtor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CMCN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CommercialContract</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an agreement between the parties, stipulating the terms and conditions of the delivery of goods or services.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SOAC">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StatementOfAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a statement of the transactions posted to the debtor's account at the supplier.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DISP">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DispatchAdvice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a dispatch advice.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="BOLD">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BillOfLading</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a shipping notice.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VCHR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Voucher</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is an electronic payment document.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="AROI">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AccountReceivableOpenItem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a payment that applies to a specific source document.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TSUT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TradeServicesUtilityTransaction</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a transaction identifier as assigned by the Trade Services Utility.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PUOR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PurchaseOrder</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document is a purchase order.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="EquivalentAmount2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">EquivalentAmount2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Amount of money to be moved between the debtor and creditor, expressed in the currency of the debtor's account, and the currency in which the amount is to be moved.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money to be moved between debtor and creditor, before deduction of charges, expressed in the currency of the debtor's account, and to be moved in a different currency.
Usage: The first agent will convert the equivalent amount into the amount to be moved.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CcyOfTrf" type="ActiveOrHistoricCurrencyCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CurrencyOfTransfer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the currency of the to be transferred amount, which is different from the currency of the debtor's account.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Exact2NumericText">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Exact2NumericText</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a numeric string with an exact length of 2 digits.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Exact4AlphaNumericText">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Exact4AlphaNumericText</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies an alphanumeric string with a length of 4 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9]{4}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalAccountIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalAccountIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external account identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashAccountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalCashAccountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use, of the cash account in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashClearingSystem1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalCashClearingSystem1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the cash clearing system, as published in an external cash clearing system code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCategoryPurpose1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalCategoryPurpose1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the category purpose, as published in an external category purpose code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalClearingSystemIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing system identification code, as published in an external clearing system identification code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDiscountAmountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalDiscountAmountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDocumentLineType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalDocumentLineType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the document line type as published in an external document type code list.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalFinancialInstitutionIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external financial institution identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalGarnishmentType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalGarnishmentType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the garnishment type as published in an external document type code list.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalLocalInstrument1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalLocalInstrument1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external local instrument code in the format of character string with a maximum length of 35 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalMandateSetupReason1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalMandateSetupReason1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external mandate setup reason code in the format of character string with a maximum length of 4 characters.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalOrganisationIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external organisation identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPaymentGroupStatus1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPaymentGroupStatus1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the status of a group of payment instructions, as published in an external payment group status code set.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPaymentTransactionStatus1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPaymentTransactionStatus1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the status of an individual payment instructions, as published in an external payment transaction status code set.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPersonIdentification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external person identification scheme name code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalProxyAccountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalProxyAccountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external proxy account type code, as published in the proxy account type external code set.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPurpose1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalPurpose1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external purpose code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalServiceLevel1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalServiceLevel1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the external service level code in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalStatusReason1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalStatusReason1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the status reason, as published in an external status reason code list.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalTaxAmountType1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ExternalTaxAmountType1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the nature, or use, of the amount in the format of character string with a maximum length of 4 characters.&#13;
The list of valid codes is an external code list published separately.&#13;
External code sets can be downloaded from www.iso20022.org.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FIToFIPaymentStatusReportV12">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FIToFIPaymentStatusReportV12</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Scope&#13;
The FIToFIPaymentStatusReport message is sent by an instructed agent to the previous party in the payment chain. It is used to inform this party about the positive or negative status of an instruction (either single or file). It is also used to report on a pending instruction.&#13;
Usage&#13;
The FIToFIPaymentStatusReport message is exchanged between agents to provide status information about instructions previously sent. Its usage will always be governed by a bilateral agreement between the agents.&#13;
The FIToFIPaymentStatusReport message can be used to provide information about the status (e.g. rejection, acceptance) of a credit transfer instruction, a direct debit instruction, as well as other intra-agent instructions (for example FIToFIPaymentCancellationRequest).&#13;
The FIToFIPaymentStatusReport message refers to the original instruction(s) by means of references only or by means of references and a set of elements from the original instruction.&#13;
The FIToFIPaymentStatusReport message can be used in domestic and cross-border scenarios.&#13;
The FIToFIPaymentStatusReport may also be sent to the receiver of the payment in a real time payment scenario, as both sides of the transactions must be informed of the status of the transaction (for example either the beneficiary is credited, or the transaction is rejected).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader101">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GroupHeader</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the status report message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="OrgnlGrpInfAndSts" type="OriginalGroupHeader17">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalGroupInformationAndStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original group information concerning the group of transactions, to which the status report message refers to.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TxInfAndSts" type="PaymentTransaction130">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransactionInformationAndStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information concerning the original transactions, to which the status report message refers.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SupplementaryData</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Additional information that cannot be captured in the structured elements and/or any other specific block.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialIdentificationSchemeName1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the organisation identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification18">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FinancialInstitutionIdentification18</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the details to identify a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BICFI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code allocated to a financial institution by the ISO 9362 Registration Authority as described in ISO 9362 "Banking - Banking telecommunication messages - Business identifier code (BIC)".</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemMemberIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information used to identify a member within a clearing system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identifier of the financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which an agent is known and which is usually used to identify that agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostalAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of an agent, as assigned by an institution, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Frequency36Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Frequency36Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of format for a frequency, for example, the frequency of payment.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Tp" type="Frequency6Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a frequency in terms of a specified period type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prd" type="FrequencyPeriod1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Period</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a frequency in terms of a count per period within a specified period type.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="PtInTm" type="FrequencyAndMoment1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PointInTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a frequency in terms of an exact point in time or moment within a specified period type.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="Frequency6Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Frequency6Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the regularity of an event.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="YEAR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Annual</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every year or once a year.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MNTH">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Monthly</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every month or once a month.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QURT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Quarterly</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every three months or four times a year.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MIAN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SemiAnnual</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every six months or two times a year.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="WEEK">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Weekly</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place once a week.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DAIL">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Daily</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every day.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ADHO">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Adhoc</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place on request or as necessary.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INDA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">IntraDay</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place several times a day.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FRTN">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Fortnightly</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Event takes place every two weeks.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FrequencyAndMoment1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FrequencyAndMoment1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Defines a frequency in terms a specific moment within a specified period type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Tp" type="Frequency6Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Period for which the number of instructions are to be created and processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="PtInTm" type="Exact2NumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PointInTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further information on the exact point in time the event should take place.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FrequencyPeriod1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">FrequencyPeriod1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Defines a frequency in terms on counts per period for a specific period type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Tp" type="Frequency6Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Period for which the number of instructions are to be created and processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CntPerPrd" type="DecimalNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountPerPeriod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number of instructions to be created and processed during the specified period.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Garnishment3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Garnishment3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides remittance information about a payment for garnishment-related purposes.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Tp" type="GarnishmentType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of garnishment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Grnshee" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Garnishee</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the garnisher.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtAdmstr" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GarnishmentAdministrator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party on the credit side of the transaction who administers the garnishment on behalf of the ultimate beneficiary.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ReferenceNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reference information that is specific to the agency receiving the garnishment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Date</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date of payment which garnishment was taken from.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money remitted for the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FmlyMdclInsrncInd" type="TrueFalseIndicator">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FamilyMedicalInsuranceIndicator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicates if the person to whom the garnishment applies (that is, the ultimate debtor) has family medical insurance coverage available.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MplyeeTermntnInd" type="TrueFalseIndicator">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EmployeeTerminationIndicator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicates if the employment of the person to whom the garnishment applies (that is, the ultimate debtor) has been terminated.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GarnishmentType1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of garnishment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="GarnishmentType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CodeOrProprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides the type details of the garnishment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the issuer of the garnishment type.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GarnishmentType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of garnishment.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalGarnishmentType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Garnishment type in a coded form.&#13;
Would suggest this to be an External Code List to contain:&#13;
GNCS    Garnishment from a third party payer for Child Support&#13;
GNDP    Garnishment from a Direct Payer for Child Support&#13;
GTPP     Garnishment from a third party payer to taxing agency.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Proprietary identification of the type of garnishment.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericAccountIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to a generic account identification.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="Max34Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification assigned by an institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="AccountSchemeName1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericFinancialIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification of a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification30">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericIdentification30</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification, for example, party identification or account identification.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="Exact4AlphaNumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Proprietary information, often a code, issued by the data source scheme issuer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Short textual description of the scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericOrganisationIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification of an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification assigned by an institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GenericPersonIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to an identification of a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="Id" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SchemeName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Entity that assigns the identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader101">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">GroupHeader101</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of characteristics shared by all individual transactions included in the message.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the instructing party, and sent to the next party in the chain to unambiguously identify the message.
Usage: The instructing party has to make sure that MessageIdentification is unique per instructed party for a pre-agreed period.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="CreDtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that instructs the next party in the chain to carry out the (set of) instruction(s).

Usage: The instructing agent is the party sending the status message and not the party that sent the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).

Usage: The instructed agent is the party receiving the status message and not the party that received the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlBizQry" type="OriginalBusinessQuery1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalBusinessQuery</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original requestor, to unambiguously identify the business query message.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">IBAN2007Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">An identifier used internationally by financial institutions to uniquely identify the account of a customer at a financial institution, as described in the latest edition of the international standard ISO 13616: 2007 - "Banking and related financial services - International Bank Account Number (IBAN)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ISODate</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time in a calendar year expressed in the YYYY-MM-DD format. This representation is defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ISODateTime</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A particular point in the progression of time defined by a mandatory date and a mandatory time component, expressed in either UTC time format (YYYY-MM-DDThh:mm:ss.sssZ), local time with UTC offset format (YYYY-MM-DDThh:mm:ss.sss+/-hh:mm), or local time format (YYYY-MM-DDThh:mm:ss.sss). These representations are defined in "XML Schema Part 2: Datatypes Second Edition - W3C Recommendation 28 October 2004" which is aligned with ISO 8601.
Note on the time format:
1) beginning / end of calendar day
00:00:00 = the beginning of a calendar day
24:00:00 = the end of a calendar day
2) fractions of second in time format
Decimal fractions of seconds may be included. In this case, the involved parties shall agree on the maximum number of digits that are allowed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="ISOYear">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ISOYear</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Year represented by YYYY (ISO 8601).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:gYear"/>
    </xs:simpleType>
    <xs:simpleType name="LEIIdentifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LEIIdentifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Legal Entity Identifier is a code allocated to a party as described in ISO 17442 "Financial Services - Legal Entity Identifier (LEI)".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="LocalInstrument2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">LocalInstrument2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements that further identifies the type of local instruments being requested by the initiating party.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalLocalInstrument1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the local instrument, as published in an external local instrument code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the local instrument, as a proprietary code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateClassification1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateClassification1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the instruction based on a set of pre-defined categories.
Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="MandateClassification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, as published in an external category purpose code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Category purpose, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="MandateClassification1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateClassification1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of direct debit amount, such as fixed or variable.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIXE">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Fixed</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Direct debit amount is fixed.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="USGB">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">UsageBased</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Direct debit amount is based on usage.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="VARI">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Variable</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Direct debit amount is variable.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MandateRelatedData2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateRelatedData2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Choice of mandate related information.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element maxOccurs="1" minOccurs="0" name="DrctDbtMndt" type="MandateRelatedInformation15">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DirectDebitMandate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specific direct debit mandate data.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtTrfMndt" type="CreditTransferMandateData1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditTransferMandate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specific credit transfer mandate data.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateRelatedInformation15">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateRelatedInformation15</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details related to a direct debit mandate signed between the creditor and the debtor.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MandateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the creditor, to unambiguously identify the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfSgntr" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateOfSignature</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which the direct debit mandate has been signed by the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AmdmntInd" type="TrueFalseIndicator">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AmendmentIndicator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicator notifying whether the underlying mandate is amended or not.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AmdmntInfDtls" type="AmendmentInformationDetails14">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AmendmentInformationDetails</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">List of mandate elements that have been modified.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncSgntr" type="Max1025Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ElectronicSignature</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Additional security provisions, such as a digital signature, as provided by the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FrstColltnDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FirstCollectionDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date of the first collection of a direct debit as per the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FnlColltnDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FinalCollectionDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date of the final collection of a direct debit as per the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Frqcy" type="Frequency36Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Frequency</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Regularity with which direct debit instructions are to be created and processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="MandateSetupReason1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the direct debit mandate to allow the user to distinguish between different mandates for the same creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TrckgDays" type="Exact2NumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TrackingDays</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the number of days the direct debit instruction must be tracked.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="MandateSetupReason1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateSetupReason1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the setup of the mandate.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalMandateSetupReason1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the mandate setup, as published in an external reason code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the mandate setup, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateTypeInformation2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">MandateTypeInformation2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to further detail the information related to the type of payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ServiceLevel</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agreement under which or rules under which the mandate resides.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LocalInstrument</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">User community specific instrument.
Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CategoryPurpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the mandate based on a set of pre-defined categories.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Clssfctn" type="MandateClassification1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Classification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of direct debit instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Max1025Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max1025Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 1025 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="1025"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max105Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max105Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 105 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="105"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max10KBinary">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max10KBinary</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Binary data of 10K maximum.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:base64Binary">
            <xs:minLength value="1"/>
            <xs:maxLength value="10240"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max128Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max128Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 128 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="128"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max140Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max140Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 140 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max15NumericText">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max15NumericText</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a numeric string with a maximum length of 15 digits.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max16Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max16Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 16 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max2048Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 2048 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max34Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max34Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 34 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max350Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max350Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 350 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="350"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max35Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 35 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max4Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max4Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 4 characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Max70Text</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies a character string with a maximum length of 70characters.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NamePrefix2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">NamePrefix2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the terms used to formally address a person.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="DOCT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Doctor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the person is Doctor or Dr.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MADM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Madam</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the person is Madam.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MISS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Miss</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the person is Miss.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MIST">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Mister</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the person is Mister or Mr.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MIKS">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GenderNeutral</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title of the person is gender neutral (Mx).</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Number">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Number</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Number of objects represented as an integer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="0"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="NumberOfTransactionsPerStatus5">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">NumberOfTransactionsPerStatus5</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide detailed information on the number of transactions that are reported with a specific transaction status.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="DtldNbOfTxs" type="Max15NumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DetailedNumberOfTransactions</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number of individual transactions contained in the message, detailed per status.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="DtldSts" type="ExternalPaymentTransactionStatus1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DetailedStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Common transaction status for all individual transactions reported.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DtldCtrlSum" type="DecimalNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DetailedControlSum</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Total of all individual amounts included in the message, irrespective of currencies, detailed per status.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentification29">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OrganisationIdentification29</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AnyBIC" type="AnyBICDec2014Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AnyBIC</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Business identification code of the organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LEI</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Legal entity identification as an alternate identification for a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericOrganisationIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of an organisation, as assigned by an institution, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OrganisationIdentificationSchemeName1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the organisation identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalOrganisationIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="OriginalBusinessQuery1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OriginalBusinessQuery1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original requestor, to unambiguously identify the business query message.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the original initiating party, to unambiguously identify the original query message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgNmId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MessageNameIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the query message name identifier to which the message refers.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CreDtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalGroupHeader17">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OriginalGroupHeader17</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides details on the original group, to which the message refers.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the original instructing party, to unambiguously identify the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="OrgnlMsgNmId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageNameIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the original message name identifier to which the message refers.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the original message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlNbOfTxs" type="Max15NumericText">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalNumberOfTransactions</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number of individual transactions contained in the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCtrlSum" type="DecimalNumber">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalControlSum</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Total of all individual amounts included in the original message, irrespective of currencies.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="GrpSts" type="ExternalPaymentGroupStatus1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GroupStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the status of a group of transactions.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="StsRsnInf" type="StatusReasonInformation12">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StatusReasonInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides detailed information on the status reason.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="NbOfTxsPerSts" type="NumberOfTransactionsPerStatus5">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">NumberOfTransactionsPerStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Detailed information on the number of transactions for each identical transaction status.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalGroupInformation29">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OriginalGroupInformation29</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identifier of the group of transactions as assigned by the original instructing party.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference assigned by the original instructing party to unambiguously identify the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="OrgnlMsgNmId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalMessageNameIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the original message name identifier to which the message refers, for example, pacs.003.001.01 or MT103.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalCreationDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Original date and time at which the message was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalTransactionReference35">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OriginalTransactionReference35</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies key elements as defined in the original transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InterbankSettlementAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money moved between the instructing agent and the instructed agent.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="AmountType4Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money to be moved between the debtor and creditor, before deduction of charges, expressed in the currency as ordered by the initiating party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InterbankSettlementDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date on which the amount of money ceases to be available to the agent that owes it and when the amount of money becomes available to the agent to which it is due.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ReqdColltnDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RequestedCollectionDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which the creditor requests that the amount of money is to be collected from the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ReqdExctnDt" type="DateAndDateTime2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RequestedExecutionDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date at which the initiating party requests the clearing agent to process the payment. 
Usage: This is the date on which the debtor's account is to be debited. If payment by cheque, the date when the cheque must be generated by the bank.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrSchmeId" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorSchemeIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Credit party that signs the mandate.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmInf" type="SettlementInstruction11">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the details on how the settlement of the original transaction(s) between the instructing agent and the instructed agent was completed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation27">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PaymentTypeInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to further specify the type of transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtMtd" type="PaymentMethod4Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PaymentMethod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the means of payment that will be used to move the amount of money.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtRltdInf" type="MandateRelatedData2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MandateRelatedInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides further details of the mandate signed between the creditor and the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation21">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittanceInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching of an entry with the items that the transfer is intended to settle, such as commercial invoices in an accounts' receivable system.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="Party40Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">UltimateDebtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Ultimate party that owes an amount of money to the (ultimate) creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="Party40Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Debtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party that owes an amount of money to the (ultimate) creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the debtor to which a debit entry will be made as a result of the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution servicing an account for the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the debtor agent at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Financial institution servicing an account for the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the creditor agent at its servicing agent to which a credit entry will be made as a result of the payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="Party40Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Creditor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party to which an amount of money is due.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the creditor to which a credit entry will be posted as a result of the payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtCdtr" type="Party40Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">UltimateCreditor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Ultimate party to which an amount of money is due.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Purpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Underlying reason for the payment transaction.&#13;
&#13;
Usage: &#13;
Purpose is used by the end customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OtherContact1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">OtherContact1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Communication device number or electronic address used for communication.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ChanlTp" type="Max4Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ChannelType</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Method used to contact the financial institution’s contact for the specific tax region.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max128Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Communication value such as phone number or email address.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party38Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Party38Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature or use of the account.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="OrgId" type="OrganisationIdentification29">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OrganisationIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify an organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="PrvtId" type="PersonIdentification13">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PrivateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a person, for example a passport.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Party40Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Party40Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Identification of a person, an organisation or a financial institution.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Pty" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Party</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a person or an organisation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Agent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a financial institution.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="PartyIdentification135">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PartyIdentification135</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the identification of a person or an organisation.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name by which a party is known and which is usually used to identify that party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress24">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostalAddress</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party38Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtryOfRes" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountryOfResidence</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Country in which a person resides (the place of a person's home). In the case of a company, it is the country from which the affairs of that company are directed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtctDtls" type="Contact4">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ContactDetails</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to indicate how to contact the party.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PaymentMethod4Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentMethod4Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the transfer method that will be used to transfer an amount of money.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CHK">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Cheque</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Written order to a bank to pay a certain amount of money from one person to another person.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TRF">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditTransfer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Transfer of an amount of money in the books of the account servicer.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="DD">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DirectDebit</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Collection of an amount of money from the debtor's bank account by the creditor. The amount of money and dates of collections may vary.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="TRA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransferAdvice</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Transfer of an amount of money in the books of the account servicer. An advice should be sent back to the account owner.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PaymentTransaction130">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentTransaction130</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details on the original transactions, to which the status report message refers.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="StsId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StatusIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by an instructing party for an instructed party, to unambiguously identify the reported status.
Usage: The instructing party is the party sending the status message and not the party that sent the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlGrpInf" type="OriginalGroupInformation29">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalGroupInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point to point reference, as assigned by the original instructing party, to unambiguously identify the original message.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlInstrId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalInstructionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original instructing party for the original instructed party, to unambiguously identify the original instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlEndToEndId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalEndToEndIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original initiating party, to unambiguously identify the original transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalTransactionIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by the original first instructing agent, to unambiguously identify the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlUETR" type="UUIDv4Identifier">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalUETR</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Universally unique identifier to provide the original end-to-end reference of a payment transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TxSts" type="ExternalPaymentTransactionStatus1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TransactionStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the status of a transaction, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="StsRsnInf" type="StatusReasonInformation12">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StatusReasonInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides detailed information on the status reason.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ChrgsInf" type="Charges7">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ChargesInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides information on the charges related to the processing of the rejection of the instruction.

Usage: This is passed on for information purposes only. Settlement of the charges will be done separately.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AccptncDtTm" type="ISODateTime">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AcceptanceDateTime</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Point in time when the payment order from the initiating party meets the processing conditions of the account servicing agent. This means that the account servicing agent has received the payment order and has applied checks such as authorisation, availability of funds.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FctvIntrBkSttlmDt" type="DateAndDateTime2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EffectiveInterbankSettlementDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and time at which a transaction is completed and cleared, that is, payment is effected.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctSvcrRef" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AccountServicerReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique reference, as assigned by the account servicing institution, to unambiguously identify the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystemReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique reference, as assigned by a clearing system, to unambiguously identify the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that instructs the next party in the chain to carry out the (set of) instruction(s).

Usage: The instructing agent is the party sending the status message and not the party that sent the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent that is instructed by the previous party in the chain to carry out the (set of) instruction(s).

Usage: The instructed agent is the party receiving the status message and not the party that received the original instruction that is being reported on.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxRef" type="OriginalTransactionReference35">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OriginalTransactionReference</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Key elements used to identify the original transaction that is being referred to.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SupplementaryData</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Additional information that cannot be captured in the structured elements and/or any other specific block.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTypeInformation27">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PaymentTypeInformation27</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details of the type of payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrPrty" type="Priority2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructionPriority</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Indicator of the urgency or order of importance that the instructing party would like the instructed party to apply to the processing of the instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrChanl" type="ClearingChannel2Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingChannel</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the clearing channel to be used to process the payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ServiceLevel</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agreement under which or rules under which the transaction should be processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LocalInstrument</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">User community specific instrument.

Usage: This element is used to specify a local instrument, local clearing option and/or further qualify the service or service level.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqTp" type="SequenceType3Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SequenceType</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies the direct debit sequence, such as first, recurrent, final or one-off.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CategoryPurpose</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the high level purpose of the instruction based on a set of pre-defined categories.
Usage: This is used by the initiating party to provide information concerning the processing of the payment. It is likely to trigger special processing by any of the agents involved in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PercentageRate">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PercentageRate</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Rate expressed as a percentage, that is, in hundredths, for example, 0.7 is 7/10 of a percent, and 7.0 is 7%.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="10"/>
            <xs:totalDigits value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PersonIdentification13">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PersonIdentification13</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous way to identify a person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DateAndPlaceOfBirth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date and place of birth of a person.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericPersonIdentification1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Other</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification of a person, as assigned by an institution, using an identification scheme.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PersonIdentificationSchemeName1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Sets of elements to identify a name of the identification scheme.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalPersonIdentification1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="PhoneNumber">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PhoneNumber</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">The collection of information which identifies a specific phone or FAX number as defined by telecom services.
It consists of a "+" followed by the country code (from 1 to 3 characters) then a "-" and finally, any combination of numbers, "(", ")", "+" and "-" (up to 30 characters).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PostalAddress24">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PostalAddress24</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AdrTp" type="AddressType3Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AddressType</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies the nature of the postal address.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Department</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SubDepartment</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of a sub-division of a large organisation or building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">StreetName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a street or thoroughfare.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Number that identifies the position of a building on a street.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">BuildingName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the building or house.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Floor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Floor or storey within a building.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="Max16Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostBox</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Numbered box in a post office, assigned to a person or organisation, where letters are kept until called for.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Room</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Building room number.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PostCode</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifier consisting of a group of letters and/or numbers that is added to a postal address to assist the sorting of mail.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of a built-up area, with defined boundaries, and a local government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TownLocationName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specific location name within the town.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DistrictName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision within a country sub-division.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CountrySubDivision</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies a subdivision of a country such as state, region, county.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Country</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Nation with its own government.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="7" minOccurs="0" name="AdrLine" type="Max70Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AddressLine</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information that locates and identifies a specific address, as defined by postal services, presented in free format text.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PreferredContactMethod1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">PreferredContactMethod1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the individual contact within an organisation.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="LETT">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Letter</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact is per letter.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MAIL">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Email</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact is per email.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="PHON">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Phone</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact is per phone.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FAXX">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Fax</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact is per fax.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CELL">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">MobileOrCellPhone</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Preferred method used to reach the contact is per mobile or cell phone.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Priority2Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Priority2Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the priority level of an event.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGH">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">High</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is high.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="NORM">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Normal</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Priority level is normal.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ProxyAccountIdentification1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ProxyAccountIdentification1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information related to a proxy  identification of the account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ProxyAccountType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of the proxy identification.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Id" type="Max2048Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Identification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification used to indicate the account identification under another specified name.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProxyAccountType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ProxyAccountType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the scheme used for the identification of an account alias.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalProxyAccountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a coded form as published in an external list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the identification scheme, in a free text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Purpose2Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">Purpose2Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the underlying reason for the payment transaction.
Usage: Purpose is used by the end-customers, that is initiating party, (ultimate) debtor, (ultimate) creditor to provide information concerning the nature of the payment. Purpose is a content element, which is not used for processing by any of the agents involved in the payment chain.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalPurpose1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Underlying reason for the payment transaction, as published in an external purpose code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Purpose, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentInformation7">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ReferredDocumentInformation7</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to identify the documents referred to in the remittance information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ReferredDocumentType4">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Number</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique and unambiguous identification of the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RelatedDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date associated with the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="LineDtls" type="DocumentLineInformation1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">LineDetails</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide the content of the referred document line.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType3Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ReferredDocumentType3Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the document referred in the remittance information.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="DocumentType6Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Document type in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Proprietary identification of the type of the remittance document.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType4">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ReferredDocumentType4</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the document referred in the remittance information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="ReferredDocumentType3Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CodeOrProprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides the type details of the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Issuer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the issuer of the reference document type.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceAmount2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">RemittanceAmount2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DuePayableAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount specified is the exact amount due and payable to the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="DscntApldAmt" type="DiscountAmountAndType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DiscountAppliedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount specified for the referred document is the amount of discount to be applied to the amount due and payable to the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditNoteAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount specified for the referred document is the amount of a credit note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TaxAmt" type="TaxAmountAndType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Quantity of cash resulting from the calculation of the tax.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdjustmentAmountAndReason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies detailed information on the amount and reason of the document adjustment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money remitted for the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceAmount3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">RemittanceAmount3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Nature of the amount and currency on a document referred to in the remittance section, typically either the original amount due/payable or the amount actually remitted for the referenced document.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DuePayableAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount specified is the exact amount due and payable to the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="DscntApldAmt" type="DiscountAmountAndType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DiscountAppliedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of discount to be applied to the amount due and payable to the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditNoteAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of a credit note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TaxAmt" type="TaxAmountAndType1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of the tax.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdjustmentAmountAndReason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies detailed information on the amount and reason of the adjustment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RemittedAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money remitted.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation21">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">RemittanceInformation21</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Ustrd" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Unstructured</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in an unstructured form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Strd" type="StructuredRemittanceInformation17">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Structured</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SequenceType3Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SequenceType3Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the current transaction that belongs to a sequence of transactions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="FRST">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">First</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">First collection of a series of direct debit instructions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RCUR">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Recurring</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Direct debit instruction where the debtor's authorisation is used for regular direct debit transactions initiated by the creditor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="FNAL">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Final</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Final collection of a series of direct debit instructions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OOFF">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">OneOff</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Direct debit instruction where the debtor's authorisation is used to initiate one single direct debit transaction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="RPRE">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Represented</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Collection used to re-present previously reversed or returned direct debit transactions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ServiceLevel8Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">ServiceLevel8Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the service level of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalServiceLevel1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a pre-agreed service or level of service between the parties, as published in an external service level code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies a pre-agreed service or level of service between the parties, as a proprietary code.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="SettlementInstruction11">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementInstruction11</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides further details on the settlement of the instruction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="SttlmMtd" type="SettlementMethod1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementMethod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Method used to settle the (batch of) payment instructions.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SettlementAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">A specific purpose account used to post debit and credit entries as a result of the transaction.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSys" type="ClearingSystemIdentification3Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specification of a pre-agreed offering between clearing agents or the channel through which the payment instruction is processed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingReimbursementAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent through which the instructing agent will reimburse the instructed agent.

Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingReimbursementAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the instructing reimbursement agent account at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedReimbursementAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent at which the instructed agent will be reimbursed.
Usage: If InstructedReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.
Usage: If InstructingAgent and InstructedAgent have the same reimbursement agent, then only InstructingReimbursementAgent must be used.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedReimbursementAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the instructed reimbursement agent account at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification6">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ThirdReimbursementAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Agent at which the instructed agent will be reimbursed.
Usage: If ThirdReimbursementAgent contains a branch of the InstructedAgent, then the party in InstructedAgent will claim reimbursement from that branch/will be paid by that branch.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgtAcct" type="CashAccount40">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ThirdReimbursementAgentAccount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous identification of the account of the third reimbursement agent account at its servicing agent in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SettlementMethod1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SettlementMethod1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the method used to settle the credit transfer instruction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructedAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done by the agent instructed to execute a payment instruction.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="INGA">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">InstructingAgent</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done by the agent instructing and forwarding the payment to the next party in the payment chain.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="COVE">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CoverMethod</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done through a cover payment.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CLRG">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ClearingSystem</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Settlement is done through a payment clearing system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="StatusReason6Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">StatusReason6Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the status of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalStatusReason1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the status, as published in an external reason code list.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reason for the status, in a proprietary form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="StatusReasonInformation12">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">StatusReasonInformation12</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides information on the status reason of the transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Orgtr" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Originator</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party that issues the status.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="StatusReason6Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Reason</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the reason for the status report.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AddtlInf" type="Max105Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdditionalInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further details on the status reason.

Usage: Additional information can be used for several purposes such as the reporting of repaired information.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StructuredRemittanceInformation17">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">StructuredRemittanceInformation17</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Information supplied to enable the matching/reconciliation of an entry with the items that the payment is intended to settle, such as commercial invoices in an accounts' receivable system, in a structured form.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RfrdDocInf" type="ReferredDocumentInformation7">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ReferredDocumentInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides the identification and the content of the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RfrdDocAmt" type="RemittanceAmount2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ReferredDocumentAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides details on the amounts of the referred document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrRefInf" type="CreditorReferenceInformation2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CreditorReferenceInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Reference information provided by the creditor to allow the identification of the underlying documents.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcr" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Invoicer</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the organisation issuing the invoice, when it is different from the creditor or ultimate creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcee" type="PartyIdentification135">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Invoicee</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the party to whom an invoice is issued, when it is different from the debtor or ultimate debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxRmt" type="TaxData1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxRemittance</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides remittance information about a payment made for tax-related purposes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtRmt" type="Garnishment3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">GarnishmentRemittance</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides remittance information about a payment for garnishment-related purposes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="3" minOccurs="0" name="AddtlRmtInf" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdditionalRemittanceInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Additional information, in free text form, to complement the structured remittance information.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryData1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SupplementaryData1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Additional information that can not be captured in the structured fields and/or any other specific block.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="PlcAndNm" type="Max350Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">PlaceAndName</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unambiguous reference to the location where the supplementary data must be inserted in the message instance.&#13;
In the case of XML, this is expressed by a valid XPath.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Envlp" type="SupplementaryDataEnvelope1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Envelope</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Technical element wrapping the supplementary data.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryDataEnvelope1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">SupplementaryDataEnvelope1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Technical component that contains the validated supplementary data information. This technical envelope allows to segregate the supplementary data information from any other information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:any namespace="##any" processContents="lax"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmount3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxAmount3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide information on the tax amount(s) of tax record.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="PercentageRate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Rate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Rate used to calculate the tax.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxableBaseAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money on which the tax is based.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TotalAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Total amount that is the result of the calculation of the tax for the record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Dtls" type="TaxRecordDetails3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Details</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide details on the tax period and amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmountAndType1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxAmountAndType1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the amount with a specific type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="TaxAmountType1Choice">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the type of the amount.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Amount of money, which has been typed.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmountType1Choice">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxAmountType1Choice</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type.</xs:documentation>
        </xs:annotation>
        <xs:choice>
            <xs:element name="Cd" type="ExternalTaxAmountType1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Code</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type, in a coded form.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Prtry" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Proprietary</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the amount type, in a free-text form.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TaxAuthorisation1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxAuthorisation1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Details of the authorised tax paying party.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Titl" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Title</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Title or position of debtor or the debtor's authorised representative.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Name</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Name of the debtor or the debtor's authorised representative.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxData1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxData1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Details about tax paid, or to be paid, to the government in accordance with the law, including pre-defined parameters such as thresholds and type of account.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="TaxParty1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Creditor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party on the credit side of the transaction to which the tax applies.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="TaxParty2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Debtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Party on the debit side of the transaction to which the tax applies.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="TaxParty2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">UltimateDebtor</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Ultimate party that owes an amount of money to the (ultimate) creditor, in this case, to the taxing authority.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AdmstnZone" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdministrationZone</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Territorial part of a country to which the tax payment is related.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ReferenceNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax reference information that is specific to a taxing agency.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Mtd" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Method</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Method used to indicate the underlying business or how the tax is paid.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TotalTaxableBaseAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Total amount of money on which the tax is based.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxAmt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TotalTaxAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Total amount of money as result of the calculation of the tax.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Date</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Date by which tax is due.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNb" type="Number">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SequenceNumber</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Sequential number of the tax report.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="TaxRecord3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Record</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Record of tax details.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty1">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxParty1</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Details about the entity involved in the tax paid or to be paid.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax identification number of the creditor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RegistrationIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by an organisation, to unambiguously identify a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxType</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of tax payer.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty2">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxParty2</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Details about the entity involved in the tax paid or to be paid.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax identification number of the debtor.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">RegistrationIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Unique identification, as assigned by an organisation, to unambiguously identify a party.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxType</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Type of tax payer.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Authstn" type="TaxAuthorisation1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Authorisation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Details of the authorised tax paying party.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxPeriod3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxPeriod3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Period of time details related to the tax payment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Yr" type="ISOYear">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Year</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Year related to the tax payment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="TaxRecordPeriod1Code">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification of the period related to the tax payment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DatePeriod2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FromToDate</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Range of time between a start date and an end date for which the tax report is provided.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecord3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxRecord3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Set of elements used to define the tax record.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Type</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">High level code to identify the type of tax details.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctgy" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Category</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Specifies the tax code as published by the tax authority.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyDtls" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CategoryDetails</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Provides further details of the category tax code.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrSts" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">DebtorStatus</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Code provided by local authority to identify the status of the party that has drawn up the settlement document.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="CertId" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">CertificateIdentification</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identification number of the tax report as assigned by the taxing authority.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="FrmsCd" type="Max35Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FormsCode</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Identifies, in a coded form, on which template the tax report is to be provided.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Period</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide details on the period of time related to the tax payment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxAmt" type="TaxAmount3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TaxAmount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide information on the amount of the tax record.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">AdditionalInformation</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Further details of the tax record.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecordDetails3">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxRecordDetails3</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Provides information on the individual tax amount(s) per period of the tax record.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Period</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Set of elements used to provide details on the period of time related to the tax payment.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">Amount</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Underlying tax amount related to the specified period.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TaxRecordPeriod1Code">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TaxRecordPeriod1Code</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Specifies the period related to the tax payment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="MM01">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FirstMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the second month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM02">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SecondMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the first month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM03">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ThirdMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the third month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM04">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FourthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the fourth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM05">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FifthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the fifth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM06">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SixthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the sixth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM07">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SeventhMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the seventh month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM08">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EighthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the eighth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM09">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">NinthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the ninth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM10">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TenthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the tenth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM11">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">EleventhMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the eleventh month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="MM12">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">TwelfthMonth</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the twelfth month of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QTR1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FirstQuarter</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the first quarter of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QTR2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SecondQuarter</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the second quarter of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QTR3">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">ThirdQuarter</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the third quarter of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="QTR4">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FourthQuarter</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the forth quarter of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HLF1">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">FirstHalf</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the first half of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="HLF2">
                <xs:annotation>
                    <xs:documentation source="Name" xml:lang="EN">SecondHalf</xs:documentation>
                    <xs:documentation source="Definition" xml:lang="EN">Tax is related to the second half of the period.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="TrueFalseIndicator">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">TrueFalseIndicator</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">A flag indicating a True or False value.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:annotation>
            <xs:documentation source="Name" xml:lang="EN">UUIDv4Identifier</xs:documentation>
            <xs:documentation source="Definition" xml:lang="EN">Universally Unique IDentifier (UUID) version 4, as described in IETC RFC 4122 "Universally Unique IDentifier (UUID) URN Namespace".</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
