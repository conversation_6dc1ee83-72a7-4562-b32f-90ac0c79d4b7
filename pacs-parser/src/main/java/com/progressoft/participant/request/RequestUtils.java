package com.progressoft.participant.request;

import com.progressoft.jfw.ResourceReader;
import org.apache.commons.lang3.StringUtils;
import org.xml.sax.SAXException;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.stream.Stream;

import static javax.xml.XMLConstants.W3C_XML_SCHEMA_NS_URI;
import static javax.xml.validation.SchemaFactory.newInstance;

public class RequestUtils {
    private static final String FORMAT = "yyyy-MM-dd'T'HH:mm:ss";

    private static final String REQUEST_XSD = "request.xsd";
    private static final String REQUEST_TEMPLATE = "request.xml";

    private static final String INVALID_FORMAT = "Invalid Format";
    private static final String INVALID_MESSAGE_TYPE = "Invalid Message Type";
    private static final String INVALID_CONTENT = "Invalid Content";
    private static final String INVALID_MESSAGE_ID = "Invalid Message Id";
    private static final String UNSUPPORTED_MESSAGE_TYPE = "Unsupported message type";

    private static JAXBContext context;
    private static String template;

    private static Schema schema;

    public static Request parse(String xmlMessage) {
        try {
            JAXBContext context = loadContext();
            Schema schema = loadSchema();

            Unmarshaller unmarshaller = context.createUnmarshaller();
            unmarshaller.setSchema(schema);
            try (InputStream stream = new ByteArrayInputStream(xmlMessage.getBytes())) {
                return (Request) unmarshaller.unmarshal(stream);
            }
        } catch (Exception e) {
            throw new EnvelopeParsingException(e);
        }
    }

    public static String serialize(Request request) {
        return template()
                .replace("{id}", request.getId())
                .replace("{type}", request.getType())
                .replace("{format}", request.getFormat())
                .replace("{date}", formatDate(request.getDate()))
                .replace("{signature}", request.getSignature())
                .replace("{content}", request.getContent());
    }

    public static void validate(Request request, String[] supportedMessageTypes, String allowedFormat) {
        if (StringUtils.isBlank(request.getId()))
            throw new EnvelopeValidationException(INVALID_MESSAGE_ID);
        if (StringUtils.isBlank(request.getContent()))
            throw new EnvelopeValidationException(INVALID_CONTENT);
        if (StringUtils.isBlank(request.getType()))
            throw new EnvelopeValidationException(INVALID_MESSAGE_TYPE);
        if (Stream.of(supportedMessageTypes).noneMatch(s -> s.equals(request.getType())))
            throw new EnvelopeValidationException(UNSUPPORTED_MESSAGE_TYPE);
        if (!allowedFormat.equals(request.getFormat()))
            throw new EnvelopeValidationException(INVALID_FORMAT);
    }

    private static String template() {
        if (template == null)
            template = new ResourceReader(RequestUtils.class).content(REQUEST_TEMPLATE);
        return template;
    }

    private static JAXBContext loadContext() throws JAXBException {
        if (context == null)
            context = JAXBContext.newInstance(Request.class);
        return context;
    }

    private static Schema loadSchema() throws SAXException {
        if (schema != null)
            schema = newInstance(W3C_XML_SCHEMA_NS_URI).newSchema(schemaFile());
        return schema;
    }

    private static StreamSource schemaFile() {
        return new StreamSource(new ResourceReader(RequestUtils.class).stream(REQUEST_XSD));
    }

    private static String formatDate(XMLGregorianCalendar date) {
        return new SimpleDateFormat(FORMAT).format(date.toGregorianCalendar().getTime());
    }
}
