package com.progressoft.participant.swift.mx.convertor;


import com.progressoft.jfw.ResourceReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.xml.sax.SAXException;

import jakarta.xml.bind.*;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import java.io.StringReader;
import java.util.concurrent.ConcurrentHashMap;

public class SwiftMXPainParser implements IMessageParser {
    private static final Logger LOG = LoggerFactory.getLogger(SwiftMXPainParser.class);
    public static final String pain_001_001_06 = "pain.001.001.06";
    public static final String pain_002_001_05 = "pain.002.001.05";
    public static final String pain_008_001_04 = "pain.008.001.04";
    public static final String pain_009_001_04 = "pain.009.001.04";
    public static final String pain_010_001_04 = "pain.010.001.04";
    public static final String pain_011_001_04 = "pain.011.001.04";
    public static final String pain_012_001_04 = "pain.012.001.04";

    public static final String pain001_PackageName = "com.progressoft.participant.swift.mx.pain001";
    public static final String pain002_PackageName = "com.progressoft.participant.swift.mx.pain002";
    public static final String pain008_PackageName = "com.progressoft.participant.swift.mx.pain008";
    public static final String pain009_PackageName = "com.progressoft.participant.swift.mx.pain009";
    public static final String pain010_PackageName = "com.progressoft.participant.swift.mx.pain010";
    public static final String pain011_PackageName = "com.progressoft.participant.swift.mx.pain011";
    public static final String pain012_PackageName = "com.progressoft.participant.swift.mx.pain012";

    private ConcurrentHashMap<String, JAXBContext> contexts = new ConcurrentHashMap<>();
    private ConcurrentHashMap<String, Schema> schemas = new ConcurrentHashMap<>();

    private static final SwiftMXPainParser INSTANCE = new SwiftMXPainParser();

    private SwiftMXPainParser() {
    }

    public static SwiftMXPainParser getInstance() {
        return INSTANCE;
    }

    private JAXBContext loadContext(String name) throws JAXBException {
        if (!contexts.containsKey(name))
            contexts.put(name, JAXBContext.newInstance(name));
        return contexts.get(name);
    }

    public Object parseMessage(String xmlMessage, String messageType, StringBuilder errorMessage) {
        errorMessage.setLength(0);
        if (!isMessageTypeSupported(messageType)) {
            errorMessage.append("Not supported  SWIFT MX ISO 20022 message type");
            return null;
        }
        String msgNameSpace = getMessageNameSpace(messageType);
        if (msgNameSpace == "") {
            errorMessage.append("Not supported  SWIFT MX ISO 20022 message type");
            return null;
        }
        if (SwiftMXParser.isPrefixedWithNamespace(xmlMessage, "GrpHdr")) {
            errorMessage.append("ISO20022 message should not be prefixed with namespace reference");
            return null;
        }

        try {
            JAXBContext jc = loadContext(msgNameSpace);
            Unmarshaller u = jc.createUnmarshaller();
            u.setSchema(loadSchema(messageType));
            StringReader reader = new StringReader(xmlMessage);
            return ((JAXBElement<?>) u.unmarshal(reader)).getValue();
        } catch (UnmarshalException ue) {
            LOG.error("Error while parsing message", ue);
            errorMessage.append(ue.getCause().toString());
        } catch (Exception e) {
            LOG.error("Error while parsing message", e);
            errorMessage.append(e.toString());
        }

        return null;
    }

    private Schema loadSchema(String messageType) throws SAXException {
        if (!schemas.containsKey(messageType)) {
            SchemaFactory factory = SchemaFactory.newInstance(javax.xml.XMLConstants.W3C_XML_SCHEMA_NS_URI);
            Source schemaFile = new StreamSource(new ResourceReader(getClass()).stream(messageType + ".xsd"));
            schemas.put(messageType, factory.newSchema(schemaFile));
        }

        return schemas.get(messageType);
    }

    private String getMessageNameSpace(String messageType) {
        if (messageType.equals(pain_008_001_04))
            return "com.progressoft.participant.swift.mx.pain008";
        if (messageType.equals(pain_001_001_06))
            return "com.progressoft.participant.swift.mx.pain001";
        if (messageType.equals(pain_002_001_05))
            return "com.progressoft.participant.swift.mx.pain002";
        if (messageType.equals(pain_009_001_04))
            return "com.progressoft.participant.swift.mx.pain009";
        if (messageType.equals(pain_010_001_04))
            return "com.progressoft.participant.swift.mx.pain010";
        if (messageType.equals(pain_011_001_04))
            return "com.progressoft.participant.swift.mx.pain011";
        if (messageType.equals(pain_012_001_04))
            return "com.progressoft.participant.swift.mx.pain012";
        return "";
    }

    private Boolean isMessageTypeSupported(String messageType) {
        if (messageType.equals(pain_008_001_04) || messageType.equals(pain_002_001_05) || messageType.equals(pain_001_001_06)
                || messageType.equals(pain_009_001_04) || messageType.equals(pain_010_001_04)
                || messageType.equals(pain_011_001_04) || messageType.equals(pain_012_001_04))
            return true;
        else
            return false;
    }

}
