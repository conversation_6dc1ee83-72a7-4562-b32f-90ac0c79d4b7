package com.progressoft.participant.swift.mx.validator.iso;

import com.progressoft.participant.swift.mx.isorule.*;
import com.progressoft.participant.swift.mx.isorule.camt029.CancellationStatusReasonInformationRule;
import com.progressoft.participant.swift.mx.isorule.camt056.GroupCancellationAndNumberOfTransactionsRule;
import com.progressoft.participant.swift.mx.isorule.camt056.GroupCancellationAndReasonRule;
import com.progressoft.participant.swift.mx.isorule.camt056.GroupCancellationFalseAndTransactionInformationRule;
import com.progressoft.participant.swift.mx.isorule.camt056.GroupCancellationTrueAndTransactionInformationRule;
import com.progressoft.participant.swift.mx.pacs002.FIToFIPaymentStatusReportV06;
import com.progressoft.participant.swift.mx.pacs002v10.FIToFIPaymentStatusReportV12;
import com.progressoft.participant.swift.mx.pacs004.PaymentReturnV05;
import com.progressoft.participant.swift.mx.pacs007.FIToFIPaymentReversalV05;
import com.progressoft.participant.swift.mx.pain008.Document;
import iso.std.iso._20022.tech.xsd.pacs_002_001.Pacs00200102;
import iso.std.iso._20022.tech.xsd.pacs_002_001.StatusReasonInformation1;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ISORulesValidator {

    private static final Logger LOG = LoggerFactory.getLogger(ISORulesValidator.class);

    public static void validateMessageIsoRules(Object message, StringBuilder errorMessage) {
        try {
            errorMessage.setLength(0);
            if (message instanceof com.progressoft.participant.swift.mx.pacs003v10.Document) {
                errorMessage.append(validateDDV10((com.progressoft.participant.swift.mx.pacs003v10.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs003.Document) {
                errorMessage.append(validateDD((com.progressoft.participant.swift.mx.pacs003.Document) message));
            } else if (message instanceof iso.std.iso._20022.tech.xsd.pacs_003_001.Document) {
                errorMessage.append(validateDDV1((iso.std.iso._20022.tech.xsd.pacs_003_001.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs008.Document) {
                errorMessage.append(validateDC((com.progressoft.participant.swift.mx.pacs008.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs008v10.Document) {
                errorMessage.append(validateDCV10((com.progressoft.participant.swift.mx.pacs008v10.Document) message));
            } else if (message instanceof iso.std.iso._20022.tech.xsd.pacs_008_001.Document) {
                errorMessage.append(validateDCV1((iso.std.iso._20022.tech.xsd.pacs_008_001.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs002.Document) {
                errorMessage.append(validatePSR((com.progressoft.participant.swift.mx.pacs002.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs002v10.Document) {
                errorMessage.append(validatePSRV10((com.progressoft.participant.swift.mx.pacs002v10.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs004.Document) {
                errorMessage.append(validateRTRN((com.progressoft.participant.swift.mx.pacs004.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pacs007.Document) {
                errorMessage.append(validateRVSL((com.progressoft.participant.swift.mx.pacs007.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pain008.Document) {
                errorMessage.append(validatePain008((com.progressoft.participant.swift.mx.pain008.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.pain001.Document) {
                errorMessage.append(validatePain001((com.progressoft.participant.swift.mx.pain001.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.camt056.Document) {
                errorMessage.append(validateCamt056((com.progressoft.participant.swift.mx.camt056.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.camt056v10.Document) {
                errorMessage.append(validateCamt056V10((com.progressoft.participant.swift.mx.camt056v10.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.camt029.Document) {
                errorMessage.append(validateCamt029((com.progressoft.participant.swift.mx.camt029.Document) message));
            } else if (message instanceof com.progressoft.participant.swift.mx.camt029v10.Document) {
                errorMessage.append(validateCamt029V10((com.progressoft.participant.swift.mx.camt029v10.Document) message));
            } else if (message instanceof iso.std.iso._20022.tech.xsd.pacs_006_001.Document) {
                errorMessage.append(validatePacs6v1((iso.std.iso._20022.tech.xsd.pacs_006_001.Document) message));
            }else if (message instanceof iso.std.iso._20022.tech.xsd.pacs_002_001.Document) {
                errorMessage.append(validatePSRV1((iso.std.iso._20022.tech.xsd.pacs_002_001.Document) message));
            }

        } catch (Exception ex) {
            LOG.error("An error occured while validating ISO rules.", ex);
            errorMessage.append("error while validating ISO20022 message rules\nerror details:\n").append(ex.getMessage());
        }
    }

    private static StringBuilder validatePacs6v1 (iso.std.iso._20022.tech.xsd.pacs_006_001.Document message) {
        StringBuilder validationError = new StringBuilder();
        if (!InstructedAgentRule.isValid(message.getPacs00600101()))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getPacs00600101()))
            validationError.append(InstructingAgentRule.RULE);
        if (!GroupCancellationAndTransInfoRule.isValid(message.getPacs00600101()))
            validationError.append(GroupCancellationAndTransInfoRule.RULE);
        if (!GroupCancellationRule1.isValid(message.getPacs00600101()))
            validationError.append(GroupCancellationRule1.RULE);
        if (!GroupCancellationRule2.isValid(message.getPacs00600101()))
            validationError.append(GroupCancellationRule2.RULE);
        if (!GroupCancellationRule3.isValid(message.getPacs00600101()))
            validationError.append(GroupCancellationRule3.RULE);
        return validationError;
    }

    private static Object validateCamt056(com.progressoft.participant.swift.mx.camt056.Document message) {
        StringBuilder validationError = new StringBuilder();
        if (!GroupCancellationAndNumberOfTransactionsRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationAndNumberOfTransactionsRule.RULE);
        if (!GroupCancellationAndReasonRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationAndReasonRule.RULE);
        if (!GroupCancellationFalseAndTransactionInformationRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationFalseAndTransactionInformationRule.RULE);
        if (!GroupCancellationTrueAndTransactionInformationRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationTrueAndTransactionInformationRule.RULE);
        return validationError;
    }

    private static Object validateCamt056V10(com.progressoft.participant.swift.mx.camt056v10.Document message) {
        StringBuilder validationError = new StringBuilder();
        if (!GroupCancellationAndNumberOfTransactionsRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationAndNumberOfTransactionsRule.RULE);
        if (!GroupCancellationAndReasonRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationAndReasonRule.RULE);
        if (!GroupCancellationFalseAndTransactionInformationRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationFalseAndTransactionInformationRule.RULE);
        if (!GroupCancellationTrueAndTransactionInformationRule.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationTrueAndTransactionInformationRule.RULE);
        if (!GroupCancellationRule3.isValid(message.getFIToFIPmtCxlReq()))
            validationError.append(GroupCancellationRule3.RULE);
        return validationError;
    }

    private static Object validateCamt029(com.progressoft.participant.swift.mx.camt029.Document message) {

        StringBuilder validationError = new StringBuilder();
        if (!CancellationStatusReasonInformationRule.isValid(message.getRsltnOfInvstgtn()))
            validationError.append(CancellationStatusReasonInformationRule.RULE);

        return validationError;
    }

    private static Object validateCamt029V10(com.progressoft.participant.swift.mx.camt029v10.Document message) {

        StringBuilder validationError = new StringBuilder();
        if (!CancellationStatusReasonInformationRule.isValid(message.getRsltnOfInvstgtn()))
            validationError.append(CancellationStatusReasonInformationRule.RULE);

        return validationError;
    }

    private static Object validatePain008(Document message) {
        StringBuilder validationError = new StringBuilder();
        if (!PainNumberOfTransactionsRule.isValid(message.getCstmrDrctDbtInitn()))
            validationError.append(PainNumberOfTransactionsRule.RULE);

        return validationError;
    }

    private static Object validatePain001(com.progressoft.participant.swift.mx.pain001.Document message) {
        StringBuilder validationError = new StringBuilder();
        if (!PainNumberOfTransactionsRule.isValid(message.getCstmrCdtTrfInitn()))
            validationError.append(PainNumberOfTransactionsRule.RULE);

        return validationError;
    }

    private static StringBuilder validateDD(com.progressoft.participant.swift.mx.pacs003.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        return result;
    }

    private static StringBuilder validateDDV10(com.progressoft.participant.swift.mx.pacs003v10.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getFIToFICstmrDrctDbt()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        return result;
    }

    private static StringBuilder validateDDV1(iso.std.iso._20022.tech.xsd.pacs_003_001.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getPacs00300101()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getPacs00300101()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getPacs00300101()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getPacs00300101()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getPacs00300101()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getPacs00300101()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getPacs00300101()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!TotalInterbankSettlementAmountAndDateRule.isValid(message.getPacs00300101()))
            result.append(TotalInterbankSettlementAmountAndDateRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getPacs00300101()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getPacs00300101()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getPacs00300101()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getPacs00300101()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule1.isValid(message.getPacs00300101()))
            result.append(InstructedAmountAndExchangeRateRule1.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getPacs00300101()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        if (!AmendmentIndicatorRule.isValid(message.getPacs00300101()))
            result.append(AmendmentIndicatorRule.RULE);
        return result;
    }

    private static StringBuilder validateDC(com.progressoft.participant.swift.mx.pacs008.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        return result;
    }

    private static StringBuilder validateDCV10(com.progressoft.participant.swift.mx.pacs008v10.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getFIToFICstmrCdtTrf()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        return result;
    }

    private static StringBuilder validateDCV1(iso.std.iso._20022.tech.xsd.pacs_008_001.Document message) {
        StringBuilder result = new StringBuilder();
        if (!NumberOfTransactionRule.isValid(message.getPacs00800101()))
            result.append(NumberOfTransactionRule.RULE);
        if (!InstructedAgentRule.isValid(message.getPacs00800101()))
            result.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(message.getPacs00800101()))
            result.append(InstructingAgentRule.RULE);
        if (!TotalInterbankSettlmentAmountRule.isValid(message.getPacs00800101()))
            result.append(TotalInterbankSettlmentAmountRule.RULE);
        if (!TotalInterbankSettlmentCurrencyRule.isValid(message.getPacs00800101()))
            result.append(TotalInterbankSettlmentCurrencyRule.RULE);
        if (!TotalInterbankSettlementAmountAndDateRule.isValid(message.getPacs00800101()))
            result.append(TotalInterbankSettlementAmountAndDateRule.RULE);
        if (!InterbankSettlmentDateRule.isValid(message.getPacs00800101()))
            result.append(InterbankSettlmentDateRule.RULE);
        if (!PaymentTypeInformationRule.isValid(message.getPacs00800101()))
            result.append(PaymentTypeInformationRule.RULE);
        if (!SettlmentMethodRule1.isValid(message.getPacs00800101()))
            result.append(SettlmentMethodRule1.RULE);
        if (!SettlmentMethodRule2.isValid(message.getPacs00800101()))
            result.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(message.getPacs00800101()))
            result.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(message.getPacs00800101()))
            result.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule1.isValid(message.getPacs00800101()))
            result.append(InstructedAmountAndExchangeRateRule1.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(message.getPacs00800101()))
            result.append(InstructedAmountAndExchangeRateRule2.RULE);
        return result;
    }

    private static StringBuilder validatePSR(com.progressoft.participant.swift.mx.pacs002.Document message) {
        StringBuilder validationError = new StringBuilder();
        FIToFIPaymentStatusReportV06 psr = message.getFIToFIPmtStsRpt();
        if (psr.getOrgnlGrpInfAndSts().isEmpty())
            validationError.append("Original group information are not provided");
        if (!InstructedAgentRule.isValid(psr))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(psr))
            validationError.append(InstructingAgentRule.RULE);
        if (!GroupAndTransactionsStatusRule1.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule1.RULE);
        if (!GroupAndTransactionsStatusRule2.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule2.RULE);
        if (!GroupAndTransactionsStatusRule3.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule3.RULE);
        if (!GroupAndTransactionsStatusRule4.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule4.RULE);

        return validationError;
    }

    private static StringBuilder validatePSRV10(com.progressoft.participant.swift.mx.pacs002v10.Document message) {
        StringBuilder validationError = new StringBuilder();
        FIToFIPaymentStatusReportV12 psr = message.getFIToFIPmtStsRpt();
        if (psr.getOrgnlGrpInfAndSts().isEmpty())
            validationError.append("Original group information are not provided");
        if (!InstructedAgentRule.isValid(psr))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(psr))
            validationError.append(InstructingAgentRule.RULE);
        if (!GroupAndTransactionsStatusRule1.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule1.RULE);
        if (!GroupAndTransactionsStatusRule2.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule2.RULE);
        if (!GroupAndTransactionsStatusRule3.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule3.RULE);
        if (!GroupAndTransactionsStatusRule4.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule4.RULE);

        return validationError;
    }

    private static StringBuilder validateRTRN(com.progressoft.participant.swift.mx.pacs004.Document message) {
        StringBuilder validationError = new StringBuilder();
        PaymentReturnV05 rtrn = message.getPmtRtr();
        if (!InstructedAgentRule.isValid(rtrn))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(rtrn))
            validationError.append(InstructingAgentRule.RULE);
        if (!SettlmentMethodRule2.isValid(rtrn))
            validationError.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(rtrn))
            validationError.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(rtrn))
            validationError.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(rtrn))
            validationError.append(InstructedAmountAndExchangeRateRule2.RULE);
        if (!GroupReturnAndReturnReasonRule.isValid(rtrn))
            validationError.append(GroupReturnAndReturnReasonRule.RULE);
        if (!GroupReturnAndNumberOfTransInfoRule.isValid(rtrn))
            validationError.append(GroupReturnAndNumberOfTransInfoRule.RULE);
        if (!TotalReturnedInterBankSettleAmountAndDateRule.isValid(rtrn))
            validationError.append(TotalReturnedInterBankSettleAmountAndDateRule.RULE);
        if (!TotalReturnedInterBankSettleAmountRule1.isValid(rtrn))
            validationError.append(TotalReturnedInterBankSettleAmountRule1.RULE);
        if (!TotalReturnedInterBankSettleAmountRule2.isValid(rtrn))
            validationError.append(TotalReturnedInterBankSettleAmountRule2.RULE);
        if (!ControlSumAndGroupReturnRule.isValid(rtrn))
            validationError.append(ControlSumAndGroupReturnRule.RULE);
        return validationError;
    }

    private static StringBuilder validateRVSL(com.progressoft.participant.swift.mx.pacs007.Document message) {
        StringBuilder validationError = new StringBuilder();
        FIToFIPaymentReversalV05 rvrsl = message.getFIToFIPmtRvsl();
        if (!InstructedAgentRule.isValid(rvrsl))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(rvrsl))
            validationError.append(InstructingAgentRule.RULE);
        if (!SettlmentMethodRule2.isValid(rvrsl))
            validationError.append(SettlmentMethodRule2.RULE);
        if (!ChargesAmountRule.isValid(rvrsl))
            validationError.append(ChargesAmountRule.RULE);
        if (!ChargesInfoAndInstructedAmountRule.isValid(rvrsl))
            validationError.append(ChargesInfoAndInstructedAmountRule.RULE);
        if (!InstructedAmountAndExchangeRateRule2.isValid(rvrsl))
            validationError.append(InstructedAmountAndExchangeRateRule2.RULE);
        if (!TotalReversedInterBankSettleAmountAndDateRule.isValid(rvrsl))
            validationError.append(TotalReversedInterBankSettleAmountAndDateRule.RULE);
        if (!TotalReversedInterBankSettleAmountRule1.isValid(rvrsl))
            validationError.append(TotalReturnedInterBankSettleAmountRule1.RULE);
        if (!TotalReversedInterBankSettleAmountRule2.isValid(rvrsl))
            validationError.append(TotalReversedInterBankSettleAmountRule2.RULE);

        return validationError;
    }

    private static StringBuilder validatePSRV1(iso.std.iso._20022.tech.xsd.pacs_002_001.Document message) {
        StringBuilder validationError = new StringBuilder();
        Pacs00200102 psr = message.getPacs00200102();
        if (psr.getOrgnlGrpInfAndSts() == null)
            validationError.append("Original group information are not provided");
        if (!InstructedAgentRule.isValid(psr))
            validationError.append(InstructedAgentRule.RULE);
        if (!InstructingAgentRule.isValid(psr))
            validationError.append(InstructingAgentRule.RULE);
        if (!GroupAndTransactionsStatusRule1.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule1.RULE);
        if (!GroupAndTransactionsStatusRule2.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule2.RULE);
        if (!GroupAndTransactionsStatusRule3.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule3.RULE);
        if (!GroupAndTransactionsStatusRule4.isValid(psr))
            validationError.append(GroupAndTransactionsStatusRule4.RULE);
        if (!StatusReasonInformationRule.isValid(psr))
            validationError.append(StatusReasonInformationRule.RULE);
        if (!psr.getOrgnlGrpInfAndSts().getStsRsnInf().isEmpty()) {
            StatusReasonInformation1 statusReasonInformation = psr.getOrgnlGrpInfAndSts().getStsRsnInf().get(0);
            if (statusReasonInformation.getStsRsn().getCd() != null) {
                if (!StatusReasonRule.isValid(psr))
                    validationError.append(StatusReasonRule.RULE);
                if (!StatusReasonRule.isValid(psr.getOrgnlGrpInfAndSts()))
                    validationError.append(StatusReasonRule.RULE);
            }
        }
        return validationError;
    }
}
