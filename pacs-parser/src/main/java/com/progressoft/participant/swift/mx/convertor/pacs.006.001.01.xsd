<?xml version="1.0" encoding="UTF-8"?>
<!--Generated by SWIFTStandards Workstation (build:R5.1.0.4) on 2006 Sep 08 11:21:07-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.006.001.01" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.006.001.01">
<xs:element name="Document" type="Document"/>
<xs:complexType name="AccountIdentification3Choice">
<xs:sequence>
<xs:choice>
<xs:element name="IBAN" type="IBANIdentifier"/>
<xs:element name="BBAN" type="BBANIdentifier"/>
<xs:element name="UPIC" type="UPICIdentifier"/>
<xs:element name="PrtryAcct" type="SimpleIdentificationInformation2"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="AddressType2Code">
<xs:restriction base="xs:string">
<xs:enumeration value="ADDR"/>
<xs:enumeration value="PBOX"/>
<xs:enumeration value="HOME"/>
<xs:enumeration value="BIZZ"/>
<xs:enumeration value="MLTO"/>
<xs:enumeration value="DLVY"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="AmendmentInformationDetails1">
<xs:sequence>
<xs:element name="OrgnlMndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlCdtrSchmeId" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlCdtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlCdtrAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlDbtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlDbtrAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlDbtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlDbtrAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlFnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlFrqcy" type="Frequency1Code" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="AmountType2Choice">
<xs:sequence>
<xs:choice>
<xs:element name="InstdAmt" type="CurrencyAndAmount"/>
<xs:element name="EqvtAmt" type="EquivalentAmount"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="BBANIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[a-zA-Z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BEIIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="BICIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="BranchAndFinancialInstitutionIdentification3">
<xs:sequence>
<xs:element name="FinInstnId" type="FinancialInstitutionIdentification5Choice"/>
<xs:element name="BrnchId" type="BranchData" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="BranchData">
<xs:sequence>
<xs:element name="Id" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="Nm" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="PstlAdr" type="PostalAddress1" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="CHIPSUniversalIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="CH[0-9]{6,6}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CancellationReason1Choice">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="CancellationReason2Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="CancellationReason2Code">
<xs:restriction base="xs:string">
<xs:enumeration value="CUST"/>
<xs:enumeration value="DUPL"/>
<xs:enumeration value="AGNT"/>
<xs:enumeration value="CURR"/>
<xs:enumeration value="UPAY"/>
<xs:enumeration value="SUSP"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CancellationReasonInformation1">
<xs:sequence>
<xs:element name="CxlOrgtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="CxlRsn" type="CancellationReason1Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="AddtlCxlRsnInf" type="Max105Text" minOccurs="0" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CashAccount7">
<xs:sequence>
<xs:element name="Id" type="AccountIdentification3Choice"/>
<xs:element name="Tp" type="CashAccountType2" minOccurs="0" maxOccurs="1"/>
<xs:element name="Ccy" type="CurrencyCode" minOccurs="0" maxOccurs="1"/>
<xs:element name="Nm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CashAccountType2">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="CashAccountType4Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="CashAccountType4Code">
<xs:restriction base="xs:string">
<xs:enumeration value="CASH"/>
<xs:enumeration value="CHAR"/>
<xs:enumeration value="COMM"/>
<xs:enumeration value="TAXE"/>
<xs:enumeration value="CISH"/>
<xs:enumeration value="TRAS"/>
<xs:enumeration value="SACC"/>
<xs:enumeration value="CACC"/>
<xs:enumeration value="SVGS"/>
<xs:enumeration value="ONDP"/>
<xs:enumeration value="MGLD"/>
<xs:enumeration value="NREX"/>
<xs:enumeration value="MOMA"/>
<xs:enumeration value="LOAN"/>
<xs:enumeration value="SLRY"/>
<xs:enumeration value="ODFT"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="CashClearingSystem3Code">
<xs:restriction base="xs:string">
<xs:enumeration value="ABE"/>
<xs:enumeration value="ART"/>
<xs:enumeration value="AVP"/>
<xs:enumeration value="AZM"/>
<xs:enumeration value="BAP"/>
<xs:enumeration value="BEL"/>
<xs:enumeration value="BOF"/>
<xs:enumeration value="BRL"/>
<xs:enumeration value="CAD"/>
<xs:enumeration value="CAM"/>
<xs:enumeration value="CBJ"/>
<xs:enumeration value="CHP"/>
<xs:enumeration value="DKC"/>
<xs:enumeration value="RTP"/>
<xs:enumeration value="EBA"/>
<xs:enumeration value="ELS"/>
<xs:enumeration value="ERP"/>
<xs:enumeration value="XCT"/>
<xs:enumeration value="HRK"/>
<xs:enumeration value="HRM"/>
<xs:enumeration value="HUF"/>
<xs:enumeration value="LGS"/>
<xs:enumeration value="LVL"/>
<xs:enumeration value="MUP"/>
<xs:enumeration value="NOC"/>
<xs:enumeration value="PCH"/>
<xs:enumeration value="PDS"/>
<xs:enumeration value="PEG"/>
<xs:enumeration value="PNS"/>
<xs:enumeration value="PVE"/>
<xs:enumeration value="SEC"/>
<xs:enumeration value="SIT"/>
<xs:enumeration value="SLB"/>
<xs:enumeration value="SPG"/>
<xs:enumeration value="SSK"/>
<xs:enumeration value="TBF"/>
<xs:enumeration value="TGT"/>
<xs:enumeration value="TOP"/>
<xs:enumeration value="FDW"/>
<xs:enumeration value="BOJ"/>
<xs:enumeration value="FEY"/>
<xs:enumeration value="ZEN"/>
<xs:enumeration value="DDK"/>
<xs:enumeration value="AIP"/>
<xs:enumeration value="BCC"/>
<xs:enumeration value="BDS"/>
<xs:enumeration value="BGN"/>
<xs:enumeration value="BHS"/>
<xs:enumeration value="BIS"/>
<xs:enumeration value="BSP"/>
<xs:enumeration value="EPM"/>
<xs:enumeration value="EPN"/>
<xs:enumeration value="FDA"/>
<xs:enumeration value="GIS"/>
<xs:enumeration value="INC"/>
<xs:enumeration value="JOD"/>
<xs:enumeration value="KPS"/>
<xs:enumeration value="LKB"/>
<xs:enumeration value="MEP"/>
<xs:enumeration value="MRS"/>
<xs:enumeration value="NAM"/>
<xs:enumeration value="PTR"/>
<xs:enumeration value="ROL"/>
<xs:enumeration value="ROS"/>
<xs:enumeration value="SCP"/>
<xs:enumeration value="STG"/>
<xs:enumeration value="THB"/>
<xs:enumeration value="TIS"/>
<xs:enumeration value="TTD"/>
<xs:enumeration value="UIS"/>
<xs:enumeration value="MOS"/>
<xs:enumeration value="ZET"/>
<xs:enumeration value="ZIS"/>
<xs:enumeration value="CHI"/>
<xs:enumeration value="COP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ClearingChannel2Code">
<xs:restriction base="xs:string">
<xs:enumeration value="RTGS"/>
<xs:enumeration value="RTNS"/>
<xs:enumeration value="MPNS"/>
<xs:enumeration value="BOOK"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="ClearingSystemIdentification1Choice">
<xs:sequence>
<xs:choice>
<xs:element name="ClrSysId" type="CashClearingSystem3Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ClearingSystemMemberIdentification3Choice">
<xs:sequence>
<xs:choice>
<xs:element name="Id" type="ExternalClearingSystemMemberCode"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="CountryCode">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{2,2}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CreditorReferenceInformation1">
<xs:sequence>
<xs:element name="CdtrRefTp" type="CreditorReferenceType1" minOccurs="0" maxOccurs="1"/>
<xs:element name="CdtrRef" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="CreditorReferenceType1">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="DocumentType3Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="CurrencyAndAmount_SimpleType">
<xs:restriction base="xs:decimal">
<xs:minInclusive value="0"/>
<xs:fractionDigits value="5"/>
<xs:totalDigits value="18"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="CurrencyAndAmount">
<xs:simpleContent>
<xs:extension base="CurrencyAndAmount_SimpleType">
<xs:attribute name="Ccy" type="CurrencyCode" use="required"/>
</xs:extension>
</xs:simpleContent>
</xs:complexType>
<xs:simpleType name="CurrencyCode">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{3,3}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="DateAndPlaceOfBirth">
<xs:sequence>
<xs:element name="BirthDt" type="ISODate"/>
<xs:element name="PrvcOfBirth" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="CityOfBirth" type="Max35Text"/>
<xs:element name="CtryOfBirth" type="CountryCode"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="DecimalNumber">
<xs:restriction base="xs:decimal">
<xs:fractionDigits value="17"/>
<xs:totalDigits value="18"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="Document">
<xs:sequence>
<xs:element name="pacs.006.001.01" type="pacs.006.001.01"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="DocumentType2Code">
<xs:restriction base="xs:string">
<xs:enumeration value="MSIN"/>
<xs:enumeration value="CNFA"/>
<xs:enumeration value="DNFA"/>
<xs:enumeration value="CINV"/>
<xs:enumeration value="CREN"/>
<xs:enumeration value="DEBN"/>
<xs:enumeration value="HIRI"/>
<xs:enumeration value="SBIN"/>
<xs:enumeration value="CMCN"/>
<xs:enumeration value="SOAC"/>
<xs:enumeration value="DISP"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DocumentType3Code">
<xs:restriction base="xs:string">
<xs:enumeration value="RADM"/>
<xs:enumeration value="RPIN"/>
<xs:enumeration value="FXDR"/>
<xs:enumeration value="DISP"/>
<xs:enumeration value="PUOR"/>
<xs:enumeration value="SCOR"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="DunsIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{9,9}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="EANGLNIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{13,13}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="EquivalentAmount">
<xs:sequence>
<xs:element name="Amt" type="CurrencyAndAmount"/>
<xs:element name="CcyOfTrf" type="CurrencyCode"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="ExternalClearingSystemMemberCode">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="35"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ExternalLocalInstrumentCode">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="35"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="FinancialInstitutionIdentification3">
<xs:sequence>
<xs:element name="BIC" type="BICIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="ClrSysMmbId" type="ClearingSystemMemberIdentification3Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="Nm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="PstlAdr" type="PostalAddress1" minOccurs="0" maxOccurs="1"/>
<xs:element name="PrtryId" type="GenericIdentification3" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="FinancialInstitutionIdentification5Choice">
<xs:sequence>
<xs:choice>
<xs:element name="BIC" type="BICIdentifier"/>
<xs:element name="ClrSysMmbId" type="ClearingSystemMemberIdentification3Choice"/>
<xs:element name="NmAndAdr" type="NameAndAddress7"/>
<xs:element name="PrtryId" type="GenericIdentification3"/>
<xs:element name="CmbndId" type="FinancialInstitutionIdentification3"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="Frequency1Code">
<xs:restriction base="xs:string">
<xs:enumeration value="YEAR"/>
<xs:enumeration value="MNTH"/>
<xs:enumeration value="QURT"/>
<xs:enumeration value="MIAN"/>
<xs:enumeration value="WEEK"/>
<xs:enumeration value="DAIL"/>
<xs:enumeration value="ADHO"/>
<xs:enumeration value="INDA"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="GenericIdentification3">
<xs:sequence>
<xs:element name="Id" type="Max35Text"/>
<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="GenericIdentification4">
<xs:sequence>
<xs:element name="Id" type="Max35Text"/>
<xs:element name="IdTp" type="Max35Text"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="GroupHeader7">
<xs:sequence>
<xs:element name="MsgId" type="Max35Text"/>
<xs:element name="CreDtTm" type="ISODateTime"/>
<xs:element name="NbOfTxs" type="Max15NumericText"/>
<xs:element name="CtrlSum" type="DecimalNumber" minOccurs="0" maxOccurs="1"/>
<xs:element name="GrpCxl" type="GroupingIndicator" minOccurs="0" maxOccurs="1"/>
<xs:element name="InitgPty" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="FwdgAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstgAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstdAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="GroupingIndicator">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="IBANIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[a-zA-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="IBEIIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[A-Z]{2,2}[B-DF-HJ-NP-TV-XZ0-9]{7,7}[0-9]{1,1}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ISODate">
<xs:restriction base="xs:date"/>
</xs:simpleType>
<xs:simpleType name="ISODateTime">
<xs:restriction base="xs:dateTime"/>
</xs:simpleType>
<xs:complexType name="LocalInstrument1Choice">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="ExternalLocalInstrumentCode"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="MandateRelatedInformation1">
<xs:sequence>
<xs:element name="MndtId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="DtOfSgntr" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="AmdmntInd" type="TrueFalseIndicator" minOccurs="0" maxOccurs="1"/>
<xs:element name="AmdmntInfDtls" type="AmendmentInformationDetails1" minOccurs="0" maxOccurs="1"/>
<xs:element name="ElctrncSgntr" type="Max1025Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="FrstColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="FnlColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="Frqcy" type="Frequency1Code" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="Max1025Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="1025"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max105Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="105"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max140Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="140"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max15NumericText">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{1,15}"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max16Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="16"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max34Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="34"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max35Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="35"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="Max70Text">
<xs:restriction base="xs:string">
<xs:minLength value="1"/>
<xs:maxLength value="70"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="NameAndAddress7">
<xs:sequence>
<xs:element name="Nm" type="Max70Text"/>
<xs:element name="PstlAdr" type="PostalAddress1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="OrganisationIdentification2">
<xs:sequence>
<xs:element name="BIC" type="BICIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="IBEI" type="IBEIIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="BEI" type="BEIIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="EANGLN" type="EANGLNIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="USCHU" type="CHIPSUniversalIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="DUNS" type="DunsIdentifier" minOccurs="0" maxOccurs="1"/>
<xs:element name="BkPtyId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="TaxIdNb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="PrtryId" type="GenericIdentification3" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="OriginalGroupInformation4">
<xs:sequence>
<xs:element name="OrgnlMsgId" type="Max35Text"/>
<xs:element name="OrgnlMsgNmId" type="Max35Text"/>
<xs:element name="OrgnlCreDtTm" type="ISODateTime" minOccurs="0" maxOccurs="1"/>
<xs:element name="CxlRsnInf" type="CancellationReasonInformation1" minOccurs="0" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="OriginalTransactionReference1">
<xs:sequence>
<xs:element name="IntrBkSttlmAmt" type="CurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
<xs:element name="Amt" type="AmountType2Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="IntrBkSttlmDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:choice>
<xs:element name="ReqdExctnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="ReqdColltnDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
</xs:choice>
<xs:element name="CdtrSchmeId" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="SttlmInf" type="SettlementInformation3" minOccurs="0" maxOccurs="1"/>
<xs:element name="PmtTpInf" type="PaymentTypeInformation6" minOccurs="0" maxOccurs="1"/>
<xs:element name="PmtMtd" type="PaymentMethod4Code" minOccurs="0" maxOccurs="1"/>
<xs:element name="MndtRltdInf" type="MandateRelatedInformation1" minOccurs="0" maxOccurs="1"/>
<xs:element name="RmtInf" type="RemittanceInformation1" minOccurs="0" maxOccurs="1"/>
<xs:element name="UltmtDbtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="Dbtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="DbtrAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="DbtrAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="CdtrAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="Cdtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="CdtrAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="UltmtCdtr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="Party2Choice">
<xs:sequence>
<xs:choice>
<xs:element name="OrgId" type="OrganisationIdentification2"/>
<xs:element name="PrvtId" type="PersonIdentification3" minOccurs="1" maxOccurs="4"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="PartyIdentification8">
<xs:sequence>
<xs:element name="Nm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="PstlAdr" type="PostalAddress1" minOccurs="0" maxOccurs="1"/>
<xs:element name="Id" type="Party2Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="CtryOfRes" type="CountryCode" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="PaymentCategoryPurpose1Code">
<xs:restriction base="xs:string">
<xs:enumeration value="CORT"/>
<xs:enumeration value="SALA"/>
<xs:enumeration value="TREA"/>
<xs:enumeration value="CASH"/>
<xs:enumeration value="DIVI"/>
<xs:enumeration value="GOVT"/>
<xs:enumeration value="INTE"/>
<xs:enumeration value="LOAN"/>
<xs:enumeration value="PENS"/>
<xs:enumeration value="SECU"/>
<xs:enumeration value="SSBE"/>
<xs:enumeration value="SUPP"/>
<xs:enumeration value="TAXS"/>
<xs:enumeration value="TRAD"/>
<xs:enumeration value="VATX"/>
<xs:enumeration value="HEDG"/>
<xs:enumeration value="INTC"/>
<xs:enumeration value="WHLD"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="PaymentMethod4Code">
<xs:restriction base="xs:string">
<xs:enumeration value="CHK"/>
<xs:enumeration value="TRF"/>
<xs:enumeration value="DD"/>
<xs:enumeration value="TRA"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="PaymentTransactionInformation3">
<xs:sequence>
<xs:element name="CxlId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlPmtInfId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlInstrId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlEndToEndId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlTxId" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlIntrBkSttlmAmt" type="CurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
<xs:element name="OrgnlInstdAmt" type="CurrencyAndAmount" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstgAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstdAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="CxlRsnInf" type="CancellationReasonInformation1" minOccurs="0" maxOccurs="unbounded"/>
<xs:element name="OrgnlTxRef" type="OriginalTransactionReference1" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="PaymentTypeInformation6">
<xs:sequence>
<xs:element name="InstrPrty" type="Priority2Code" minOccurs="0" maxOccurs="1"/>
<xs:choice>
<xs:element name="SvcLvl" type="ServiceLevel2Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="ClrChanl" type="ClearingChannel2Code" minOccurs="0" maxOccurs="1"/>
</xs:choice>
<xs:element name="LclInstrm" type="LocalInstrument1Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="SeqTp" type="SequenceType1Code" minOccurs="0" maxOccurs="1"/>
<xs:element name="CtgyPurp" type="PaymentCategoryPurpose1Code" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="PersonIdentification3">
<xs:sequence>
<xs:choice>
<xs:element name="DrvrsLicNb" type="Max35Text"/>
<xs:element name="CstmrNb" type="Max35Text"/>
<xs:element name="SclSctyNb" type="Max35Text"/>
<xs:element name="AlnRegnNb" type="Max35Text"/>
<xs:element name="PsptNb" type="Max35Text"/>
<xs:element name="TaxIdNb" type="Max35Text"/>
<xs:element name="IdntyCardNb" type="Max35Text"/>
<xs:element name="MplyrIdNb" type="Max35Text"/>
<xs:element name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth"/>
<xs:element name="OthrId" type="GenericIdentification4"/>
</xs:choice>
<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="PostalAddress1">
<xs:sequence>
<xs:element name="AdrTp" type="AddressType2Code" minOccurs="0" maxOccurs="1"/>
<xs:element name="AdrLine" type="Max70Text" minOccurs="0" maxOccurs="5"/>
<xs:element name="StrtNm" type="Max70Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="BldgNb" type="Max16Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="PstCd" type="Max16Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="TwnNm" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="CtrySubDvsn" type="Max35Text" minOccurs="0" maxOccurs="1"/>
<xs:element name="Ctry" type="CountryCode"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="Priority2Code">
<xs:restriction base="xs:string">
<xs:enumeration value="HIGH"/>
<xs:enumeration value="NORM"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="ReferredDocumentAmount1Choice">
<xs:sequence>
<xs:choice>
<xs:element name="DuePyblAmt" type="CurrencyAndAmount"/>
<xs:element name="DscntApldAmt" type="CurrencyAndAmount"/>
<xs:element name="RmtdAmt" type="CurrencyAndAmount"/>
<xs:element name="CdtNoteAmt" type="CurrencyAndAmount"/>
<xs:element name="TaxAmt" type="CurrencyAndAmount"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ReferredDocumentInformation1">
<xs:sequence>
<xs:element name="RfrdDocTp" type="ReferredDocumentType1" minOccurs="0" maxOccurs="1"/>
<xs:element name="RfrdDocNb" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="ReferredDocumentType1">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="DocumentType2Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
<xs:element name="Issr" type="Max35Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="RemittanceInformation1">
<xs:sequence>
<xs:element name="Ustrd" type="Max140Text" minOccurs="0" maxOccurs="unbounded"/>
<xs:element name="Strd" type="StructuredRemittanceInformation6" minOccurs="0" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="SequenceType1Code">
<xs:restriction base="xs:string">
<xs:enumeration value="FRST"/>
<xs:enumeration value="RCUR"/>
<xs:enumeration value="FNAL"/>
<xs:enumeration value="OOFF"/>
</xs:restriction>
</xs:simpleType>
<xs:simpleType name="ServiceLevel1Code">
<xs:restriction base="xs:string">
<xs:enumeration value="SEPA"/>
<xs:enumeration value="SDVA"/>
<xs:enumeration value="PRPT"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="ServiceLevel2Choice">
<xs:sequence>
<xs:choice>
<xs:element name="Cd" type="ServiceLevel1Code"/>
<xs:element name="Prtry" type="Max35Text"/>
</xs:choice>
</xs:sequence>
</xs:complexType>
<xs:complexType name="SettlementInformation3">
<xs:sequence>
<xs:element name="SttlmMtd" type="SettlementMethod1Code"/>
<xs:element name="SttlmAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="ClrSys" type="ClearingSystemIdentification1Choice" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstgRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstgRmbrsmntAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="InstdRmbrsmntAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
<xs:element name="ThrdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification3" minOccurs="0" maxOccurs="1"/>
<xs:element name="ThrdRmbrsmntAgtAcct" type="CashAccount7" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="SettlementMethod1Code">
<xs:restriction base="xs:string">
<xs:enumeration value="INDA"/>
<xs:enumeration value="INGA"/>
<xs:enumeration value="COVE"/>
<xs:enumeration value="CLRG"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="SimpleIdentificationInformation2">
<xs:sequence>
<xs:element name="Id" type="Max34Text"/>
</xs:sequence>
</xs:complexType>
<xs:complexType name="StructuredRemittanceInformation6">
<xs:sequence>
<xs:element name="RfrdDocInf" type="ReferredDocumentInformation1" minOccurs="0" maxOccurs="1"/>
<xs:element name="RfrdDocRltdDt" type="ISODate" minOccurs="0" maxOccurs="1"/>
<xs:element name="RfrdDocAmt" type="ReferredDocumentAmount1Choice" minOccurs="0" maxOccurs="unbounded"/>
<xs:element name="CdtrRefInf" type="CreditorReferenceInformation1" minOccurs="0" maxOccurs="1"/>
<xs:element name="Invcr" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="Invcee" type="PartyIdentification8" minOccurs="0" maxOccurs="1"/>
<xs:element name="AddtlRmtInf" type="Max140Text" minOccurs="0" maxOccurs="1"/>
</xs:sequence>
</xs:complexType>
<xs:simpleType name="TrueFalseIndicator">
<xs:restriction base="xs:boolean"/>
</xs:simpleType>
<xs:simpleType name="UPICIdentifier">
<xs:restriction base="xs:string">
<xs:pattern value="[0-9]{8,17}"/>
</xs:restriction>
</xs:simpleType>
<xs:complexType name="pacs.006.001.01">
<xs:sequence>
<xs:element name="GrpHdr" type="GroupHeader7"/>
<xs:element name="OrgnlGrpInf" type="OriginalGroupInformation4"/>
<xs:element name="TxInf" type="PaymentTransactionInformation3" minOccurs="0" maxOccurs="unbounded"/>
</xs:sequence>
</xs:complexType>
</xs:schema>
