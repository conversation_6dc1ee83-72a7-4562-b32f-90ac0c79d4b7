package com.progressoft.participant.swift.mx.psysrule;

import iso.std.iso._20022.tech.xsd.pacs_002_001.GroupHeader5;
import iso.std.iso._20022.tech.xsd.pacs_002_001.Pacs00200102;
import iso.std.iso._20022.tech.xsd.pacs_002_001.PaymentTransactionInformation1;
import iso.std.iso._20022.tech.xsd.pacs_003_001.DirectDebitTransactionInformation2;
import iso.std.iso._20022.tech.xsd.pacs_003_001.GroupHeader3;
import iso.std.iso._20022.tech.xsd.pacs_003_001.Pacs00300101;
import iso.std.iso._20022.tech.xsd.pacs_008_001.BranchAndFinancialInstitutionIdentification3;
import iso.std.iso._20022.tech.xsd.pacs_008_001.CreditTransferTransactionInformation2;
import iso.std.iso._20022.tech.xsd.pacs_008_001.GroupHeader2;
import iso.std.iso._20022.tech.xsd.pacs_008_001.Pacs00800101;
import org.junit.Test;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

public class BatchInstructingAgentRuleTest {

    @Test
    public void givenPacs8V1WithNull_whenCheckBatchInstructingAgentRule_thenShouldReturnTrue() {
        boolean valid = BatchInstructingAgentRule.isValid(pacs8V1ValidTx1());
        assertTrue(valid);

        valid = BatchInstructingAgentRule.isValid(pacs8V1ValidTx2());
        assertTrue(valid);

        valid = BatchInstructingAgentRule.isValid(pacs8V1InValidTx2());
        assertFalse(valid);
    }

    private Pacs00800101 pacs8V1ValidTx1() {
        Pacs00800101 msg = new Pacs00800101();
        GroupHeader2 groupHeader2 = new GroupHeader2();
        groupHeader2.setInstgAgt(new BranchAndFinancialInstitutionIdentification3());
        msg.setGrpHdr(groupHeader2);
        return msg;
    }

    private Pacs00800101 pacs8V1ValidTx2() {
        Pacs00800101 msg = new Pacs00800101();
        GroupHeader2 groupHeader2 = new GroupHeader2();
        groupHeader2.setInstgAgt(null);
        msg.setGrpHdr(groupHeader2);
        msg.getCdtTrfTxInf().add(buildTx(new BranchAndFinancialInstitutionIdentification3()));
        return msg;
    }

    private Pacs00800101 pacs8V1InValidTx2() {
        Pacs00800101 msg = new Pacs00800101();
        GroupHeader2 groupHeader2 = new GroupHeader2();
        groupHeader2.setInstgAgt(null);
        msg.setGrpHdr(groupHeader2);
        msg.getCdtTrfTxInf().add(buildTx(null));
        return msg;
    }

    private CreditTransferTransactionInformation2 buildTx(BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentification3) {
        CreditTransferTransactionInformation2 tx = new CreditTransferTransactionInformation2();
        tx.setInstgAgt(branchAndFinancialInstitutionIdentification3);
        return tx;
    }


    @Test
    public void givenPacs3V1WithNull_whenCheckBatchInstructingAgentRule_thenShouldReturnTrue() {
        boolean valid = BatchInstructingAgentRule.isValid(pacs3V1ValidTx1());
        assertTrue(valid);

        valid = BatchInstructingAgentRule.isValid(pacs3V1ValidTx2());
        assertTrue(valid);

        valid = BatchInstructingAgentRule.isValid(pacs3V1InValidTx2());
        assertFalse(valid);
    }

    private Pacs00300101 pacs3V1ValidTx1() {
        Pacs00300101 msg = new Pacs00300101();
        GroupHeader3 groupHeader3 = new GroupHeader3();
        groupHeader3.setInstgAgt(new iso.std.iso._20022.tech.xsd.pacs_003_001.BranchAndFinancialInstitutionIdentification3());
        msg.setGrpHdr(groupHeader3);
        return msg;
    }

    private Pacs00300101 pacs3V1ValidTx2() {
        Pacs00300101 msg = new Pacs00300101();
        GroupHeader3 groupHeader3 = new GroupHeader3();
        groupHeader3.setInstgAgt(null);
        msg.setGrpHdr(groupHeader3);
        msg.getDrctDbtTxInf().add(buildTxPacs3(new iso.std.iso._20022.tech.xsd.pacs_003_001.BranchAndFinancialInstitutionIdentification3()));
        return msg;
    }

    private Pacs00300101 pacs3V1InValidTx2() {
        Pacs00300101 msg = new Pacs00300101();
        GroupHeader3 groupHeader3 = new GroupHeader3();
        groupHeader3.setInstgAgt(null);
        msg.setGrpHdr(groupHeader3);
        msg.getDrctDbtTxInf().add(buildTxPacs3(null));
        return msg;
    }

    private DirectDebitTransactionInformation2 buildTxPacs3(iso.std.iso._20022.tech.xsd.pacs_003_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentification3) {
        DirectDebitTransactionInformation2 tx = new DirectDebitTransactionInformation2();
        tx.setInstgAgt(branchAndFinancialInstitutionIdentification3);
        return tx;
    }

    @Test
    public void givenPacs2V1HasInstructingAgentInGroupHeader_whenIsValid_thenShouldReturnTrue() {
        assertTrue(BatchInstructingAgentRule.isValid(pacs2v1()));
    }

    @Test
    public void givenPacs2V1HasNoInstructingAgentInGroupHeader_whenIsValid_thenShouldReturnFalse() {
        assertFalse(BatchInstructingAgentRule.isValid(invalidPacs2v1()));
    }

    private Pacs00200102 invalidPacs2v1() {
        Pacs00200102 msg = new Pacs00200102();
        GroupHeader5 groupHeader5 = new GroupHeader5();
        iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentification3 = new iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3();
        branchAndFinancialInstitutionIdentification3.setFinInstnId(new iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice());
        groupHeader5.setInstgAgt(null);
        msg.setGrpHdr(groupHeader5);
        return msg;
    }


    private static Pacs00200102 pacs2v1() {
        Pacs00200102 msg = new Pacs00200102();
        msg.setGrpHdr(header());
        msg.getTxInfAndSts().add(paymentTxInfo());
        return msg;
    }

    private static PaymentTransactionInformation1 paymentTxInfo() {
        PaymentTransactionInformation1 info = new PaymentTransactionInformation1();
        iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentificationForInstdAgnt = new iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3();
        iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice financialInstitutionIdentificationForHeaderInstdAgt = new iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice();
        financialInstitutionIdentificationForHeaderInstdAgt.setBIC("BICInstdAgnt");
        branchAndFinancialInstitutionIdentificationForInstdAgnt.setFinInstnId(financialInstitutionIdentificationForHeaderInstdAgt);
        info.setInstdAgt(branchAndFinancialInstitutionIdentificationForInstdAgnt);

        iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentificationForInstgAgnt = new iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3();
        iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice financialInstitutionIdentificationForHeaderInstgAgt = new iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice();
        financialInstitutionIdentificationForHeaderInstgAgt.setBIC("BICInstgAgnt");
        branchAndFinancialInstitutionIdentificationForInstgAgnt.setFinInstnId(financialInstitutionIdentificationForHeaderInstgAgt);
        info.setInstgAgt(branchAndFinancialInstitutionIdentificationForInstgAgnt);
        return info;
    }

    private static GroupHeader5 header() {
        GroupHeader5 header = new GroupHeader5();

        iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentificationForInstdAgnt = new iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3();
        iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice financialInstitutionIdentificationForHeaderInstdAgt = new iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice();
        financialInstitutionIdentificationForHeaderInstdAgt.setBIC("BICHdrInstdAgnt");
        branchAndFinancialInstitutionIdentificationForInstdAgnt.setFinInstnId(financialInstitutionIdentificationForHeaderInstdAgt);
        header.setInstdAgt(branchAndFinancialInstitutionIdentificationForInstdAgnt);

        iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3 branchAndFinancialInstitutionIdentificationForInstgAgnt = new iso.std.iso._20022.tech.xsd.pacs_002_001.BranchAndFinancialInstitutionIdentification3();
        iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice financialInstitutionIdentificationForHeaderInstgAgt = new iso.std.iso._20022.tech.xsd.pacs_002_001.FinancialInstitutionIdentification5Choice();
        financialInstitutionIdentificationForHeaderInstgAgt.setBIC("BICHdrInstgAgnt");
        branchAndFinancialInstitutionIdentificationForInstgAgnt.setFinInstnId(financialInstitutionIdentificationForHeaderInstgAgt);
        header.setInstgAgt(branchAndFinancialInstitutionIdentificationForInstgAgnt);
        return header;
    }

}
