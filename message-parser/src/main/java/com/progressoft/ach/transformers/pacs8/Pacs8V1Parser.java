package com.progressoft.ach.transformers.pacs8;

import com.progressoft.ach.transformers.BatchResponse;
import com.progressoft.ach.transformers.MxParser;
import com.progressoft.ach.transformers.ParserRequest;
import com.progressoft.ach.transformers.dto.CustomerDto;
import com.progressoft.ach.transformers.dto.Priority;
import com.progressoft.ach.transformers.dto.TransactionDto;
import iso.std.iso._20022.tech.xsd.pacs_008_001.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.progressoft.ach.transformers.MessageVersions.VER_201001;
import static com.progressoft.ach.transformers.dto.Priority.valueOf;
import static java.util.Objects.nonNull;
import static java.util.Optional.of;
import static java.util.Optional.ofNullable;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.EMPTY;

public class Pacs8V1Parser extends MxParser<ParserRequest, BatchResponse> {

    protected String batchId(GroupHeader2 header) {
        return header.getMsgId();
    }

    protected String categoryPurpose(GroupHeader2 header) {
        if(header.getPmtTpInf() != null && header.getPmtTpInf().getCtgyPurp() != null)
            return header.getPmtTpInf().getCtgyPurp().value();
        return EMPTY;
    }

    protected Date creationDate(GroupHeader2 header) {
        return header.getCreDtTm().toGregorianCalendar().getTime();
    }

    protected String currency(GroupHeader2 header) {
        return header.getTtlIntrBkSttlmAmt().getCcy();
    }

    protected Date settlementDate(GroupHeader2 header) {
        return header.getIntrBkSttlmDt().toGregorianCalendar().getTime();
    }

    protected void fillInstructedAgentAndBranch(BatchResponse response, BranchAndFinancialInstitutionIdentification3 agent) {
        String participant = StringUtils.EMPTY;
        String branchCode = StringUtils.EMPTY;
        if (nonNull(agent) && nonNull(agent.getFinInstnId())) {
            participant = agent.getFinInstnId().getBIC() != null ? agent.getFinInstnId().getBIC() : agent.getFinInstnId().getClrSysMmbId().getId();
            if (nonNull(agent.getBrnchId()))
                branchCode = agent.getBrnchId().getId();
        }
        response.setInstructedParticipantAndBranch(participant, branchCode);
    }

    protected void fillInstructingAgentAndBranch(BatchResponse response,
                                                 BranchAndFinancialInstitutionIdentification3 agent) {
        String participant = StringUtils.EMPTY;
        String branchCode = StringUtils.EMPTY;
        if (nonNull(agent) && nonNull(agent.getFinInstnId())) {
            participant = agent.getFinInstnId().getBIC() != null ? agent.getFinInstnId().getBIC() : agent.getFinInstnId().getClrSysMmbId().getId();
            if (nonNull(agent.getBrnchId()))
                branchCode = agent.getBrnchId().getId();
        }
        response.setInstructingParticipantAndBranch(participant, branchCode);
    }

    @Override
    protected void fillResponse(Object message, BatchResponse response) {
        Document swiftBatch = (Document) message;
        GroupHeader2 header = swiftBatch.getPacs00800101().getGrpHdr();
        List<CreditTransferTransactionInformation2> txList = swiftBatch.getPacs00800101().getCdtTrfTxInf();
        response.setBatchId(batchId(header));
        response.setSettlementDate(settlementDate(header));
        response.setCreationDate(creationDate(header));
        response.setSuplemntaryData(Collections.singletonMap("batchSource", "1"));
        response.setCategoryPurpose(categoryPurpose(header), VER_201001.name());
        response.setCurrency(currency(header));
        response.setTotalAmount(header.getTtlIntrBkSttlmAmt().getValue());
        fillInstructedAgentAndBranch(response, header.getInstdAgt());
        fillInstructingAgentAndBranch(response, header.getInstgAgt());
        response.setPriority(priority(header.getPmtTpInf()));
        response.setTransactions(txList.stream().map(this::tx).collect(toList()));
        response.setTotalCount(txList.size());
        response.setClearingChannel(clearingChannel(header.getPmtTpInf()));
        response.setSettlementMethod(settlementMethod(header.getSttlmInf()));
        response.setClearingSystem(clearingSystem(header.getSttlmInf()));
    }

    public String settlementMethod(SettlementInformation1 sttlmInf) {
        return ofNullable(sttlmInf).map(SettlementInformation1::getSttlmMtd)
                .map(SettlementMethod1Code::name).orElse(null);
    }

    public String clearingSystem(SettlementInformation1 sttlmInf) {
        return ofNullable(sttlmInf).map(SettlementInformation1::getClrSys)
                .map(ClearingSystemIdentification1Choice::getPrtry).orElse(null);
    }

    public String clearingChannel(PaymentTypeInformation3 pmtTpInf) {
        return ofNullable(pmtTpInf).map(PaymentTypeInformation3::getClrChanl).map(ClearingChannel2Code::name).orElse(null);
    }

    private String branch(BranchAndFinancialInstitutionIdentification3 agent) {
        return agent != null && agent.getBrnchId() != null ? agent.getBrnchId().getId() : StringUtils.EMPTY;
    }

    private CustomerDto customer(PartyIdentification8 partyIdentification, CashAccount7 acct) {
        CustomerDto customer = new CustomerDto();
        customer.setName(partyIdentification != null ? partyIdentification.getNm() : EMPTY);
        if (acct != null) {
            AccountIdentification3Choice id = acct.getId();
            if (id != null) {
                if (id.getPrtryAcct() != null)
                    customer.setAccountId(id.getPrtryAcct().getId());
                customer.setIban(id.getIBAN());
            }
        }

        Party2Choice id = partyIdentification != null ? partyIdentification.getId() : null;
        if (nonNull(id) && isNotEmpty(id.getPrvtId())) {
            PersonIdentification3 identification = id.getPrvtId().get(0);
            if (identification.getOthrId() != null) {
                customer.setIdType(identification.getOthrId().getIdTp());
                customer.setIdValue(identification.getOthrId().getId());
            }
            if (id.getPrvtId().size() > 1) {
                identification = id.getPrvtId().get(1);
                if (identification.getOthrId() != null) {
                    customer.setIdType(identification.getOthrId().getIdTp());
                    customer.setIdValue(identification.getOthrId().getId());
                }
            }
        }

        if (partyIdentification != null) {
            List<String> addressLines = of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getAdrLine).orElse(new ArrayList<>());
            if (!addressLines.isEmpty())
                customer.setAddressLine(String.join("\n", addressLines));
            customer.setStreetName(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getStrtNm).orElse(null));
            customer.setBuildingNumber(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getBldgNb).orElse(null));
            customer.setPostCode(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getPstCd).orElse(null));
            customer.setTownName(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getTwnNm).orElse(null));
            customer.setCountrySubDivision(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getCtrySubDvsn).orElse(null));
            customer.setCountry(of(partyIdentification).map(PartyIdentification8::getPstlAdr).map(PostalAddress1::getCtry).orElse(null));
        }

        return customer;
    }

    private String participant(BranchAndFinancialInstitutionIdentification3 agent) {
        return agent != null && agent.getFinInstnId() != null ?
                agent.getFinInstnId().getBIC() != null ? agent.getFinInstnId().getBIC() :
                        agent.getFinInstnId().getClrSysMmbId().getId() : StringUtils.EMPTY;
    }

    private Priority priority(PaymentTypeInformation3 pr) {
        if (pr == null)
            return valueOf(Priority2Code.NORM.name());
        Priority2Code priority = pr.getInstrPrty() != null ? pr.getInstrPrty() : null;
        if (priority == null)
            priority = Priority2Code.NORM;
        return valueOf(priority.name());
    }

    private List<String> remittance(RemittanceInformation1 rmtInf) {
        return rmtInf != null && isNotEmpty(rmtInf.getUstrd()) ? rmtInf.getUstrd() : new ArrayList<>();
    }

    private String transactionPurpose(CreditTransferTransactionInformation2 tx) {
        if (tx.getPurp() != null)
            return Arrays.stream(tx.getPurp().getPrtry().split("/")).findFirst().orElse(EMPTY);
        return EMPTY;
    }

    private TransactionDto tx(CreditTransferTransactionInformation2 tx) {
        PaymentIdentification2 pmtId = tx.getPmtId();
        TransactionDto dto = new TransactionDto();
        dto.setTransactionId(pmtId.getTxId());
        dto.setEndToEndId(pmtId.getEndToEndId());
        dto.setInstructionId(pmtId.getInstrId());
        dto.setAmount(tx.getIntrBkSttlmAmt().getValue());
        dto.setCurrency(tx.getIntrBkSttlmAmt().getCcy());
        dto.setDebtorParticipant(participant(tx.getDbtrAgt()));
        dto.setDebtorBranch(branch(tx.getDbtrAgt()));
        dto.setDebtorCustomer(customer(tx.getDbtr(), tx.getDbtrAcct()));
        dto.setCreditorBranch(branch(tx.getCdtrAgt()));
        dto.setCreditorParticipant(participant(tx.getCdtrAgt()));
        dto.setCreditorCustomer(customer(tx.getCdtr(), tx.getCdtrAcct()));
        dto.setRemittance(remittance(tx.getRmtInf()));
        dto.setTransactionPurpose(transactionPurpose(tx));
        dto.setPriority(priority(tx.getPmtTpInf()));
        dto.setInstructedParticipant(participant(tx.getInstdAgt()));
        dto.setInstructingParticipant(participant(tx.getInstgAgt()));
        dto.setClearingChannel(clearingChannel(tx.getPmtTpInf()));
        return dto;
    }

}
