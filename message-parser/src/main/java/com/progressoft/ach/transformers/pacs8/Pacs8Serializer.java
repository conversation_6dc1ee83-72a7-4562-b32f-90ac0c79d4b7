package com.progressoft.ach.transformers.pacs8;

import com.progressoft.ach.transformers.BatchRequest;
import com.progressoft.ach.transformers.MxSerializer;
import com.progressoft.ach.transformers.SerializerResponse;
import com.progressoft.ach.transformers.dto.CustomerDto;
import com.progressoft.ach.transformers.dto.TransactionDto;
import com.progressoft.participant.swift.ach.SupplementaryData;
import com.progressoft.participant.swift.mx.pacs008.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.progressoft.ach.transformers.MessageVersions.VER_202204;
import static java.lang.System.lineSeparator;
import static java.util.Arrays.asList;
import static java.util.stream.Collectors.toList;
import static org.apache.commons.collections.CollectionUtils.isEmpty;
import static org.apache.commons.collections.CollectionUtils.isNotEmpty;
import static org.apache.commons.lang3.StringUtils.*;

public class Pacs8Serializer extends MxSerializer<BatchRequest, SerializerResponse> {

    @Override
    protected Object buildMessageDocument(BatchRequest request) {
        Document document = new Document();
        FIToFICustomerCreditTransferV05 batch = new FIToFICustomerCreditTransferV05();
        document.setFIToFICstmrCdtTrf(batch);
        batch.setGrpHdr(header(request));
        batch.getCdtTrfTxInf().addAll(request.getTransactions().stream().map(t -> tx(t, request)).collect(toList()));
        return document;
    }

    private CashAccount24 account(CustomerDto customer) {
        CashAccount24 result = new CashAccount24();

        AccountIdentification4Choice accountId = new AccountIdentification4Choice();
        result.setId(accountId);

        if (!StringUtils.isBlank(customer.getAccountId())) {
            GenericAccountIdentification1 genericAccountId = new GenericAccountIdentification1();
            accountId.setOthr(genericAccountId);
            genericAccountId.setId(customer.getAccountId());
        }

        if (!StringUtils.isBlank(customer.getIban()))
            accountId.setIBAN(customer.getIban());

        return result;
    }

    private BranchAndFinancialInstitutionIdentification5 agent(String agent, String branch) {
        if (StringUtils.isEmpty(agent))
            return null;

        BranchAndFinancialInstitutionIdentification5 fid = new BranchAndFinancialInstitutionIdentification5();
        FinancialInstitutionIdentification8 finChoice = new FinancialInstitutionIdentification8();

        finChoice.setBICFI(agent);
        fid.setFinInstnId(finChoice);

        return fid;
    }

    private ActiveCurrencyAndAmount amountAndCurrency(BigDecimal totalAmount, String currency) {
        ActiveCurrencyAndAmount currencyAndAmount = new ActiveCurrencyAndAmount();
        currencyAndAmount.setValue(totalAmount);
        currencyAndAmount.setCcy(currency);
        return currencyAndAmount;
    }

    private CategoryPurpose1Choice buildCategoryPurposeBlock(String categoryPurpose) {
        CategoryPurpose1Choice result = null;
        if (isNotBlank(categoryPurpose)) {
            result = new CategoryPurpose1Choice();
            result.setPrtry(categoryPurpose);
        }
        return result;
    }

    private PartyIdentification43 customer(CustomerDto customer) {
        PartyIdentification43 partyId = new PartyIdentification43();
        partyId.setNm(customer.getName());
        if (isNotBlank(customer.getIdValue()) && isNotBlank(customer.getIdType()))
            partyId.setId(partyChoice(customer));
        if (isNotBlank(customer.getAddressLine())) {
            PostalAddress6 postalAddress6 = new PostalAddress6();
            postalAddress6.getAdrLine().addAll(asList(defaultIfBlank(customer.getAddressLine(), EMPTY).split(lineSeparator())));
            partyId.setPstlAdr(postalAddress6);
        }
        return partyId;
    }

    private GroupHeader49 header(BatchRequest request) {
        GroupHeader49 groupHeader = new GroupHeader49();
        groupHeader.setMsgId(request.getBatchId());
        groupHeader.setTtlIntrBkSttlmAmt(amountAndCurrency(request.getAmount(), request.getCurrency()));
        groupHeader.setIntrBkSttlmDt(toXmlDate(request.getSettlementDate()));
        groupHeader.setSttlmInf(settlementInstruction(request.getSettlementMethod(), request.getClearingSystemId()));
        groupHeader.setNbOfTxs(request.getNumberOfTransactions());
        groupHeader.setCreDtTm(toXmlCreDtTime(request.getCreationDate()));
        groupHeader.setInstgAgt(agent(request.getInstructingParticipant(), request.getInstructingBranch()));
        groupHeader.setInstdAgt(agent(request.getInstructedParticipant(), request.getInstructedBranch()));
        groupHeader.setPmtTpInf(paymentTypeInfo(request.getCategoryPurpose(VER_202204.name())));
        return groupHeader;
    }

    private Party11Choice partyChoice(CustomerDto customer) {
        Party11Choice partyChoice = new Party11Choice();

        PersonIdentificationSchemeName1Choice schemeNameChoice = new PersonIdentificationSchemeName1Choice();
        schemeNameChoice.setPrtry(customer.getIdType());

        PersonIdentification5 personId = new PersonIdentification5();
        partyChoice.setPrvtId(personId);

        GenericPersonIdentification1 genericPersonId = new GenericPersonIdentification1();
        personId.getOthr().add(genericPersonId);

        genericPersonId.setId(customer.getIdValue());
        genericPersonId.setSchmeNm(schemeNameChoice);

        return partyChoice;
    }

    private PaymentIdentification3 paymentId(TransactionDto tx) {
        PaymentIdentification3 result = new PaymentIdentification3();
        result.setTxId(tx.getTransactionId());
        if (isNoneBlank(tx.getInstructionId()))
            result.setInstrId(tx.getInstructionId());
        if (isNoneBlank(tx.getEndToEndId()))
            result.setEndToEndId(tx.getEndToEndId());
        return result;
    }

    private PaymentTypeInformation21 paymentTypeInfo(String categoryPurpose) {
        if (isBlank(categoryPurpose) && isBlank(categoryPurpose))
            return null;
        PaymentTypeInformation21 paymentTypeInfo = new PaymentTypeInformation21();
        paymentTypeInfo.setCtgyPurp(buildCategoryPurposeBlock(categoryPurpose));
        return paymentTypeInfo;
    }

    private RemittanceInformation10 remittenceInfo(List<String> remittence) {
        if (isNotEmpty(remittence))
            remittence = remittence.stream().filter(StringUtils::isNotBlank).collect(toList());
        if (isEmpty(remittence))
            return null;
        RemittanceInformation10 result = new RemittanceInformation10();
        result.getUstrd().addAll(remittence);
        return result;
    }

    private SettlementInstruction1 settlementInstruction(String settlementMethod, String clearingSysId) {
        SettlementInstruction1 settleInfo = new SettlementInstruction1();
        settleInfo.setSttlmMtd(SettlementMethod1Code.valueOf(settlementMethod));
        ClearingSystemIdentification3Choice clearingSys = new ClearingSystemIdentification3Choice();
        settleInfo.setClrSys(clearingSys);
        clearingSys.setPrtry(clearingSysId);
        return settleInfo;
    }

    private SupplementaryData1 supplementaryData(Map<String, String> supplemntaryDataMap) {
        if (MapUtils.isEmpty(supplemntaryDataMap))
            return null;
        SupplementaryData1 result = new SupplementaryData1();
        SupplementaryDataEnvelope1 splmtry = new SupplementaryDataEnvelope1();
        SupplementaryData achData = new SupplementaryData();
        achData.getItems().addAll(suppmentaryData(supplemntaryDataMap));
        splmtry.setAny(achData);
        result.setEnvlp(splmtry);
        return result;
    }

    private Purpose2Choice transactionPurpose(TransactionDto tx) {
        Purpose2Choice purpose = null;
        if (StringUtils.isNotEmpty(tx.getTransactionPurpose())) {
            purpose = new Purpose2Choice();
            purpose.setPrtry(tx.getTransactionPurpose());
        }
        return purpose;
    }

    private CreditTransferTransaction19 tx(TransactionDto tx, BatchRequest batch) {
        CreditTransferTransaction19 result = new CreditTransferTransaction19();

        result.setPmtId(paymentId(tx));
        result.setPurp(transactionPurpose(tx));
        result.setRmtInf(remittenceInfo(tx.getRemittance()));

        result.setDbtr(customer(tx.getDebtorCustomer()));
        result.setDbtrAcct(account(tx.getDebtorCustomer()));
        result.setDbtrAgt(agent(tx.getDebtorParticipant(), tx.getDebtorBranch()));

        result.setCdtr(customer(tx.getCreditorCustomer()));
        result.setCdtrAcct(account(tx.getCreditorCustomer()));
        result.setCdtrAgt(agent(tx.getCreditorParticipant(), tx.getCreditorBranch()));

        result.setIntrBkSttlmAmt(amountAndCurrency(tx.getAmount(), tx.getCurrency()));
        result.setChrgBr(ChargeBearerType1Code.SLEV);
        result.setSttlmPrty(Priority3Code.valueOf(batch.getPriority().name()));

        return result;
    }

}
