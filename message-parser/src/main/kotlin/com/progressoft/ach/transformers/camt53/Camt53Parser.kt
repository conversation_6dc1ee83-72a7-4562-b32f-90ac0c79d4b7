package com.progressoft.ach.transformers.camt53

import com.progressoft.ach.transformers.util.ContextSupplier
import com.progressoft.ach.transformers.util.DateUtil.toDateString
import com.progressoft.ach.transformers.util.DateUtil.toLocalDateTime
import com.progressoft.ach.transformers.util.JaxbParser
import iso.std.iso._20022.tech.xsd.camt_053_001.CashBalance7
import iso.std.iso._20022.tech.xsd.camt_053_001.Document
import iso.std.iso._20022.tech.xsd.camt_053_001.EntryDetails7
import iso.std.iso._20022.tech.xsd.camt_053_001.ReportEntry8
import org.springframework.stereotype.Component

@Component
class Camt53Parser {

    private val contextSupplier = ContextSupplier(Document::class.java.getPackage().name)
    private val jaxbParser = JaxbParser(contextSupplier)

    fun parse(message: String): Camt53Dto{
        val document = jaxbParser.parse(message) as Document
        return buildDto(document)
    }

    private fun buildDto(document: Document): Camt53Dto {
        val sts = document.bkToCstmrStmt.stmt[0]
        return Camt53Dto(
            document.bkToCstmrStmt.grpHdr.msgId,
            toLocalDateTime(document.bkToCstmrStmt.grpHdr.creDtTm),
            sts.id,
            sts.elctrncSeqNb.toString(),
            toLocalDateTime(sts.creDtTm),
            sts.acct.id.othr.id,
            sts.acct.ccy,
            getBalanceInfo(sts.bal),
            getTransactionInfo(sts.ntry)
        )
    }

    private fun getBalanceInfo(balance: List<CashBalance7>): List<BalanceDto> {
        return balance.map { bal ->
            BalanceDto(
                bal.tp.cdOrPrtry.prtry,
                bal.amt.value,
                bal.cdtDbtInd.value(),
                toDateString(bal.dt.dt)
            )
        }
    }

    private fun getTransactionInfo(ntryList: List<ReportEntry8>): List<TransactionDto>{
        return ntryList.map { ntry ->
            val details = getTransactionDetailsInfo(ntry.ntryDtls)
            TransactionDto(
                ntry.ntryRef,
                ntry.amt?.value,
                ntry.cdtDbtInd?.value(),
                ntry.sts?.value(),
                ntry.bkTxCd?.prtry?.cd,
                ntry.addtlInfInd?.msgNmId,
                ntry.addtlInfInd?.msgId,
                details
            )
        }
    }

    private fun getTransactionDetailsInfo(ntryDtlsList: List<EntryDetails7>): List<TransactionDetailsDto>{
        if (ntryDtlsList.isEmpty()) return emptyList()
        return ntryDtlsList.flatMap { dtl ->
            dtl.txDtls.map { tx ->
                TransactionDetailsDto(
                    tx.amt?.value,
                    tx.cdtDbtInd?.value(),
                    tx.rltdPties?.dbtr?.nm,
                    tx.rltdPties?.dbtrAcct?.id?.iban,
                    tx.rltdPties?.cdtr?.nm,
                    tx.rltdPties?.cdtrAcct?.id?.iban
                )
            }
        }
    }
}