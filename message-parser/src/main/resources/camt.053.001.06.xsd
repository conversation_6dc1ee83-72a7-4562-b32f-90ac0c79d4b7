<?xml version="1.0" encoding="UTF-8"?>
<!--Generated by Standards Editor (build:R1.6.5.6) on 2016 Feb 12 18:17:14, ISO 20022 version : 2013-->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:camt.053.001.06" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:camt.053.001.06">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice">
        <xs:choice>
            <xs:element name="IBAN" type="IBAN2007Identifier"/>
            <xs:element name="Othr" type="GenericAccountIdentification1"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AccountInterest3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="InterestType1Choice"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rate" type="Rate3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DateTimePeriodDetails"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tax" type="TaxCharges2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AccountSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalAccountIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AccountStatement6">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StmtPgntn" type="Pagination"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncSeqNb" type="Number"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LglSeqNb" type="Number"/>
            <xs:element name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DateTimePeriodDetails"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CpyDplctInd" type="CopyDuplicate1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RptgSrc" type="ReportingSource1Choice"/>
            <xs:element name="Acct" type="CashAccount25"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdAcct" type="CashAccount24"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Intrst" type="AccountInterest3"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Bal" type="CashBalance7"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxsSummry" type="TotalTransactions5"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Ntry" type="ReportEntry8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlStmtInf" type="Max500Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ActiveCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ActiveOrHistoricCurrencyAnd13DecimalAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="13"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveOrHistoricCurrencyAnd13DecimalAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveOrHistoricCurrencyAnd13DecimalAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AddressType2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDR"/>
            <xs:enumeration value="PBOX"/>
            <xs:enumeration value="HOME"/>
            <xs:enumeration value="BIZZ"/>
            <xs:enumeration value="MLTO"/>
            <xs:enumeration value="DLVY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="AmountAndCurrencyExchange3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAmt" type="AmountAndCurrencyExchangeDetails3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxAmt" type="AmountAndCurrencyExchangeDetails3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CntrValAmt" type="AmountAndCurrencyExchangeDetails3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AnncdPstngAmt" type="AmountAndCurrencyExchangeDetails3"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="PrtryAmt" type="AmountAndCurrencyExchangeDetails4"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountAndCurrencyExchangeDetails3">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CcyXchg" type="CurrencyExchange5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountAndCurrencyExchangeDetails4">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CcyXchg" type="CurrencyExchange5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountAndDirection35">
        <xs:sequence>
            <xs:element name="Amt" type="NonNegativeDecimalNumber"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountRangeBoundary1">
        <xs:sequence>
            <xs:element name="BdryAmt" type="ImpliedCurrencyAndAmount"/>
            <xs:element name="Incl" type="YesNoIndicator"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="AnyBICIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AttendanceContext1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ATTD"/>
            <xs:enumeration value="SATT"/>
            <xs:enumeration value="UATT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AuthenticationEntity1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ICCD"/>
            <xs:enumeration value="AGNT"/>
            <xs:enumeration value="MERC"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AuthenticationMethod1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="UKNW"/>
            <xs:enumeration value="BYPS"/>
            <xs:enumeration value="NPIN"/>
            <xs:enumeration value="FPIN"/>
            <xs:enumeration value="CPSG"/>
            <xs:enumeration value="PPSG"/>
            <xs:enumeration value="MANU"/>
            <xs:enumeration value="MERC"/>
            <xs:enumeration value="SCRT"/>
            <xs:enumeration value="SNCT"/>
            <xs:enumeration value="SCNL"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BalanceSubType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalBalanceSubType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="BalanceType12">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="BalanceType5Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubTp" type="BalanceSubType1Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="BalanceType12Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="XPCD"/>
            <xs:enumeration value="OPAV"/>
            <xs:enumeration value="ITAV"/>
            <xs:enumeration value="CLAV"/>
            <xs:enumeration value="FWAV"/>
            <xs:enumeration value="CLBD"/>
            <xs:enumeration value="ITBD"/>
            <xs:enumeration value="OPBD"/>
            <xs:enumeration value="PRCD"/>
            <xs:enumeration value="INFO"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BalanceType5Choice">
        <xs:choice>
            <xs:element name="Cd" type="BalanceType12Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="BankToCustomerStatementV06">
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader58"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Stmt" type="AccountStatement6"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BankTransactionCodeStructure4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Domn" type="BankTransactionCodeStructure5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Prtry" type="ProprietaryBankTransactionCodeStructure1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BankTransactionCodeStructure5">
        <xs:sequence>
            <xs:element name="Cd" type="ExternalBankTransactionDomain1Code"/>
            <xs:element name="Fmly" type="BankTransactionCodeStructure6"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BankTransactionCodeStructure6">
        <xs:sequence>
            <xs:element name="Cd" type="ExternalBankTransactionFamily1Code"/>
            <xs:element name="SubFmlyCd" type="ExternalBankTransactionSubFamily1Code"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="BaseOneRate">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="10"/>
            <xs:totalDigits value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BatchInformation2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtInfId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="NbOfTxs" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification5">
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BrnchId" type="BranchData2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchData2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CSCManagement1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PRST"/>
            <xs:enumeration value="BYPS"/>
            <xs:enumeration value="UNRD"/>
            <xs:enumeration value="NCSC"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CardAggregated1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlSvc" type="CardPaymentServiceType2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxCtgy" type="ExternalCardTransactionCategory1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SaleRcncltnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNbRg" type="CardSequenceNumberRange1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxDtRg" type="DateOrDateTimePeriodChoice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CardDataReading1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="TAGC"/>
            <xs:enumeration value="PHYS"/>
            <xs:enumeration value="BRCD"/>
            <xs:enumeration value="MGST"/>
            <xs:enumeration value="CICC"/>
            <xs:enumeration value="DFLE"/>
            <xs:enumeration value="CTLS"/>
            <xs:enumeration value="ECTL"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CardEntry2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Card" type="PaymentCard4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="POI" type="PointOfInteraction1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AggtdNtry" type="CardAggregated1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrePdAcct" type="CashAccount24"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CardIndividualTransaction2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ICCRltdData" type="Max1025Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtCntxt" type="PaymentContext3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlSvc" type="CardPaymentServiceType2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxCtgy" type="ExternalCardTransactionCategory1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SaleRcncltnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SaleRefNb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RePresntmntRsn" type="ExternalRePresentmentReason1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxId" type="TransactionIdentifier1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Pdct" type="Product2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="VldtnDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="VldtnSeqNb" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CardPaymentServiceType2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="AGGR"/>
            <xs:enumeration value="DCCV"/>
            <xs:enumeration value="GRTT"/>
            <xs:enumeration value="INSP"/>
            <xs:enumeration value="LOYT"/>
            <xs:enumeration value="NRES"/>
            <xs:enumeration value="PUCO"/>
            <xs:enumeration value="RECP"/>
            <xs:enumeration value="SOAF"/>
            <xs:enumeration value="UNAF"/>
            <xs:enumeration value="VCAU"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CardSecurityInformation1">
        <xs:sequence>
            <xs:element name="CSCMgmt" type="CSCManagement1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CSCVal" type="Min3Max4NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CardSequenceNumberRange1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="FrstTx" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LastTx" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CardTransaction2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Card" type="PaymentCard4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="POI" type="PointOfInteraction1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tx" type="CardTransaction2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrePdAcct" type="CashAccount24"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CardTransaction2Choice">
        <xs:choice>
            <xs:element name="Aggtd" type="CardAggregated1"/>
            <xs:element name="Indv" type="CardIndividualTransaction2"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CardholderAuthentication2">
        <xs:sequence>
            <xs:element name="AuthntcnMtd" type="AuthenticationMethod1Code"/>
            <xs:element name="AuthntcnNtty" type="AuthenticationEntity1Code"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CardholderVerificationCapability1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MNSG"/>
            <xs:enumeration value="NPIN"/>
            <xs:enumeration value="FCPN"/>
            <xs:enumeration value="FEPN"/>
            <xs:enumeration value="FDSG"/>
            <xs:enumeration value="FBIO"/>
            <xs:enumeration value="MNVR"/>
            <xs:enumeration value="FBIG"/>
            <xs:enumeration value="APKI"/>
            <xs:enumeration value="PKIS"/>
            <xs:enumeration value="CHDT"/>
            <xs:enumeration value="SCEC"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CashAccount24">
        <xs:sequence>
            <xs:element name="Id" type="AccountIdentification4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccount25">
        <xs:sequence>
            <xs:element name="Id" type="AccountIdentification4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ownr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Svcr" type="BranchAndFinancialInstitutionIdentification5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashAccountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CashAvailability1">
        <xs:sequence>
            <xs:element name="Dt" type="CashAvailabilityDate1Choice"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAvailabilityDate1Choice">
        <xs:choice>
            <xs:element name="NbOfDays" type="Max15PlusSignedNumericText"/>
            <xs:element name="ActlDt" type="ISODate"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CashBalance7">
        <xs:sequence>
            <xs:element name="Tp" type="BalanceType12"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtLine" type="CreditLine2"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element name="Dt" type="DateAndDateTimeChoice"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Avlbty" type="CashAvailability1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashDeposit1">
        <xs:sequence>
            <xs:element name="NoteDnmtn" type="ActiveCurrencyAndAmount"/>
            <xs:element name="NbOfNotes" type="Max15NumericText"/>
            <xs:element name="Amt" type="ActiveCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ChargeBearerType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEBT"/>
            <xs:enumeration value="CRED"/>
            <xs:enumeration value="SHAR"/>
            <xs:enumeration value="SLEV"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ChargeIncludedIndicator">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:complexType name="ChargeType3Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalChargeType1Code"/>
            <xs:element name="Prtry" type="GenericIdentification3"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Charges4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlChrgsAndTaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="ChargesRecord2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ChargesRecord2">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ChrgInclInd" type="ChargeIncludedIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ChargeType3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="PercentageRate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Br" type="ChargeBearerType1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Agt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tax" type="TaxCharges2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ClearingSystemIdentification2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysId" type="ClearingSystemIdentification2Choice"/>
            <xs:element name="MmbId" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ContactDetails2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NmPrfx" type="NamePrefix1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PhneNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MobNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FaxNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailAdr" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CopyDuplicate1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CODU"/>
            <xs:enumeration value="COPY"/>
            <xs:enumeration value="DUPL"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CorporateAction9">
        <xs:sequence>
            <xs:element name="EvtTp" type="Max35Text"/>
            <xs:element name="EvtId" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CreditDebitCode">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CRDT"/>
            <xs:enumeration value="DBIT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CreditLine2">
        <xs:sequence>
            <xs:element name="Incl" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceInformation2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CreditorReferenceType2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ref" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="DocumentType3Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType2">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CurrencyAndAmountRange2">
        <xs:sequence>
            <xs:element name="Amt" type="ImpliedCurrencyAmountRangeChoice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CurrencyExchange5">
        <xs:sequence>
            <xs:element name="SrcCcy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TrgtCcy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UnitCcy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element name="XchgRate" type="BaseOneRate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrctId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="QtnDt" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndDateTimeChoice">
        <xs:choice>
            <xs:element name="Dt" type="ISODate"/>
            <xs:element name="DtTm" type="ISODateTime"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DateAndPlaceOfBirth">
        <xs:sequence>
            <xs:element name="BirthDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvcOfBirth" type="Max35Text"/>
            <xs:element name="CityOfBirth" type="Max35Text"/>
            <xs:element name="CtryOfBirth" type="CountryCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateOrDateTimePeriodChoice">
        <xs:choice>
            <xs:element name="Dt" type="DatePeriodDetails"/>
            <xs:element name="DtTm" type="DateTimePeriodDetails"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DatePeriodDetails">
        <xs:sequence>
            <xs:element name="FrDt" type="ISODate"/>
            <xs:element name="ToDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateTimePeriodDetails">
        <xs:sequence>
            <xs:element name="FrDtTm" type="ISODateTime"/>
            <xs:element name="ToDtTm" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="DecimalNumber">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="DiscountAmountAndType1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DiscountAmountType1Choice"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DiscountAmountType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDiscountAmountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DisplayCapabilities1">
        <xs:sequence>
            <xs:element name="DispTp" type="UserInterface2Code"/>
            <xs:element name="NbOfLines" type="Max3NumericText"/>
            <xs:element name="LineWidth" type="Max3NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="BkToCstmrStmt" type="BankToCustomerStatementV06"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAdjustment1">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max4Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineIdentification1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DocumentLineType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineInformation1">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Id" type="DocumentLineIdentification1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Desc" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="RemittanceAmount3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="DocumentLineType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDocumentLineType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="DocumentType3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RADM"/>
            <xs:enumeration value="RPIN"/>
            <xs:enumeration value="FXDR"/>
            <xs:enumeration value="DISP"/>
            <xs:enumeration value="PUOR"/>
            <xs:enumeration value="SCOR"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocumentType6Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MSIN"/>
            <xs:enumeration value="CNFA"/>
            <xs:enumeration value="DNFA"/>
            <xs:enumeration value="CINV"/>
            <xs:enumeration value="CREN"/>
            <xs:enumeration value="DEBN"/>
            <xs:enumeration value="HIRI"/>
            <xs:enumeration value="SBIN"/>
            <xs:enumeration value="CMCN"/>
            <xs:enumeration value="SOAC"/>
            <xs:enumeration value="DISP"/>
            <xs:enumeration value="BOLD"/>
            <xs:enumeration value="VCHR"/>
            <xs:enumeration value="AROI"/>
            <xs:enumeration value="TSUT"/>
            <xs:enumeration value="PUOR"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="EntryDetails7">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Btch" type="BatchInformation2"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TxDtls" type="EntryTransaction8"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="EntryStatus2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="BOOK"/>
            <xs:enumeration value="PDNG"/>
            <xs:enumeration value="INFO"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="EntryTransaction8">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Refs" type="TransactionReferences3"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AmtDtls" type="AmountAndCurrencyExchange3"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Avlbty" type="CashAvailability1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BkTxCd" type="BankTransactionCodeStructure4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Chrgs" type="Charges4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Intrst" type="TransactionInterest3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdPties" type="TransactionParties3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdAgts" type="TransactionAgents3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice"/>
            <xs:element maxOccurs="10" minOccurs="0" name="RltdRmtInf" type="RemittanceLocation4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation11"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDts" type="TransactionDates2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdPric" type="TransactionPrice3Choice"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RltdQties" type="TransactionQuantities2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FinInstrmId" type="SecurityIdentification19"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tax" type="TaxInformation3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RtrInf" type="PaymentReturnReason2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CorpActn" type="CorporateAction9"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SfkpgAcct" type="SecuritiesAccount19"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="CshDpst" type="CashDeposit1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardTx" type="CardTransaction2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlTxInf" type="Max500Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Exact1NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Exact3NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Exact4AlphaNumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9]{4}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalAccountIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalBalanceSubType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalBankTransactionDomain1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalBankTransactionFamily1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalBankTransactionSubFamily1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCardTransactionCategory1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashAccountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalChargeType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDiscountAmountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDocumentLineType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstrumentIdentificationType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalGarnishmentType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPurpose1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalRePresentmentReason1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalReportingSource1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalReturnReason1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalTaxAmountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalTechnicalInputChannel1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification8">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstrumentQuantityChoice">
        <xs:choice>
            <xs:element name="Unit" type="DecimalNumber"/>
            <xs:element name="FaceAmt" type="ImpliedCurrencyAndAmount"/>
            <xs:element name="AmtsdVal" type="ImpliedCurrencyAndAmount"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="FromToAmountRange">
        <xs:sequence>
            <xs:element name="FrAmt" type="AmountRangeBoundary1"/>
            <xs:element name="ToAmt" type="AmountRangeBoundary1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Garnishment1">
        <xs:sequence>
            <xs:element name="Tp" type="GarnishmentType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Grnshee" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtAdmstr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FmlyMdclInsrncInd" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MplyeeTermntnInd" type="TrueFalseIndicator"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="GarnishmentType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalGarnishmentType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max34Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="AccountSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification3">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification30">
        <xs:sequence>
            <xs:element name="Id" type="Exact4AlphaNumericText"/>
            <xs:element name="Issr" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification32">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="PartyType3Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="PartyType4Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ShrtNm" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader58">
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text"/>
            <xs:element name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgRcpt" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgPgntn" type="Pagination"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlBizQry" type="OriginalBusinessQuery1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max500Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISINOct2015Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[A-Z0-9]{9,9}[0-9]{1,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISO2ALanguageCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="ISOYearMonth">
        <xs:restriction base="xs:gYearMonth"/>
    </xs:simpleType>
    <xs:complexType name="IdentificationSource3Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalFinancialInstrumentIdentificationType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ImpliedCurrencyAmountRangeChoice">
        <xs:choice>
            <xs:element name="FrAmt" type="AmountRangeBoundary1"/>
            <xs:element name="ToAmt" type="AmountRangeBoundary1"/>
            <xs:element name="FrToAmt" type="FromToAmountRange"/>
            <xs:element name="EQAmt" type="ImpliedCurrencyAndAmount"/>
            <xs:element name="NEQAmt" type="ImpliedCurrencyAndAmount"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ImpliedCurrencyAndAmount">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="InterestRecord1">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="InterestType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="Rate3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DateTimePeriodDetails"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tax" type="TaxCharges2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="InterestType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="InterestType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="InterestType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDY"/>
            <xs:enumeration value="OVRN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max1025Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="1025"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max105Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="105"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max140Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max15NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max15PlusSignedNumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[\+]{0,1}[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max16Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max34Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max350Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="350"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max3NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max4Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max500Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="500"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max5NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,5}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MessageIdentification2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgId" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Min2Max3NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{2,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Min3Max4NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{3,4}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Min8Max28NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{8,28}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="NameAndAddress10">
        <xs:sequence>
            <xs:element name="Nm" type="Max140Text"/>
            <xs:element name="Adr" type="PostalAddress6"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="NamePrefix1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DOCT"/>
            <xs:enumeration value="MIST"/>
            <xs:enumeration value="MISS"/>
            <xs:enumeration value="MADM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NonNegativeDecimalNumber">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Number">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="0"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="NumberAndSumOfTransactions1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NbOfNtries" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Sum" type="DecimalNumber"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="NumberAndSumOfTransactions4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NbOfNtries" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Sum" type="DecimalNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlNetNtry" type="AmountAndDirection35"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="OnLineCapability1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OFLN"/>
            <xs:enumeration value="ONLN"/>
            <xs:enumeration value="SMON"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="OrganisationIdentification8">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AnyBIC" type="AnyBICIdentifier"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericOrganisationIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="OriginalAndCurrentQuantities1">
        <xs:sequence>
            <xs:element name="FaceAmt" type="ImpliedCurrencyAndAmount"/>
            <xs:element name="AmtsdVal" type="ImpliedCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalBusinessQuery1">
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CreDtTm" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OtherIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Sfx" type="Max16Text"/>
            <xs:element name="Tp" type="IdentificationSource3Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="POIComponentType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="SOFT"/>
            <xs:enumeration value="EMVK"/>
            <xs:enumeration value="EMVO"/>
            <xs:enumeration value="MRIT"/>
            <xs:enumeration value="CHIT"/>
            <xs:enumeration value="SECM"/>
            <xs:enumeration value="PEDV"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Pagination">
        <xs:sequence>
            <xs:element name="PgNb" type="Max5NumericText"/>
            <xs:element name="LastPgInd" type="YesNoIndicator"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party11Choice">
        <xs:choice>
            <xs:element name="OrgId" type="OrganisationIdentification8"/>
            <xs:element name="PrvtId" type="PersonIdentification5"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="PartyIdentification43">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party11Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtryOfRes" type="CountryCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtctDtls" type="ContactDetails2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PartyType3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="OPOI"/>
            <xs:enumeration value="MERC"/>
            <xs:enumeration value="ACCP"/>
            <xs:enumeration value="ITAG"/>
            <xs:enumeration value="ACQR"/>
            <xs:enumeration value="CISS"/>
            <xs:enumeration value="DLIS"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="PartyType4Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MERC"/>
            <xs:enumeration value="ACCP"/>
            <xs:enumeration value="ITAG"/>
            <xs:enumeration value="ACQR"/>
            <xs:enumeration value="CISS"/>
            <xs:enumeration value="TAXH"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PaymentCard4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="PlainCardData" type="PlainCardData1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardCtryCd" type="Exact3NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardBrnd" type="GenericIdentification1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlCardData" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentContext3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="CardPres" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CrdhldrPres" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OnLineCntxt" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AttndncCntxt" type="AttendanceContext1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxEnvt" type="TransactionEnvironment1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxChanl" type="TransactionChannel1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AttndntMsgCpbl" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AttndntLang" type="ISO2ALanguageCode"/>
            <xs:element name="CardDataNtryMd" type="CardDataReading1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FllbckInd" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AuthntcnMtd" type="CardholderAuthentication2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentReturnReason2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlBkTxCd" type="BankTransactionCodeStructure4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Orgtr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="ReturnReason5Choice"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AddtlInf" type="Max105Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PercentageRate">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="10"/>
            <xs:totalDigits value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PersonIdentification5">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericPersonIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="PhoneNumber">
        <xs:restriction base="xs:string">
            <xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PlainCardData1">
        <xs:sequence>
            <xs:element name="PAN" type="Min8Max28NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardSeqNb" type="Min2Max3NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FctvDt" type="ISOYearMonth"/>
            <xs:element name="XpryDt" type="ISOYearMonth"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SvcCd" type="Exact3NumericText"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TrckData" type="TrackData1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardSctyCd" type="CardSecurityInformation1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PointOfInteraction1">
        <xs:sequence>
            <xs:element name="Id" type="GenericIdentification32"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SysNm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrpId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Cpblties" type="PointOfInteractionCapabilities1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Cmpnt" type="PointOfInteractionComponent1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PointOfInteractionCapabilities1">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="CardRdngCpblties" type="CardDataReading1Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="CrdhldrVrfctnCpblties" type="CardholderVerificationCapability1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OnLineCpblties" type="OnLineCapability1Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="DispCpblties" type="DisplayCapabilities1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrtLineWidth" type="Max3NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PointOfInteractionComponent1">
        <xs:sequence>
            <xs:element name="POICmpntTp" type="POIComponentType1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ManfctrId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Mdl" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="VrsnNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SrlNb" type="Max35Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ApprvlNb" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PostalAddress6">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AdrTp" type="AddressType2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="7" minOccurs="0" name="AdrLine" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Price2">
        <xs:sequence>
            <xs:element name="Tp" type="YieldedOrValueType1Choice"/>
            <xs:element name="Val" type="PriceRateOrAmountChoice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PriceRateOrAmountChoice">
        <xs:choice>
            <xs:element name="Rate" type="PercentageRate"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAnd13DecimalAmount"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="PriceValueType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DISC"/>
            <xs:enumeration value="PREM"/>
            <xs:enumeration value="PARV"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Product2">
        <xs:sequence>
            <xs:element name="PdctCd" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UnitOfMeasr" type="UnitOfMeasure1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PdctQty" type="DecimalNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UnitPric" type="ImpliedCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PdctAmt" type="ImpliedCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlPdctInf" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryAgent3">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryBankTransactionCodeStructure1">
        <xs:sequence>
            <xs:element name="Cd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryDate2">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Dt" type="DateAndDateTimeChoice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryParty3">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Pty" type="PartyIdentification43"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryPrice2">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Pric" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryQuantity1">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Qty" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProprietaryReference1">
        <xs:sequence>
            <xs:element name="Tp" type="Max35Text"/>
            <xs:element name="Ref" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Purpose2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalPurpose1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Rate3">
        <xs:sequence>
            <xs:element name="Tp" type="RateType4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="VldtyRg" type="CurrencyAndAmountRange2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RateType4Choice">
        <xs:choice>
            <xs:element name="Pctg" type="PercentageRate"/>
            <xs:element name="Othr" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentInformation7">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ReferredDocumentType4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="LineDtls" type="DocumentLineInformation1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType3Choice">
        <xs:choice>
            <xs:element name="Cd" type="DocumentType6Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType4">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="ReferredDocumentType3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceAmount2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="DscntApldAmt" type="DiscountAmountAndType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TaxAmt" type="TaxAmountAndType1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceAmount3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="DscntApldAmt" type="DiscountAmountAndType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TaxAmt" type="TaxAmountAndType1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation11">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Ustrd" type="Max140Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Strd" type="StructuredRemittanceInformation13"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceLocation4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtId" type="Max35Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RmtLctnDtls" type="RemittanceLocationDetails1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceLocationDetails1">
        <xs:sequence>
            <xs:element name="Mtd" type="RemittanceLocationMethod2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncAdr" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="NameAndAddress10"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="RemittanceLocationMethod2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FAXI"/>
            <xs:enumeration value="EDIC"/>
            <xs:enumeration value="URID"/>
            <xs:enumeration value="EMAL"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="SMSM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ReportEntry8">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NtryRef" type="Max35Text"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RvslInd" type="TrueFalseIndicator"/>
            <xs:element name="Sts" type="EntryStatus2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BookgDt" type="DateAndDateTimeChoice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ValDt" type="DateAndDateTimeChoice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctSvcrRef" type="Max35Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Avlbty" type="CashAvailability1"/>
            <xs:element name="BkTxCd" type="BankTransactionCodeStructure4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ComssnWvrInd" type="YesNoIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInfInd" type="MessageIdentification2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AmtDtls" type="AmountAndCurrencyExchange3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Chrgs" type="Charges4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TechInptChanl" type="TechnicalInputChannel1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Intrst" type="TransactionInterest3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CardTx" type="CardEntry2"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="NtryDtls" type="EntryDetails7"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlNtryInf" type="Max500Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReportingSource1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalReportingSource1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReturnReason5Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalReturnReason1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="SecuritiesAccount19">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="GenericIdentification30"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SecurityIdentification19">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ISIN" type="ISINOct2015Identifier"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="OthrId" type="OtherIdentification1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Desc" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StructuredRemittanceInformation13">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RfrdDocInf" type="ReferredDocumentInformation7"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RfrdDocAmt" type="RemittanceAmount2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrRefInf" type="CreditorReferenceInformation2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcee" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxRmt" type="TaxInformation4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtRmt" type="Garnishment1"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AddtlRmtInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryData1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="PlcAndNm" type="Max350Text"/>
            <xs:element name="Envlp" type="SupplementaryDataEnvelope1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryDataEnvelope1">
        <xs:sequence>
            <xs:any namespace="##any" processContents="lax"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmount1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="PercentageRate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Dtls" type="TaxRecordDetails1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmountAndType1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="TaxAmountType1Choice"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmountType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalTaxAmountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TaxAuthorisation1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Titl" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxCharges2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="PercentageRate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxInformation3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="TaxParty1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="TaxParty2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AdmstnZn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Mtd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNb" type="Number"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="TaxRecord1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxInformation4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="TaxParty1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="TaxParty2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="TaxParty2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AdmstnZone" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Mtd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNb" type="Number"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="TaxRecord1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Authstn" type="TaxAuthorisation1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxPeriod1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Yr" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="TaxRecordPeriod1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DatePeriodDetails"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecord1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctgy" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyDtls" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrSts" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CertId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrmsCd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxAmt" type="TaxAmount1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecordDetails1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod1"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TaxRecordPeriod1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MM01"/>
            <xs:enumeration value="MM02"/>
            <xs:enumeration value="MM03"/>
            <xs:enumeration value="MM04"/>
            <xs:enumeration value="MM05"/>
            <xs:enumeration value="MM06"/>
            <xs:enumeration value="MM07"/>
            <xs:enumeration value="MM08"/>
            <xs:enumeration value="MM09"/>
            <xs:enumeration value="MM10"/>
            <xs:enumeration value="MM11"/>
            <xs:enumeration value="MM12"/>
            <xs:enumeration value="QTR1"/>
            <xs:enumeration value="QTR2"/>
            <xs:enumeration value="QTR3"/>
            <xs:enumeration value="QTR4"/>
            <xs:enumeration value="HLF1"/>
            <xs:enumeration value="HLF2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TechnicalInputChannel1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalTechnicalInputChannel1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TotalTransactions5">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlNtries" type="NumberAndSumOfTransactions4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlCdtNtries" type="NumberAndSumOfTransactions1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlDbtNtries" type="NumberAndSumOfTransactions1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TtlNtriesPerBkTxCd" type="TotalsPerBankTransactionCode4"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TotalsPerBankTransactionCode4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NbOfNtries" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Sum" type="DecimalNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlNetNtry" type="AmountAndDirection35"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FcstInd" type="TrueFalseIndicator"/>
            <xs:element name="BkTxCd" type="BankTransactionCodeStructure4"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Avlbty" type="CashAvailability1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TrackData1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TrckNb" type="Exact1NumericText"/>
            <xs:element name="TrckVal" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TransactionAgents3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt1" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt2" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt3" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RcvgAgt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DlvrgAgt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IssgAgt" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmPlc" type="BranchAndFinancialInstitutionIdentification5"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Prtry" type="ProprietaryAgent3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TransactionChannel1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIL"/>
            <xs:enumeration value="TLPH"/>
            <xs:enumeration value="ECOM"/>
            <xs:enumeration value="TVPY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TransactionDates2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AccptncDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TradActvtyCtrctlSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TradDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StartDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EndDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Prtry" type="ProprietaryDate2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TransactionEnvironment1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MERC"/>
            <xs:enumeration value="PRIV"/>
            <xs:enumeration value="PUBL"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="TransactionIdentifier1">
        <xs:sequence>
            <xs:element name="TxDtTm" type="ISODateTime"/>
            <xs:element name="TxRef" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TransactionInterest3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlIntrstAndTaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="InterestRecord1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TransactionParties3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InitgPty" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount24"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount24"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtCdtr" type="PartyIdentification43"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TradgPty" type="PartyIdentification43"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Prtry" type="ProprietaryParty3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TransactionPrice3Choice">
        <xs:choice>
            <xs:element name="DealPric" type="Price2"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Prtry" type="ProprietaryPrice2"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TransactionQuantities2Choice">
        <xs:choice>
            <xs:element name="Qty" type="FinancialInstrumentQuantityChoice"/>
            <xs:element name="OrgnlAndCurFaceAmt" type="OriginalAndCurrentQuantities1"/>
            <xs:element name="Prtry" type="ProprietaryQuantity1"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="TransactionReferences3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MsgId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctSvcrRef" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtInfId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EndToEndId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ChqNb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctOwnrTxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AcctSvcrTxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MktInfrstrctrTxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrcgId" type="Max35Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Prtry" type="ProprietaryReference1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TrueFalseIndicator">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:simpleType name="UnitOfMeasure1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PIEC"/>
            <xs:enumeration value="TONS"/>
            <xs:enumeration value="FOOT"/>
            <xs:enumeration value="GBGA"/>
            <xs:enumeration value="USGA"/>
            <xs:enumeration value="GRAM"/>
            <xs:enumeration value="INCH"/>
            <xs:enumeration value="KILO"/>
            <xs:enumeration value="PUND"/>
            <xs:enumeration value="METR"/>
            <xs:enumeration value="CMET"/>
            <xs:enumeration value="MMET"/>
            <xs:enumeration value="LITR"/>
            <xs:enumeration value="CELI"/>
            <xs:enumeration value="MILI"/>
            <xs:enumeration value="GBOU"/>
            <xs:enumeration value="USOU"/>
            <xs:enumeration value="GBQA"/>
            <xs:enumeration value="USQA"/>
            <xs:enumeration value="GBPI"/>
            <xs:enumeration value="USPI"/>
            <xs:enumeration value="MILE"/>
            <xs:enumeration value="KMET"/>
            <xs:enumeration value="YARD"/>
            <xs:enumeration value="SQKI"/>
            <xs:enumeration value="HECT"/>
            <xs:enumeration value="ARES"/>
            <xs:enumeration value="SMET"/>
            <xs:enumeration value="SCMT"/>
            <xs:enumeration value="SMIL"/>
            <xs:enumeration value="SQMI"/>
            <xs:enumeration value="SQYA"/>
            <xs:enumeration value="SQFO"/>
            <xs:enumeration value="SQIN"/>
            <xs:enumeration value="ACRE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="UserInterface2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MDSP"/>
            <xs:enumeration value="CDSP"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="YesNoIndicator">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:complexType name="YieldedOrValueType1Choice">
        <xs:choice>
            <xs:element name="Yldd" type="YesNoIndicator"/>
            <xs:element name="ValTp" type="PriceValueType1Code"/>
        </xs:choice>
    </xs:complexType>
</xs:schema>