<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="IncomingTransactionReport" pageWidth="1741" pageHeight="842" orientation="Landscape" columnWidth="1721" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10" uuid="7c8e7a0d-32d2-496c-8ba8-89b067e34c92">
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter.jrdax"/>
    <property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
    <property name="net.sf.jasperreports.export.xls.remove.empty.space.between.columns" value="true"/>
    <property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
    <property name="net.sf.jasperreports.export.xls.exclude.origin.band.1" value="pageHeader"/>
    <property name="net.sf.jasperreports.export.xls.exclude.origin.band.2" value="pageFooter"/>
    <property name="net.sf.jasperreports.export.xls.font.size.fix.enabled" value="false"/>
    <property name="net.sf.jasperreports.export.xls.wrap.text" value="true"/>
    <property name="com.jaspersoft.studio.data.sql.tables" value=""/>
    <property name="com.jaspersoft.studio.unit." value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
    <property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
    <parameter name="sessionSequence" class="java.lang.String"/>
    <parameter name="sessionDate" class="java.lang.String"/>
    <parameter name="participantCode" class="java.lang.String"/>
    <parameter name="logo" class="java.lang.String"/>
    <queryString>
        <![CDATA[WITH IncomingData AS (
    -- Incoming Transactions Data
    SELECT
        tr.INSTDPARTIID AS participantID,
        COUNT(CASE WHEN tr.MSGTYPEID = 6 THEN 1 END) AS creditTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 6 THEN tr.AMOUNT ELSE 0 END) AS creditTransactionAmount,
        COUNT(CASE WHEN tr.MSGTYPEID = 2 THEN 1 END) AS debitTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 2 THEN tr.AMOUNT ELSE 0 END) AS debitTransactionAmount,
        COUNT(CASE WHEN tr.MSGTYPEID = 3 THEN 1 END) AS returnTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 3 THEN tr.AMOUNT ELSE 0 END) AS returnTransactionAmount,
        COUNT(CASE WHEN tr.MSGTYPEID = 5 THEN 1 END) AS reversalTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 5 THEN tr.AMOUNT ELSE 0 END) AS reversalTransactionAmount,
        0 AS creditBatchCount,
        0 AS creditBatchAmount,
        0 AS debitBatchCount,
        0 AS debitBatchAmount
    FROM ATSTRANSACTIONS tr
    JOIN ATSBDI_SESSIONS ss ON ss.id = tr.SESSIONID
    WHERE (($P{sessionSequence} IS NOT NULL AND ss.SESSIONSEQ = $P{sessionSequence})
           OR ($P{sessionDate} IS NOT NULL AND TRUNC(ss.BUSINESSDT) = TO_DATE($P{sessionDate}, 'YYYY-MM-DD'))
           OR ($P{sessionSequence} IS NULL AND $P{sessionDate} IS NULL))
        AND tr.MSGTYPEID IN (6, 2, 3, 5)
        AND (($P{participantCode} IS NULL)
             OR ($P{participantCode} = 'ALL')
             OR (tr.INSTDPARTIID IN (
                 SELECT p.ID FROM ATSPRT_Participants p
                 WHERE p.CODE IN (SELECT TRIM(REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL))
                                  FROM DUAL
                                  CONNECT BY REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL) IS NOT NULL)
             )))
    GROUP BY tr.INSTDPARTIID

    UNION ALL

    -- Incoming Batches Data
    SELECT
        ab.INSTDPARTIID AS participantID,
        0 AS creditTransactionCount,
        0 AS creditTransactionAmount,
        0 AS debitTransactionCount,
        0 AS debitTransactionAmount,
        0 AS returnTransactionCount,
        0 AS returnTransactionAmount,
        0 AS reversalTransactionCount,
        0 AS reversalTransactionAmount,
        COUNT(CASE WHEN ab.MSGTYPEID = 6 THEN 1 END) AS creditBatchCount,
        SUM(CASE WHEN ab.MSGTYPEID = 6 THEN ab.AMOUNT ELSE 0 END) AS creditBatchAmount,
        COUNT(CASE WHEN ab.MSGTYPEID = 2 THEN 1 END) AS debitBatchCount,
        SUM(CASE WHEN ab.MSGTYPEID = 2 THEN ab.AMOUNT ELSE 0 END) AS debitBatchAmount
    FROM ATSBATCHES ab
    JOIN ATSBDI_SESSIONS ss ON ss.id = ab.SESSIONID
    WHERE (($P{sessionSequence} IS NOT NULL AND ss.SESSIONSEQ = $P{sessionSequence})
           OR ($P{sessionDate} IS NOT NULL AND TRUNC(ss.BUSINESSDT) = TO_DATE($P{sessionDate}, 'YYYY-MM-DD'))
           OR ($P{sessionSequence} IS NULL AND $P{sessionDate} IS NULL))
        AND ab.MSGTYPEID IN (6, 2, 3, 5)
        AND (($P{participantCode} IS NULL)
             OR ($P{participantCode} = 'ALL')
             OR (ab.INSTDPARTIID IN (
                 SELECT p.ID FROM ATSPRT_Participants p
                 WHERE p.CODE IN (SELECT TRIM(REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL))
                                  FROM DUAL
                                  CONNECT BY REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL) IS NOT NULL)
             )))
    GROUP BY ab.INSTDPARTIID
),

AggregatedIncoming AS (
    SELECT
        participantID,
        SUM(creditTransactionCount) AS creditTransactionCount,
        SUM(creditTransactionAmount) AS creditTransactionAmount,
        SUM(debitTransactionCount) AS debitTransactionCount,
        SUM(debitTransactionAmount) AS debitTransactionAmount,
        SUM(returnTransactionCount) AS returnTransactionCount,
        SUM(returnTransactionAmount) AS returnTransactionAmount,
        SUM(reversalTransactionCount) AS reversalTransactionCount,
        SUM(reversalTransactionAmount) AS reversalTransactionAmount,
        SUM(creditBatchCount) AS creditBatchCount,
        SUM(creditBatchAmount) AS creditBatchAmount,
        SUM(debitBatchCount) AS debitBatchCount,
        SUM(debitBatchAmount) AS debitBatchAmount
    FROM IncomingData
    GROUP BY participantID
),

GrandTotals AS (
    SELECT
        COALESCE(SUM(creditTransactionCount), 0) AS totalCreditTransactionCount,
        COALESCE(SUM(creditTransactionAmount + creditBatchAmount), 0) AS totalCreditTotalAmount,
        COALESCE(SUM(debitTransactionCount), 0) AS totalDebitTransactionCount,
        COALESCE(SUM(debitTransactionAmount + debitBatchAmount), 0) AS totalDebitTotalAmount,
        COALESCE(SUM(returnTransactionCount), 0) AS totalReturnTransactionCount,
        COALESCE(SUM(returnTransactionAmount), 0) AS totalReturnTransactionAmount,
        COALESCE(SUM(reversalTransactionCount), 0) AS totalReversalTransactionCount,
        COALESCE(SUM(reversalTransactionAmount), 0) AS totalReversalTransactionAmount,
        COALESCE(SUM(creditBatchCount), 0) AS totalCreditBatchCount,
        COALESCE(SUM(debitBatchCount), 0) AS totalDebitBatchCount,
        COALESCE(SUM(creditTransactionCount + debitTransactionCount + returnTransactionCount + reversalTransactionCount), 0) AS totalAllTransactionCount,
        COALESCE(SUM(creditTransactionAmount + debitTransactionAmount + returnTransactionAmount + reversalTransactionAmount + creditBatchAmount + debitBatchAmount), 0) AS totalAllTransactionAmount,
        COALESCE(SUM(creditBatchCount + debitBatchCount), 0) AS totalAllBatchCount
    FROM AggregatedIncoming
)

SELECT
    ROW_NUMBER() OVER (ORDER BY p.CODE) AS sequence,
    p.CODE AS participantCode,
    p.NAME AS participantName,

    -- Session information
COALESCE($P{sessionSequence},
         (SELECT LISTAGG(DISTINCT ss.SESSIONSEQ, ',') WITHIN GROUP (ORDER BY ss.SESSIONSEQ)
          FROM ATSBDI_SESSIONS ss
          WHERE EXISTS (SELECT 1 FROM ATSTRANSACTIONS tr WHERE tr.SESSIONID = ss.id)
             OR EXISTS (SELECT 1 FROM ATSBATCHES ab WHERE ab.SESSIONID = ss.id))) AS sessionSequence,

    -- Direct Credit Batch Data
    COALESCE(ao.creditBatchCount, 0) AS creditBatchCount,
    CASE
        WHEN gt.totalAllBatchCount > 0 THEN
            ROUND((COALESCE(ao.creditBatchCount, 0) * 100.0) / gt.totalAllBatchCount, 2)
        ELSE 0
    END AS creditBatchCountPercentage,

    -- Direct Credit Transaction Data
    COALESCE(ao.creditTransactionCount, 0) AS creditTransactionCount,
    CASE
        WHEN gt.totalAllTransactionCount > 0 THEN
            ROUND((COALESCE(ao.creditTransactionCount, 0) * 100.0) / gt.totalAllTransactionCount, 2)
        ELSE 0
    END AS creditTransactionCountPercentage,

    -- Direct Credit Transaction Amount (separate)
    COALESCE(ao.creditTransactionAmount, 0) AS creditTransactionAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND((COALESCE(ao.creditTransactionAmount, 0) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS creditTransactionAmountPercentage,

    -- Direct Credit Total Amount (Transactions + Batches)
    (COALESCE(ao.creditTransactionAmount, 0) + COALESCE(ao.creditBatchAmount, 0)) AS creditTotalAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND(((COALESCE(ao.creditTransactionAmount, 0) + COALESCE(ao.creditBatchAmount, 0)) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS creditTotalAmountPercentage,

    -- Direct Debit Batch Data
    COALESCE(ao.debitBatchCount, 0) AS debitBatchCount,
    CASE
        WHEN gt.totalAllBatchCount > 0 THEN
            ROUND((COALESCE(ao.debitBatchCount, 0) * 100.0) / gt.totalAllBatchCount, 2)
        ELSE 0
    END AS debitBatchCountPercentage,

    -- Direct Debit Transaction Data
    COALESCE(ao.debitTransactionCount, 0) AS debitTransactionCount,
    CASE
        WHEN gt.totalAllTransactionCount > 0 THEN
            ROUND((COALESCE(ao.debitTransactionCount, 0) * 100.0) / gt.totalAllTransactionCount, 2)
        ELSE 0
    END AS debitTransactionCountPercentage,

    -- Direct Debit Transaction Amount (separate)
    COALESCE(ao.debitTransactionAmount, 0) AS debitTransactionAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND((COALESCE(ao.debitTransactionAmount, 0) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS debitTransactionAmountPercentage,

    -- Direct Debit Total Amount (Transactions + Batches)
    (COALESCE(ao.debitTransactionAmount, 0) + COALESCE(ao.debitBatchAmount, 0)) AS debitTotalAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND(((COALESCE(ao.debitTransactionAmount, 0) + COALESCE(ao.debitBatchAmount, 0)) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS debitTotalAmountPercentage,

    -- Return Request Data
    COALESCE(ao.returnTransactionCount, 0) AS returnTransactionCount,
    CASE
        WHEN gt.totalAllTransactionCount > 0 THEN
            ROUND((COALESCE(ao.returnTransactionCount, 0) * 100.0) / gt.totalAllTransactionCount, 2)
        ELSE 0
    END AS returnTransactionCountPercentage,

    COALESCE(ao.returnTransactionAmount, 0) AS returnTransactionAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND((COALESCE(ao.returnTransactionAmount, 0) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS returnTransactionAmountPercentage,

    -- Reversal Request Data
    COALESCE(ao.reversalTransactionCount, 0) AS reversalTransactionCount,
    CASE
        WHEN gt.totalAllTransactionCount > 0 THEN
            ROUND((COALESCE(ao.reversalTransactionCount, 0) * 100.0) / gt.totalAllTransactionCount, 2)
        ELSE 0
    END AS reversalTransactionCountPercentage,

    COALESCE(ao.reversalTransactionAmount, 0) AS reversalTransactionAmount,
    CASE
        WHEN gt.totalAllTransactionAmount > 0 THEN
            ROUND((COALESCE(ao.reversalTransactionAmount, 0) * 100.0) / gt.totalAllTransactionAmount, 2)
        ELSE 0
    END AS reversalTransactionAmountPercentage,

    -- Grand Totals (same for all rows - for report footer)
    gt.totalCreditBatchCount,
    gt.totalCreditTransactionCount,
    gt.totalCreditTotalAmount AS totalCreditTransactionAmount,
    gt.totalDebitBatchCount,
    gt.totalDebitTransactionCount,
    gt.totalDebitTotalAmount AS totalDebitTransactionAmount,
    gt.totalReturnTransactionCount AS totalReturnTransactionCount,
    gt.totalReturnTransactionAmount AS totalReturnTransactionAmount,
    gt.totalReversalTransactionCount AS totalReversalTransactionCount,
    gt.totalReversalTransactionAmount AS totalReversalTransactionAmount,
    gt.totalCreditTotalAmount,
    gt.totalDebitTotalAmount

FROM ATSPRT_Participants p
CROSS JOIN GrandTotals gt
LEFT JOIN AggregatedIncoming ao ON p.ID = ao.participantID
WHERE (($P{participantCode} IS NULL)
       OR ($P{participantCode} = 'ALL')
       OR (p.CODE IN (SELECT TRIM(REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL))
                      FROM DUAL
                      CONNECT BY REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL) IS NOT NULL)))
ORDER BY p.CODE]]>
    </queryString>
    <field name="participantCode" class="java.lang.String"/>
    <field name="participantName" class="java.lang.String"/>
    <field name="creditBatchCount" class="java.lang.String"/>
    <field name="creditBatchCountPercentage" class="java.lang.String"/>
    <field name="creditTransactionCount" class="java.lang.String"/>
    <field name="creditTransactionCountPercentage" class="java.lang.String"/>
    <field name="creditTransactionAmount" class="java.lang.String"/>
    <field name="creditTransactionAmountPercentage" class="java.lang.String"/>
    <field name="debitBatchCount" class="java.lang.String"/>
    <field name="debitBatchCountPercentage" class="java.lang.String"/>
    <field name="debitTransactionCount" class="java.lang.String"/>
    <field name="debitTransactionCountPercentage" class="java.lang.String"/>
    <field name="debitTransactionAmount" class="java.lang.String"/>
    <field name="debitTransactionAmountPercentage" class="java.lang.String"/>
    <field name="returnTransactionCountPercentage" class="java.lang.String"/>
    <field name="reversalTransactionCountPercentage" class="java.lang.String"/>
    <field name="reversalTransactionAmountPercentage" class="java.lang.String"/>
    <field name="returnTransactionCount" class="java.lang.String"/>
    <field name="returnTransactionAmount" class="java.lang.String"/>
    <field name="reversalTransactionCount" class="java.lang.String"/>
    <field name="reversalTransactionAmount" class="java.lang.String"/>
    <field name="totalCreditBatchCount" class="java.lang.String"/>
    <field name="totalCreditTransactionCount" class="java.lang.String"/>
    <field name="totalCreditTransactionAmount" class="java.lang.String"/>
    <field name="sessionSequence" class="java.lang.String"/>
    <field name="totalDebitBatchCount" class="java.lang.String"/>
    <field name="totalDebitTransactionCount" class="java.lang.String"/>
    <field name="totalDebitTransactionAmount" class="java.lang.String"/>
    <field name="totalReturnTransactionCount" class="java.lang.String"/>
    <field name="totalReturnTransactionAmount" class="java.lang.String"/>
    <field name="totalReversalTransactionCount" class="java.lang.String"/>
    <field name="totalReversalTransactionAmount" class="java.lang.String"/>
    <field name="totalCreditTotalAmount" class="java.lang.String"/>
    <field name="totalDebitTotalAmount" class="java.lang.String"/>
    <field name="returnTransactionAmountPercentage" class="java.lang.String"/>
    <variable name="SerialNumber" class="java.lang.Integer">
        <variableExpression><![CDATA[$V{SerialNumber} + 1]]></variableExpression>
        <initialValueExpression><![CDATA[0]]></initialValueExpression>
    </variable>
    <variable name="SumCreditBatchCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditBatchCount})]]></variableExpression>
    </variable>
    <variable name="SumCreditBatchCountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditBatchCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumCreditTransactionCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditTransactionCount})]]></variableExpression>
    </variable>
    <variable name="SumCreditTransactionCountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditTransactionCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumCreditTransactionAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditTransactionAmount})]]></variableExpression>
    </variable>
    <variable name="SumCreditTransactionAmountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{creditTransactionAmountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumDebitBatchCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitBatchCount})]]></variableExpression>
    </variable>
    <variable name="SumDebitBatchCountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitBatchCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumDebitTransactionCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitTransactionCount})]]></variableExpression>
    </variable>
    <variable name="SumDebitTransactionCountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitTransactionCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumDebitTransactionAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitTransactionAmount})]]></variableExpression>
    </variable>
    <variable name="SumDebitTransactionAmountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{debitTransactionAmountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumInwardCreditBatchAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{returnTransactionCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumReversalTransactionCountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionCountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumReversalTransactionAmountPercentage" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionAmountPercentage})]]></variableExpression>
    </variable>
    <variable name="SumReturnTransactionCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{returnTransactionCount})]]></variableExpression>
    </variable>
    <variable name="SumReturnTransactionAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{returnTransactionAmount})]]></variableExpression>
    </variable>
    <variable name="SumReversalTransactionCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionCount})]]></variableExpression>
    </variable>
    <variable name="SumReversalTransactionAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionAmount})]]></variableExpression>
    </variable>
    <variable name="SumTotalCreditBatchCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{totalCreditBatchCount})]]></variableExpression>
    </variable>
    <variable name="SumTotalCreditTransactionCount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{totalCreditTransactionCount})]]></variableExpression>
    </variable>
    <variable name="SumTotalCreditTransactionAmount" class="java.lang.Double" calculation="Sum">
        <variableExpression><![CDATA[Double.parseDouble($F{totalCreditTransactionAmount})]]></variableExpression>
    </variable>
    <title>
        <band height="150">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <image hAlign="Center" vAlign="Middle" isUsingCache="true">
                <reportElement x="800" y="2" width="120" height="97" uuid="6b370881-e683-4513-81cf-55d1963d56a1"/>
                <imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
            </image>
            <staticText>
                <reportElement x="590" y="110" width="495" height="40" uuid="0072f9ef-de10-4730-a471-6e2ed6f40f14"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="18" isBold="true"/>
                </textElement>
                <text><![CDATA[Kuwait Automated Clearing House]]></text>
            </staticText>
            <staticText>
                <reportElement x="0" y="0" width="210" height="50" uuid="cd91385e-3921-40b2-98bf-f88eeba0315b"/>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="9" isBold="true"/>
                </textElement>
                <text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw

	]]></text>
            </staticText>
            <staticText>
                <reportElement x="1490" y="3" width="230" height="50" uuid="9406321f-f544-4652-9ec5-486e3fc67adb"/>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="9" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[	بنك الكويت المركزي
	ص.ب. 526 صفاة 13006 الكويت
	http://www.cbk.gov.kw
]]></text>
            </staticText>
        </band>
    </title>
    <pageHeader>
        <band height="100">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <textField>
                <reportElement x="0" y="50" width="200" height="20" uuid="41c54a5d-ba99-4dce-bd73-1ede5cd4e2b2"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["Session Sequence: " + ($P{sessionSequence} == null ? "-" : $P{sessionSequence})]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1580" y="50" width="150" height="20" uuid="35bdd8c5-c26f-4876-8f79-bffeb83129f4"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <textFieldExpression><![CDATA["Kuwaiti Dinar / دينار كويتي"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1490" y="50" width="81" height="20" uuid="246862b6-040c-4cb6-8b87-51dcd0d49d64"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["Currency"]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Transparent" x="1" y="20" width="550" height="30" forecolor="#030000" backcolor="#0702F7" uuid="5f47afa8-a3ff-4006-84e1-50a4ee439ec1">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
                </reportElement>
                <box>
                    <pen lineWidth="0.0" lineColor="#030000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="14" isBold="true" isUnderline="true"/>
                </textElement>
                <text><![CDATA[Filters]]></text>
            </staticText>
            <textField textAdjust="ScaleFont">
                <reportElement x="0" y="80" width="1745" height="20" uuid="bfeeabe4-6d0a-4ddc-be21-948433d47768"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["Participant: " + ($P{participantCode} != null ? $P{participantCode}  :  "-")]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="590" y="0" width="495" height="40" uuid="101235fe-5c78-4b54-a8c0-3e5e596d2160"/>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="18" isBold="true"/>
                </textElement>
                <text><![CDATA[IncomingTransaction Report]]></text>
            </staticText>
            <textField>
                <reportElement x="220" y="50" width="200" height="20" uuid="5035dd80-04f8-412a-8f31-2fdb20c7d305"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="true"/>
                </textElement>
                <textFieldExpression><![CDATA["Session Date: " + ($P{sessionDate} == null ? "-" : $P{sessionDate})]]></textFieldExpression>
            </textField>
        </band>
    </pageHeader>
    <columnHeader>
        <band height="139">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <staticText>
                <reportElement mode="Opaque" x="61" y="90" width="82" height="49" backcolor="#C2C2C2" uuid="1f76d7de-5823-4c23-bf54-4d3fb5dc3d3d">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[رمز المشارك
]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="143" y="90" width="72" height="49" backcolor="#C4C2C2" uuid="62b716e9-cca3-4b5a-b259-537d2d7530cf">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[اسم المشارك]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="215" y="90" width="64" height="49" backcolor="#C2C2C2" uuid="40bc6423-7fce-46c5-8233-91e838dfb626">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[أعداد دفعات التحويل المباشر"]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1" y="90" width="60" height="49" isRemoveLineWhenBlank="true" forecolor="#140505" backcolor="#C7C5C5" uuid="09d35ff8-5505-41b0-b830-7c026b3d2ac1">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="6.5" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[تسلسل]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="279" y="90" width="78" height="49" backcolor="#BFBDBD" uuid="fd847aa1-654e-420c-9fff-c4e30413fa7b">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد دفعات الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="357" y="90" width="66" height="49" backcolor="#C2C0C0" uuid="972f9417-c33e-4f32-b960-e8b515fc8a84">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[عدد معاملات الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="423" y="90" width="85" height="49" backcolor="#BFBFBF" uuid="e3d0185b-3a49-4e36-82b4-cbcd6798fe2f">
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد معاملات الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="508" y="90" width="89" height="49" backcolor="#C2C0C0" uuid="f4bad8fd-e754-45ec-b07c-e131a13bcc52">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="597" y="90" width="99" height="49" backcolor="#C4C2C2" uuid="6d509cc3-2cc6-4582-909d-7de95c170789">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لإجمالي مبلغ الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="696" y="90" width="60" height="49" backcolor="#C2C0C0" uuid="737e5b69-b092-47e3-91f8-6c5ad924c909">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[عدد دفعات الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="756" y="90" width="69" height="49" backcolor="#C2C0C0" uuid="cf1f23a7-cfe5-4510-934a-4f2140b8ad22">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لإجمالي مبلغ طلبات الإلغاء]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="825" y="90" width="80" height="49" backcolor="#C9C7C7" uuid="f49b89eb-2e1d-424d-9754-a79b816287c1">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[عدد معاملات الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="905" y="90" width="80" height="49" backcolor="#BDBBBB" uuid="d7e0187f-1526-40c1-a9ab-93008b6cdd12">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد معاملات الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="985" y="90" width="80" height="49" backcolor="#C2C2C2" uuid="8626318b-6344-4ef0-b77b-ece5bb59bdbd">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1065" y="90" width="78" height="49" backcolor="#BFBDBD" uuid="8b336e0d-ab0e-45c3-9cbc-e0a885c2d021">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لإجمالي مبلغ الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1143" y="90" width="62" height="49" backcolor="#BAB8B8" uuid="7b82c8cf-ee38-4dab-853e-6d33ed330fe3">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[عدد طلبات الإرجاع]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1205" y="90" width="90" height="49" backcolor="#BAB6B6" uuid="3e000e9d-bb33-4eee-aa2d-0179ad33c661">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد طلبات الإرجاع]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1295" y="90" width="80" height="49" backcolor="#BFBDBD" uuid="9591bf8e-7321-4240-bcc8-e7aeb84979ee">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ طلبات الإرجاع
]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1375" y="90" width="70" height="49" backcolor="#B8B4B4" uuid="f1faa425-ce2d-4558-b19f-66da5b274395">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لإجمالي مبلغ طلبات الإرجاع]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1445" y="90" width="60" height="49" backcolor="#C2C0C0" uuid="b820dabb-e5f0-431a-8a6b-badafde2799a">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[عدد طلبات الإلغاء]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1505" y="90" width="60" height="49" backcolor="#C2C0C0" uuid="602d338a-3aff-47ab-98e7-a52d151ee929">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد طلبات الإلغاء]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1565" y="90" width="72" height="49" backcolor="#BABABA" uuid="2d833a4a-b3b6-40b5-99aa-c422cef8c01f">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ طلبات الإلغاء]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1637" y="90" width="92" height="49" backcolor="#BFBDBD" uuid="99887f85-ceab-4243-8a43-23fc625d61eb">
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                    <pen lineWidth="1.5"/>
                    <topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                    <rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لإجمالي مبلغ طلبات الإلغاء]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1637" y="20" width="92" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="1271429d-6fe4-4552-a996-3ad4e8da29a5">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal requests total amount percentage ]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1565" y="20" width="72" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="767399cc-39f3-42a2-8499-66339d8b1258">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal requests total amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1505" y="20" width="60" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="5927fefe-7acb-495f-9fb2-d8921df6a786">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal requests count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1445" y="20" width="60" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="2387a259-fa95-4abb-8b28-c821ac4c3bf1">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal requests count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1375" y="20" width="70" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="1ec8411e-fd07-48fc-b2fe-1bd451d3a683">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return requests total amount percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1295" y="20" width="80" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="525b630b-7840-4320-9b53-4f81e5715bd3">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return requests total amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1205" y="20" width="90" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="003d66c5-74fe-4237-9331-1aa7b6dcbf9f">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return requests count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1143" y="20" width="62" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="dd9b8d56-a636-446e-a9b4-f71007cc27e1">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return requests count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1065" y="20" width="78" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="6f1f56aa-33c5-42d3-ad11-4e2ad847b655">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit total amount percentage ]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="985" y="20" width="80" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="560e8209-e541-45e7-b652-************">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit total amount ]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="905" y="20" width="80" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="bf098446-eebd-4486-8bb6-08fe84e38611">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit transactions count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="825" y="20" width="80" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="b39d7fe0-0953-411b-9636-9efef678d308">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit transactions count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="756" y="20" width="69" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="41d74ecc-52ba-4e1c-8eda-f383df00fb0c">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit batches count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="696" y="20" width="60" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="53b52682-3760-4c26-841b-9e1f0e381c33">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit batches count.]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="597" y="20" width="99" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="61bbc6d0-d833-43e7-bd51-0bade77288af">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit total amount percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="508" y="20" width="89" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="00bcf2e5-45a1-4be7-b703-221571f5bb3d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit total amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="423" y="20" width="85" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="07c4b701-e85a-4467-8ae9-69669101d9a7">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit transactions count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="357" y="20" width="66" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="f9828860-922d-4dcb-8250-89868b546d4b">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit transactions count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="279" y="20" width="78" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="2eabc947-dd72-4e69-b1e0-02167e4ca96d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit batches count percentage]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="215" y="20" width="64" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="ff617721-0d7e-4be6-97ba-2c48c7e03c7a">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit batches count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="143" y="20" width="72" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="471c5d46-30fe-44d4-ba12-7012291ba3b0">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Participant Name]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="61" y="20" width="82" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="5a6eac3f-f1e8-4760-9958-feaea4b86947">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Participant Code]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1" y="20" width="60" height="69" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="13e434d5-6ed2-4df1-8bbf-e4292e8fff28">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Sequence]]></text>
            </staticText>
        </band>
    </columnHeader>
    <detail>
        <band height="45" splitType="Stretch">
            <property name="com.jaspersoft.studio.unit.height" value="px"/>
            <property name="com.jaspersoft.studio.layout"/>
            <textField>
                <reportElement x="1" y="0" width="60" height="43" uuid="c790d0d6-9234-41cf-b8ed-2c1509b850d6">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{sessionSequence}!=null?$F{sessionSequence}: "-"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="215" y="0" width="64" height="43" uuid="799d42f0-2b1b-436f-950a-a81238a72a32">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditBatchCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="423" y="0" width="85" height="43" uuid="6c4884aa-00c0-4fb7-9a9a-b2072ccc3667">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditTransactionCountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="61" y="0" width="82" height="43" uuid="5d10e6a4-ebe9-435c-a04c-a8b60d4a189c">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{participantCode}!=null?$F{participantCode}: "-"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="143" y="0" width="72" height="43" uuid="1df9f263-675a-49e9-8b73-efde2f8278e7">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                </reportElement>
                <box padding="0">
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{participantName}!=null?$F{participantName}: "-"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="597" y="0" width="99" height="43" uuid="aed7579e-0d0f-48d5-9f6d-9b8074e00b89">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditTransactionAmountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="756" y="0" width="69" height="43" uuid="20be8e8f-ceef-4e65-b753-29390d39fcfe">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{debitBatchCountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="905" y="0" width="80" height="43" uuid="b9fc7c8e-c9f7-4929-b419-92a5de6bafd9">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{debitTransactionCountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1065" y="0" width="78" height="43" uuid="714c8fcb-6a95-4f44-a6df-e69e2a2b9233">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditTransactionCountPercentage}!=null?$F{creditTransactionCountPercentage}: "-"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="985" y="0" width="80" height="43" uuid="6ff2cac8-10a5-492f-9b3d-0d84c3c8ed6d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalDebitTotalAmount}!=null?$F{totalDebitTotalAmount}: 0]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1205" y="0" width="90" height="43" uuid="9c1c5694-59e2-4b4a-8e5e-701ffdb6a5d4">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{returnTransactionCountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1295" y="0" width="80" height="43" uuid="79a78d1b-96ec-44da-81fa-987e9615371e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{returnTransactionAmount}!=null?$F{returnTransactionAmount}: "-"]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1375" y="0" width="70" height="43" uuid="8addddba-afe7-4dfb-bc3f-1f993b40cd5b">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{returnTransactionAmountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1565" y="0" width="72" height="43" uuid="ed696890-1c88-460d-b674-bf56d603366e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{reversalTransactionAmount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1637" y="0" width="92" height="43" uuid="e93f68f2-d668-4a82-a221-22759238b010">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{reversalTransactionAmountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="279" y="0" width="78" height="43" uuid="6e6cc8ec-6e59-441d-9041-0e8b8be89b21">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditBatchCountPercentage}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="357" y="0" width="66" height="43" uuid="c0061356-b915-4c24-82d9-b1b7bd62490f">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="508" y="0" width="89" height="43" uuid="de75fb4f-7b28-455e-a487-d4ffc9300586">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{creditTransactionAmount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="696" y="0" width="60" height="43" uuid="73ca2fd4-53aa-4501-b300-56c386483811">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{debitBatchCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="825" y="0" width="80" height="43" uuid="5e3eafe4-2cc7-4005-a1a0-bca1eadd9db7">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{debitTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1143" y="0" width="62" height="43" uuid="04fd019b-2752-4e0e-8e0a-a95133c9eb3f">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{returnTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1445" y="0" width="60" height="43" uuid="dc5e58b0-3e43-482f-a44a-d43f5b05396e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{reversalTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1505" y="0" width="60" height="43" uuid="2dcaa95b-e57e-47fc-9e5e-971b7681f3cf">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="8"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{reversalTransactionCountPercentage}]]></textFieldExpression>
            </textField>
        </band>
    </detail>
    <columnFooter>
        <band height="23" splitType="Stretch">
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
        </band>
    </columnFooter>
    <pageFooter>
        <band height="46" splitType="Stretch">
            <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
            <textField>
                <reportElement x="0" y="0" width="575" height="46" uuid="8302e160-d22d-4e27-b0cf-e75d9e5f8ffd"/>
                <textElement textAlignment="Right" verticalAlignment="Middle">
                    <font fontName="Arial" size="9"/>
                </textElement>
                <textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Report">
                <reportElement x="575" y="0" width="573" height="46" uuid="88a8b787-5935-4613-b087-7ad3859dde2f"/>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="9"/>
                </textElement>
                <textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
            <textField pattern="MMM, dd, yyyy">
                <reportElement x="1148" y="0" width="573" height="46" uuid="a67fa361-7bc5-43e1-beac-abb4f48095c2"/>
                <textElement verticalAlignment="Middle">
                    <font fontName="Arial" size="9"/>
                </textElement>
                <textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
            </textField>
        </band>
    </pageFooter>
    <summary>
        <band height="146" splitType="Stretch">
            <staticText>
                <reportElement mode="Opaque" x="30" y="40" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="ff96ee51-a791-4afb-bbde-a1a70e8e05cc">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit batches total count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="30" y="75" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="6b94ec4a-d3d5-4986-b295-af278e9ab7c3">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[النسبة المئوية لعدد دفعات الائتمان المباشر
]]></text>
            </staticText>
            <textField>
                <reportElement x="30" y="110" width="160" height="36" uuid="b44cb8d5-45ba-4ba0-b693-657ba8ea9ec4">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="false"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalCreditBatchCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="190" y="110" width="150" height="36" uuid="c9a21ec7-c6a3-4414-a27d-1e4f6be8fd68">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalCreditTransactionCount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="190" y="40" width="150" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="9c82b2c4-bfc8-4f68-8db8-c233056a4c8c">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit transactions total count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="190" y="75" width="150" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="2bc2de55-46ab-4b57-8b92-5255cb88f691">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[إجمالي عدد حركات الائتمان المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="340" y="40" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="2fcbc35b-18ed-4e7c-ac34-e0553957a2d0">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Credit grand amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="510" y="40" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="1c327fc9-2307-42b6-9362-c1a2e24fff35">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit batches total count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="510" y="75" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="aa3b6538-f7e9-4431-8630-199eb06c1cc8">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[إجمالي عدد دفعات الخصم المباشر]]></text>
            </staticText>
            <textField>
                <reportElement x="510" y="110" width="160" height="36" uuid="fe6f3965-3dee-4379-b21d-5be14abc2fc8">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalDebitBatchCount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="340" y="75" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="79283beb-c3f9-4953-9686-3c36aade5e18">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ الائتمان المباشر]]></text>
            </staticText>
            <textField>
                <reportElement x="340" y="110" width="170" height="36" uuid="b39cbe77-99fa-45d8-85f1-f3f560674262">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="false"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalCreditTransactionAmount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="670" y="40" width="160" height="35" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="05781d61-858d-43b5-90a8-a89a8b07918d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit transactions total count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="670" y="75" width="160" height="35" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="df7d4c4b-8df2-4f85-8206-b137dc39e58d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[إجمالي عدد معاملات الخصم المباشر]]></text>
            </staticText>
            <textField>
                <reportElement x="670" y="110" width="160" height="36" uuid="ada8cd9e-3728-4c51-b238-a7b6b387453a">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="false"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalDebitTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="830" y="110" width="150" height="36" uuid="1a68be31-ae7a-480a-b220-ea0fc6ecc1a6">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalDebitTransactionAmount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="830" y="75" width="150" height="35" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="702103c1-ff39-4c59-af51-da3eeea12f74">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[إجمالي مبلغ الخصم المباشر]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="830" y="40" width="150" height="35" forecolor="#0D0B0B" backcolor="#C2C2C2" uuid="f7dcc0d6-521a-44dd-9f77-a2cf12ce107c">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Direct Debit grand amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="980" y="40" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="cfe6aa74-75ec-43fb-87ee-87f147ed36c9">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="980" y="75" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="a2c15f4b-b36b-424d-9a21-094b6b182fac">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[عدد المرتجعات]]></text>
            </staticText>
            <textField>
                <reportElement x="980" y="110" width="170" height="36" uuid="eedf4780-ee75-4db8-91ed-16c0a3556be1">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="false"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalReturnTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1150" y="110" width="160" height="36" uuid="e6d1dcc8-5fe7-4063-9a97-585cfe2d124e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalReturnTransactionAmount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="1150" y="75" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="1e2882df-52b5-48d5-9596-aa3275c5b84e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[مبلغ المرتجعات]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1150" y="40" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="9c08e61d-4aa2-452e-b755-06d253350ea1">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Return amount]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1310" y="40" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="f1dabb8f-567e-4332-98d8-cc204abed562">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal count]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1310" y="75" width="170" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="fc137686-7ab8-4986-8ad7-9d94ac11255e">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[عدد الإلغاءات]]></text>
            </staticText>
            <textField>
                <reportElement x="1310" y="110" width="170" height="36" uuid="76eb3c99-6037-47a7-a3b1-3539ccadfcf6">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                    <property name="com.jaspersoft.studio.unit.y" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12" isBold="false"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalReversalTransactionCount}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="1480" y="110" width="160" height="36" uuid="45ed53d7-1cde-4f5a-a98e-8c8e0b6f7f78">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="12"/>
                </textElement>
                <textFieldExpression><![CDATA[$F{totalReversalTransactionAmount}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement mode="Opaque" x="1480" y="75" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="4f5f33de-df6e-4677-bb6d-e96fa830e4a8">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[مبلغ الإلغاءات]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Opaque" x="1480" y="40" width="160" height="35" forecolor="#120B0B" backcolor="#C2C2C2" uuid="70762212-edd3-4661-9b7a-17b654caaa2d">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
                    <property name="com.jaspersoft.studio.unit.height" value="px"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                    <property name="com.jaspersoft.studio.unit.x" value="px"/>
                </reportElement>
                <box>
                    <pen lineWidth="1.5" lineColor="#0F0908"/>
                </box>
                <textElement textAlignment="Center" verticalAlignment="Middle">
                    <font fontName="Arial" size="11" isBold="true"/>
                </textElement>
                <text><![CDATA[Reversal amount]]></text>
            </staticText>
        </band>
    </summary>
</jasperReport>
