<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="NetClearingPositionReport" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" >
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CentralReport.jrdax"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<parameter name="sessionSequence" class="java.lang.String"/>
	<parameter name="sessionStatus" class="java.lang.String"/>
	<parameter name="sessionDate" class="java.lang.String"/>
	<parameter name="participantName" class="java.lang.String"/>
	<parameter name="participantId" class="java.lang.String"/>
	<parameter name="logo" class="java.lang.String"/>
	<queryString>
		<![CDATA[SELECT
    p.CODE AS participantCode,
    p.NAME AS participantName,

    COALESCE(outAccAmount, 0) AS outAccAmount,
    COALESCE(outAccCount, 0) AS outAccCount,
    COALESCE(outPsysRAmount, 0) AS outPsysRAmount,
    COALESCE(outPsysRCount, 0) AS outPsysRCount,
    COALESCE(outSysRAmount, 0) AS outSysRAmount,
    COALESCE(outSysRCount, 0) AS outSysRCount,

    COALESCE(inAccAmount, 0) AS inAccAmount,
    COALESCE(inAccCount, 0) AS inAccCount,
    COALESCE(inPsysRAmount, 0) AS inPsysRAmount,
    COALESCE(inPsysRCount, 0) AS inPsysRCount,
    COALESCE(inSysRAmount, 0) AS inSysRAmount,
    COALESCE(inSysRCount, 0) AS inSysRCount

FROM ATSPRT_PARTICIPANTS p
    LEFT JOIN (
    SELECT
        t.DBTRPARTIID AS participantID,
        SUM(CASE WHEN t.REASONID IS NULL THEN t.AMOUNT ELSE 0 END) AS outAccAmount,
        COUNT(CASE WHEN t.REASONID IS NULL THEN t.ID END) AS outAccCount,
        SUM(CASE WHEN r.ISSYSTEM = 0 AND t.REASONID IS NOT NULL THEN t.AMOUNT ELSE 0 END) AS outPsysRAmount,
        COUNT(CASE WHEN r.ISSYSTEM = 0 AND t.REASONID IS NOT NULL THEN t.ID END) AS outPsysRCount,
        SUM(CASE WHEN r.ISSYSTEM = 1 THEN t.AMOUNT ELSE 0 END) AS outSysRAmount,
        COUNT(CASE WHEN r.ISSYSTEM = 1 THEN t.ID END) AS outSysRCount
    FROM ATSTRANSACTIONS t
             LEFT JOIN ATSMSG_REASONS r ON t.REASONID = r.ID
             INNER JOIN ATSBDI_SESSIONS s ON t.SESSIONID = s.ID
    WHERE s.SESSIONSEQ = '$P!{sessionSequence}'
      AND ('$P!{sessionStatus}' = '-1' OR s.CURRPERIOD LIKE '$P!{sessionStatus}')
    GROUP BY t.DBTRPARTIID
) outTransactions ON p.ID = outTransactions.participantID
         LEFT JOIN (
    SELECT
        t.CRDTRPARTIID AS participantID,
        SUM(CASE WHEN t.REASONID IS NULL THEN t.AMOUNT ELSE 0 END) AS inAccAmount,
        COUNT(CASE WHEN t.REASONID IS NULL THEN t.ID END) AS inAccCount,
        SUM(CASE WHEN r.ISSYSTEM = 0 AND t.REASONID IS NOT NULL THEN t.AMOUNT ELSE 0 END) AS inPsysRAmount,
        COUNT(CASE WHEN r.ISSYSTEM = 0 AND t.REASONID IS NOT NULL THEN t.ID END) AS inPsysRCount,
        SUM(CASE WHEN r.ISSYSTEM = 1 THEN t.AMOUNT ELSE 0 END) AS inSysRAmount,
        COUNT(CASE WHEN r.ISSYSTEM = 1 THEN t.ID END) AS inSysRCount
    FROM ATSTRANSACTIONS t
             LEFT JOIN ATSMSG_REASONS r ON t.REASONID = r.ID
             INNER JOIN ATSBDI_SESSIONS s ON t.SESSIONID = s.ID
    WHERE s.SESSIONSEQ = '$P!{sessionSequence}'
      AND ('$P!{sessionStatus}' = '-1' OR s.CURRPERIOD LIKE '$P!{sessionStatus}')
    GROUP BY t.CRDTRPARTIID
) inTransactions ON p.ID = inTransactions.participantID WHERE p.ID IN ($P!{participantId})]]>
	</queryString>
	<field name="participantCode" class="java.lang.String"/>
	<field name="participantName" class="java.lang.String"/>
	<field name="outAccAmount" class="java.lang.String"/>
	<field name="outAccCount" class="java.lang.String"/>
	<field name="outPsysRCount" class="java.lang.String"/>
	<field name="outPsysRAmount" class="java.lang.String"/>
	<field name="outSysRCount" class="java.lang.String"/>
	<field name="outSysRAmount" class="java.lang.String"/>
	<field name="inAccCount" class="java.lang.String"/>
	<field name="inAccAmount" class="java.lang.String"/>
	<field name="inPsysRCount" class="java.lang.String"/>
	<field name="inPsysRAmount" class="java.lang.String"/>
	<field name="inSysRCount" class="java.lang.String"/>
	<field name="inSysRAmount" class="java.lang.String"/>
	<variable name="SerialNumber" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{SerialNumber} + 1]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="SumOutAccAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outAccAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutAccCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outAccCount})]]></variableExpression>
	</variable>
	<variable name="SumOutPsysRCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outPsysRCount})]]></variableExpression>
	</variable>
	<variable name="SumOutPsysRAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outPsysRAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutSysRCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outSysRCount})]]></variableExpression>
	</variable>
	<variable name="SumOutSysRAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outSysRAmount})]]></variableExpression>
	</variable>
	<variable name="SumInAccAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inAccAmount})]]></variableExpression>
	</variable>
	<variable name="SumInAccCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inAccCount})]]></variableExpression>
	</variable>
	<variable name="SumInPsysRCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inPsysRCount})]]></variableExpression>
	</variable>
	<variable name="SumInPsysRAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inPsysRAmount})]]></variableExpression>
	</variable>
	<variable name="SumInSysRCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inSysRCount})]]></variableExpression>
	</variable>
	<variable name="SumInSysRAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inSysRAmount})]]></variableExpression>
	</variable>
	<title>
		<band height="110">
			<image hAlign="Center" vAlign="Middle" isUsingCache="true">
				<reportElement x="330" y="-20" width="120" height="97"/>
				<imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="219" y="75" width="495" height="20" />
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Kuwait Automated Clearing House					]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="-20" width="210" height="50" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw

	]]></text>
			</staticText>
			<staticText>
				<reportElement x="592" y="-19" width="210" height="50" />
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[	بنك الكويت المركزي
	ص.ب. 526 صفاة 13006 الكويت
	http://www.cbk.gov.kw
]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="80">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="30" width="200" height="20" />
				<textElement verticalAlignment="Middle">
					<font fontName="DejaVu Sans" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Session Sequence: " + $P{sessionSequence}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="50" width="200" height="20" />
				<textElement verticalAlignment="Middle">
					<font fontName="DejaVu Sans" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Session Date: " + $P{sessionDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="651" y="30" width="150" height="20" />
				<textElement verticalAlignment="Middle">
					<font fontName="DejaVu Sans" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Kuwaiti Dinar / دينار كويتي"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="559" y="30" width="81" height="20" />
				<textElement verticalAlignment="Middle">
					<font fontName="DejaVu Sans" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Currency"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="142" y="-19" width="495" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" isBold="true"/>
				</textElement>
				<text><![CDATA[Net Clearing Position Details]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="84">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement mode="Opaque" x="230" y="0" width="290" height="20" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="10" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Outward صادر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="521" y="0" width="280" height="20" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="10" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Inward وارد]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="-10" y="20" width="50" height="30" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[Sequence]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="40" y="20" width="190" height="30" backcolor="#E0DEDE" />
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Bank البنك]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="20" width="98" height="30" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Presented / مرسل]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="328" y="20" width="101" height="30" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Rejected / مرفوض]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="521" y="21" width="89" height="29" backcolor="#E0DEDE" />
				<box>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Received / مستلم]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="429" y="21" width="91" height="29" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[System Rejected / مرفوض تقنيا]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="611" y="21" width="90" height="29" backcolor="#E0DEDE" />
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Rejected / مرفوض]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="701" y="20" width="100" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[System Rejected / مرفوض تقنيا]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="40" y="50" width="60" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Code / رمز
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="100" y="50" width="130" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Name / اسم
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="230" y="50" width="46" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="-10" y="50" width="50" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[التسلسل
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="276" y="50" width="52" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="328" y="50" width="46" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="374" y="50" width="54" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="429" y="50" width="39" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="468" y="50" width="52" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="521" y="50" width="37" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="651" y="50" width="50" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="610" y="50" width="41" height="34" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="558" y="50" width="52" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="741" y="50" width="60" height="34" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount / المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="701" y="50" width="40" height="34" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true" pdfFontName="DejaVu Sans" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Count / عدد
]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="29" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="-10" y="1" width="50" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SerialNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="230" y="1" width="46" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outAccCount}!=null?$F{outAccCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="276" y="1" width="52" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outAccAmount}!=null?$F{outAccAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="40" y="1" width="60" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{participantCode}!=null?$F{participantCode}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="1" width="130" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{participantName}!=null?$F{participantName}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="328" y="1" width="46" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outPsysRCount}!=null?$F{outPsysRCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="374" y="1" width="55" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outPsysRAmount}!=null?$F{outPsysRAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="429" y="1" width="39" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outSysRCount}!=null?$F{outSysRCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="468" y="1" width="52" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outSysRAmount}!=null?$F{outSysRAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="521" y="1" width="37" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inAccCount}!=null?$F{inAccCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="559" y="1" width="52" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inAccAmount}!=null?$F{inAccAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="611" y="1" width="40" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inPsysRCount}!=null?$F{inPsysRCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="651" y="1" width="50" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inPsysRAmount}!=null?$F{inPsysRAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="701" y="1" width="40" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inSysRCount}!=null?$F{inSysRCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="741" y="1" width="60" height="28" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inSysRAmount}!=null?$F{inSysRAmount}: "-"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="23" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="46" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
			<textField>
				<reportElement x="0" y="0" width="268" height="46" />
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="268" y="0" width="267" height="46" />
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMM, dd, yyyy">
				<reportElement x="535" y="0" width="267" height="46" />
				<textElement verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="40" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="231" y="0" width="45" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutAccCount}!=null?$V{SumOutAccCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="-10" y="0" width="240" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["TOTAL"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="276" y="0" width="52" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutAccAmount}!=null?$V{SumOutAccAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="328" y="0" width="45" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutPsysRCount}!=null?$V{SumOutPsysRCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="373" y="0" width="56" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutPsysRAmount}!=null?$V{SumOutPsysRAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="429" y="0" width="39" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutSysRCount}!=null?$V{SumOutSysRCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="468" y="0" width="52" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutSysRAmount}!=null?$V{SumOutSysRAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="520" y="0" width="38" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInAccCount}!=null?$V{SumInAccCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="558" y="0" width="53" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInAccAmount}!=null?$V{SumInAccAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="611" y="0" width="39" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInPsysRCount}!=null?$V{SumInPsysRCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="650" y="0" width="51" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInPsysRAmount}!=null?$V{SumInPsysRAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="701" y="0" width="40" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInSysRCount}!=null?$V{SumInSysRCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="741" y="0" width="60" height="30" backcolor="#E0DEDE" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="DejaVu Sans" size="9" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInSysRAmount}!=null?$V{SumInSysRAmount}: new Double(0)]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
