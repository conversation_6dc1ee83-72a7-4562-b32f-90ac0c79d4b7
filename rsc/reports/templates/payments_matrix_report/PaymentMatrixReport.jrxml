<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PaymentsMatrixReport" pageWidth="1741" pageHeight="842" orientation="Landscape" columnWidth="1721" leftMargin="10" rightMargin="10" topMargin="10" bottomMargin="10">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="CentralReport.jrdax"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
	<property name="net.sf.jasperreports.export.xls.remove.empty.space.between.columns" value="true"/>
	<property name="net.sf.jasperreports.export.xls.auto.fit.column" value="true"/>
	<property name="net.sf.jasperreports.export.xls.exclude.origin.band.1" value="pageHeader"/>
	<property name="net.sf.jasperreports.export.xls.exclude.origin.band.2" value="pageFooter"/>
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.unit." value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageHeight" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.pageWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.topMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.bottomMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.leftMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.rightMargin" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnWidth" value="pixel"/>
	<property name="com.jaspersoft.studio.unit.columnSpacing" value="pixel"/>
	<parameter name="sessionSequence" class="java.lang.String"/>
	<parameter name="sessionDate" class="java.lang.String"/>
	<parameter name="participantId" class="java.lang.String"/>
	<parameter name="logo" class="java.lang.String"/>
	<queryString>
		<![CDATA[WITH AggregatedData AS (
    SELECT
        tr.INSTGPARTIID AS participantID,
        COUNT(CASE WHEN tr.MSGTYPEID IN (6, 2) THEN 1 END) AS transactionCount,
        SUM(CASE WHEN tr.MSGTYPEID IN (6, 2) THEN tr.AMOUNT ELSE 0 END) AS transactionAmount,
        COUNT(CASE WHEN tr.MSGTYPEID = 3 THEN 1 END) AS returnTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 3 THEN tr.AMOUNT ELSE 0 END) AS returnTransactionAmount,
        COUNT(CASE WHEN tr.MSGTYPEID = 5 THEN 1 END) AS reversalTransactionCount,
        SUM(CASE WHEN tr.MSGTYPEID = 5 THEN tr.AMOUNT ELSE 0 END) AS reversalTransactionAmount,
        COUNT(CASE WHEN tr.STATEID != 7 THEN 1 END) AS waitingSettlementTransactionCount,
        SUM(CASE WHEN tr.STATEID != 7 THEN tr.AMOUNT ELSE 0 END) AS waitingSettlementTransactionAmount,
        0 AS batchCount,
        0 AS batchAmount,
        0 AS reversalBatchCount,
        0 AS returnBatchCount,
        0 AS returnBatchAmount,
        0 AS reversalBatchAmount,
        0 AS waitingSettlementBatchCount,
        CASE
            WHEN tr.MSGTYPEID = 6 THEN 'Credit'
            WHEN tr.MSGTYPEID = 2 THEN 'Debit'
            WHEN tr.MSGTYPEID = 3 THEN 'Return'
            WHEN tr.MSGTYPEID = 5 THEN 'Reversal'
            ELSE 'None'
        END AS type,
        CASE
            WHEN tr.INSTGPARTIID IN ($P!{participantId}) THEN 'Outward'
            WHEN tr.INSTDPARTIID IN ($P!{participantId}) THEN 'Inward'
            ELSE 'None'
        END AS direction
    FROM ATSTRANSACTIONS tr
    JOIN ATSBDI_SESSIONS ss ON ss.id = tr.SESSIONID
    WHERE ss.SESSIONSEQ = '$P!{sessionSequence}' OR TRUNC(ss.BUSINESSDT) = TO_DATE('$P!{sessionDate}', 'YYYY-MM-DD')
        AND tr.MSGTYPEID IN (6, 2, 3, 5)
        AND (tr.INSTGPARTIID IN ($P!{participantId}) OR tr.INSTDPARTIID IN ($P!{participantId}))
    GROUP BY tr.INSTGPARTIID, tr.INSTDPARTIID, tr.MSGTYPEID

    UNION ALL

    SELECT
        ab.INSTGPARTIID AS participantID,
        0 AS transactionCount,
        0 AS transactionAmount,
        0 AS returnTransactionCount,
        0 AS returnTransactionAmount,
        0 AS reversalTransactionCount,
        0 AS reversalTransactionAmount,
        0 AS waitingSettlementTransactionCount,
        0 AS waitingSettlementTransactionAmount,
        COUNT(CASE WHEN ab.MSGTYPEID IN (6, 2, 3, 5) THEN 1 END) AS batchCount,
        SUM(CASE WHEN ab.MSGTYPEID IN (6, 2) THEN ab.AMOUNT ELSE 0 END) AS batchAmount,
        COUNT(CASE WHEN ab.MSGTYPEID = 5 THEN 1 END) AS reversalBatchCount,
        COUNT(CASE WHEN ab.MSGTYPEID = 3 THEN 1 END) AS returnBatchCount,
        SUM(CASE WHEN ab.MSGTYPEID = 3 THEN ab.AMOUNT ELSE 0 END) AS returnBatchAmount,
        SUM(CASE WHEN ab.MSGTYPEID = 5 THEN ab.AMOUNT ELSE 0 END) AS reversalBatchAmount,
        COUNT(CASE WHEN ab.STATEID != 7 THEN 1 END) AS waitingSettlementBatchCount,
        CASE
            WHEN ab.MSGTYPEID = 6 THEN 'Credit'
            WHEN ab.MSGTYPEID = 2 THEN 'Debit'
            WHEN ab.MSGTYPEID = 3 THEN 'Return'
            WHEN ab.MSGTYPEID = 5 THEN 'Reversal'
            ELSE 'None'
        END AS type,
        CASE
            WHEN ab.INSTGPARTIID IN ($P!{participantId}) THEN 'Outward'
            WHEN ab.INSTDPARTIID IN ($P!{participantId}) THEN 'Inward'
            ELSE 'None'
        END AS direction
    FROM ATSBATCHES ab
    JOIN ATSBDI_SESSIONS ss ON ss.id = ab.SESSIONID
    WHERE ss.SESSIONSEQ = '$P!{sessionSequence}' OR TRUNC(ss.BUSINESSDT) = TO_DATE('$P!{sessionDate}', 'YYYY-MM-DD')
        AND ab.MSGTYPEID IN (6, 2, 3, 5)
        AND (ab.INSTGPARTIID IN ($P!{participantId}) OR ab.INSTDPARTIID IN ($P!{participantId}))
    GROUP BY ab.INSTGPARTIID, ab.INSTDPARTIID, ab.MSGTYPEID
)

SELECT
    p.CODE AS participantCode,
    p.NAME AS participantName,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Credit' THEN transactionCount ELSE 0 END), 0) AS inwardCreditTransactionCount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Credit' THEN transactionAmount ELSE 0 END), 0) AS inwardCreditTransactionAmount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Debit' THEN transactionCount ELSE 0 END), 0) AS inwardDebitTransactionCount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Debit' THEN transactionAmount ELSE 0 END), 0) AS inwardDebitTransactionAmount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Credit' THEN transactionCount ELSE 0 END), 0) AS outwardCreditTransactionCount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Credit' THEN transactionAmount ELSE 0 END), 0) AS outwardCreditTransactionAmount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Debit' THEN transactionCount ELSE 0 END), 0) AS outwardDebitTransactionCount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Debit' THEN transactionAmount ELSE 0 END), 0) AS outwardDebitTransactionAmount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Credit' THEN batchCount ELSE 0 END), 0) AS inwardCreditBatchCount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Credit' THEN batchCount ELSE 0 END), 0) AS outwardCreditBatchCount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Debit' THEN batchCount ELSE 0 END), 0) AS inwardDebitBatchCount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Debit' THEN batchCount ELSE 0 END), 0) AS outwardDebitBatchCount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Credit' THEN batchAmount ELSE 0 END), 0) AS inwardCreditBatchAmount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Credit' THEN batchAmount ELSE 0 END), 0) AS outwardCreditBatchAmount,
    COALESCE(SUM(CASE WHEN direction = 'Inward' AND type = 'Debit' THEN batchAmount ELSE 0 END), 0) AS inwardDebitBatchAmount,
    COALESCE(SUM(CASE WHEN direction = 'Outward' AND type = 'Debit' THEN batchAmount ELSE 0 END), 0) AS outwardDebitBatchAmount,
    COALESCE(SUM(CASE WHEN type = 'Return' THEN returnBatchCount ELSE 0 END), 0) AS returnBatchCount,
    COALESCE(SUM(CASE WHEN type = 'Reversal' THEN reversalBatchCount ELSE 0 END), 0) AS reversalBatchCount,
    COALESCE(SUM(CASE WHEN type = 'Return' THEN returnTransactionCount ELSE 0 END), 0) AS returnTransactionCount,
    COALESCE(SUM(CASE WHEN type = 'Return' THEN returnTransactionAmount ELSE 0 END), 0) AS returnTransactionAmount,
    COALESCE(SUM(CASE WHEN type = 'Reversal' THEN reversalTransactionCount ELSE 0 END), 0) AS reversalTransactionCount,
    COALESCE(SUM(CASE WHEN type = 'Reversal' THEN reversalTransactionAmount ELSE 0 END), 0) AS reversalTransactionAmount,
    COALESCE(SUM(waitingSettlementBatchCount), 0) AS waitingSettlementBatchCount,
    COALESCE(SUM(waitingSettlementTransactionCount), 0) AS waitingSettlementTransactionCount,
    COALESCE(SUM(waitingSettlementTransactionAmount), 0) AS waitingSettlementTransactionAmount
FROM ATSPRT_Participants p
LEFT JOIN AggregatedData subquery ON p.ID = subquery.participantID
WHERE p.ID IN ($P!{participantId})
GROUP BY p.CODE, p.NAME]]>
	</queryString>
	<field name="participantCode" class="java.lang.String"/>
	<field name="participantName" class="java.lang.String"/>
	<field name="inwardCreditTransactionCount" class="java.lang.String"/>
	<field name="inwardCreditTransactionAmount" class="java.lang.String"/>
	<field name="inwardDebitTransactionCount" class="java.lang.String"/>
	<field name="inwardDebitTransactionAmount" class="java.lang.String"/>
	<field name="outwardCreditTransactionCount" class="java.lang.String"/>
	<field name="outwardCreditTransactionAmount" class="java.lang.String"/>
	<field name="outwardDebitTransactionCount" class="java.lang.String"/>
	<field name="outwardDebitTransactionAmount" class="java.lang.String"/>
	<field name="inwardCreditBatchCount" class="java.lang.String"/>
	<field name="outwardCreditBatchCount" class="java.lang.String"/>
	<field name="inwardDebitBatchCount" class="java.lang.String"/>
	<field name="outwardDebitBatchCount" class="java.lang.String"/>
	<field name="inwardCreditBatchAmount" class="java.lang.String"/>
	<field name="outwardCreditBatchAmount" class="java.lang.String"/>
	<field name="inwardDebitBatchAmount" class="java.lang.String"/>
	<field name="outwardDebitBatchAmount" class="java.lang.String"/>
	<field name="returnTransactionCount" class="java.lang.String"/>
	<field name="returnTransactionAmount" class="java.lang.String"/>
	<field name="reversalTransactionCount" class="java.lang.String"/>
	<field name="reversalTransactionAmount" class="java.lang.String"/>
	<field name="reversalBatchCount" class="java.lang.String"/>
	<field name="returnBatchCount" class="java.lang.String"/>
	<field name="waitingSettlementBatchCount" class="java.lang.String"/>
	<field name="waitingSettlementTransactionCount" class="java.lang.String"/>
	<field name="waitingSettlementTransactionAmount" class="java.lang.String"/>
	<variable name="SerialNumber" class="java.lang.Integer">
		<variableExpression><![CDATA[$V{SerialNumber} + 1]]></variableExpression>
		<initialValueExpression><![CDATA[0]]></initialValueExpression>
	</variable>
	<variable name="SumInwardCreditTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardCreditTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumInwardCreditTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardCreditTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumInwardDebitTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardDebitTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumInwardDebitTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardDebitTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardCreditTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardCreditTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardCreditTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardCreditTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardDebitTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardDebitTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardDebitTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardDebitTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumInwardCreditBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardCreditBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardCreditBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardCreditBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumInwardDebitBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardDebitBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardDebitBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardDebitBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumInwardCreditBatchAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardCreditBatchAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardCreditBatchAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardCreditBatchAmount})]]></variableExpression>
	</variable>
	<variable name="SumInwardDebitBatchAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{inwardDebitBatchAmount})]]></variableExpression>
	</variable>
	<variable name="SumOutwardDebitBatchAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{outwardDebitBatchAmount})]]></variableExpression>
	</variable>
	<variable name="SumReturnTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{returnTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumReturnTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{returnTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumReversalTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumReversalTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{reversalTransactionAmount})]]></variableExpression>
	</variable>
	<variable name="SumReversalBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{reversalBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumReturnBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{returnBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumWaitingSettlementBatchCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{waitingSettlementBatchCount})]]></variableExpression>
	</variable>
	<variable name="SumWaitingSettlementTransactionCount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{waitingSettlementTransactionCount})]]></variableExpression>
	</variable>
	<variable name="SumWaitingSettlementTransactionAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{waitingSettlementTransactionAmount})]]></variableExpression>
	</variable>
	<title>
		<band height="150">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<image hAlign="Center" vAlign="Middle" isUsingCache="true">
				<reportElement x="800" y="2" width="120" height="97"/>
				<imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="613" y="100" width="495" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Kuwait Automated Clearing House					]]></text>
			</staticText>
			<staticText>
				<reportElement x="0" y="0" width="210" height="50"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true"/>
				</textElement>
				<text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw

	]]></text>
			</staticText>
			<staticText>
				<reportElement x="1490" y="3" width="230" height="50"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[	بنك الكويت المركزي
	ص.ب. 526 صفاة 13006 الكويت
	http://www.cbk.gov.kw
]]></text>
			</staticText>
		</band>
	</title>
	<pageHeader>
		<band height="70">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField>
				<reportElement x="0" y="30" width="200" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Session Sequence: " + ($P{sessionSequence} == null ? "-" : $P{sessionSequence})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="50" width="200" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Session Date: " + ($P{sessionDate} == null ? "-" : $P{sessionDate})]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1569" y="30" width="150" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Kuwaiti Dinar / دينار كويتي"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1477" y="30" width="81" height="20"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["Currency"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="613" y="-28" width="495" height="20"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" isBold="true"/>
				</textElement>
				<text><![CDATA[Payments Matrix Report]]></text>
			</staticText>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="130">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement mode="Opaque" x="274" y="17" width="390" height="43" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Outward صادر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="664" y="17" width="426" height="43" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Inward وارد]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1" y="60" width="46" height="30" backcolor="#E0DEDE"/>
				<box>
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<text><![CDATA[Sequence]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="48" y="60" width="226" height="30" backcolor="#E0DEDE"/>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Bank البنك]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="274" y="61" width="203" height="28" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.x" value="pixel"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Direct Credit / الإيداع المباشر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="477" y="60" width="188" height="30" backcolor="#E0DEDE"/>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Direct Debit / الخصم المباشر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="874" y="60" width="216" height="29" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Direct Debit / الخصم المباشر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="48" y="90" width="82" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Code / رمز
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="131" y="90" width="142" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Name / الاسم
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="274" y="90" width="64" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1" y="90" width="46" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="6.5" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[التسلسل
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="339" y="90" width="60" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="400" y="90" width="77" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="665" y="60" width="208" height="29" backcolor="#E0DEDE"/>
				<box>
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Direct Credit / الإيداع المباشر]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1090" y="59" width="210" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Returned مرتجع]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1301" y="60" width="208" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Reversed مُعاد]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1509" y="60" width="212" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Waiting Settlement في انتظار التسوية]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1090" y="16" width="210" height="43" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Returned مرتجع]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1301" y="17" width="208" height="43" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Reversed مُعاد]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1509" y="17" width="212" height="43" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Waiting Settlement في انتظار التسوية]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="477" y="90" width="62" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="539" y="90" width="60" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="599" y="90" width="66" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="665" y="90" width="64" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="730" y="90" width="64" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="794" y="90" width="80" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="874" y="90" width="64" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="939" y="90" width="60" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="999" y="90" width="90" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1090" y="90" width="61" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1152" y="90" width="58" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1211" y="90" width="90" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1301" y="90" width="60" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1362" y="90" width="60" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1423" y="90" width="86" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1509" y="90" width="66" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Batch Count  عدد الدُفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1576" y="90" width="64" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[TXN Count عدد المعاملات
]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="1641" y="90" width="80" height="40" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
					<pen lineWidth="1.5"/>
					<topPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<leftPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<bottomPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
					<rightPen lineWidth="1.5" lineStyle="Solid" lineColor="#000000"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true" pdfFontName="Arial" pdfEncoding="Identity-H" isPdfEmbedded="true"/>
				</textElement>
				<text><![CDATA[Amount  المبلغ
]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="44" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<property name="com.jaspersoft.studio.layout"/>
			<textField>
				<reportElement x="1" y="1" width="46" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box topPadding="0" leftPadding="0" bottomPadding="0" rightPadding="0">
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SerialNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="274" y="1" width="64" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardCreditBatchCount}!=null?$F{outwardCreditBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="477" y="1" width="62" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardDebitBatchCount}!=null?$F{outwardDebitBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="48" y="1" width="82" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{participantCode}!=null?$F{participantCode}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="130" y="1" width="144" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box padding="0">
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{participantName}!=null?$F{participantName}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="599" y="1" width="66" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardDebitTransactionAmount}!=null?$F{outwardDebitTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="729" y="1" width="66" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardCreditTransactionCount}!=null?$F{inwardCreditTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="874" y="1" width="64" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardDebitBatchCount}!=null?$F{inwardDebitBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="999" y="1" width="90" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardDebitTransactionAmount}!=null?$F{inwardDebitTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="939" y="1" width="60" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardDebitTransactionCount}!=null?$F{inwardDebitTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1152" y="1" width="58" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{returnTransactionCount}!=null?$F{returnTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1211" y="1" width="90" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{returnTransactionAmount}!=null?$F{returnTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1301" y="1" width="60" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{reversalBatchCount}!=null?$F{reversalBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1509" y="1" width="67" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{waitingSettlementBatchCount}!=null?$F{waitingSettlementBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1576" y="1" width="64" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{waitingSettlementTransactionCount}!=null?$F{waitingSettlementTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="339" y="1" width="60" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardCreditTransactionCount}!=null?$F{outwardCreditTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="1" width="77" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardCreditTransactionAmount}!=null?$F{outwardCreditTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="539" y="1" width="60" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{outwardDebitTransactionCount}!=null?$F{outwardDebitTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="665" y="1" width="64" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardCreditBatchCount}!=null?$F{inwardCreditBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="795" y="1" width="78" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{inwardCreditTransactionAmount}!=null?$F{inwardCreditTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1089" y="1" width="62" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{returnBatchCount}!=null?$F{returnBatchCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1362" y="1" width="60" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{reversalTransactionCount}!=null?$F{reversalTransactionCount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1423" y="1" width="86" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{reversalTransactionAmount}!=null?$F{reversalTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="1640" y="1" width="80" height="43">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{waitingSettlementTransactionAmount}!=null?$F{waitingSettlementTransactionAmount}: "-"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="23" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
		</band>
	</columnFooter>
	<pageFooter>
		<band height="46" splitType="Stretch">
			<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.HorizontalRowLayout"/>
			<textField>
				<reportElement x="0" y="0" width="575" height="46"/>
				<textElement textAlignment="Right" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="575" y="0" width="573" height="46"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMM, dd, yyyy">
				<reportElement x="1148" y="0" width="573" height="46"/>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="40" splitType="Stretch">
			<textField>
				<reportElement mode="Opaque" x="0" y="0" width="274" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA["TOTAL"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1152" y="0" width="58" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReturnTransactionCount}!=null?$V{SumReturnTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1210" y="0" width="90" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReturnTransactionAmount}!=null?$V{SumReturnTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1361" y="0" width="61" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReversalTransactionCount}!=null?$V{SumReversalTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1422" y="0" width="86" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReversalTransactionAmount}!=null?$V{SumReversalTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1300" y="0" width="60" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReversalBatchCount}!=null?$V{SumReversalBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1087" y="0" width="64" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumReturnBatchCount}!=null?$V{SumReturnBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="938" y="0" width="61" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardDebitTransactionCount}!=null?$V{SumInwardDebitTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="999" y="0" width="88" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardDebitTransactionAmount}!=null?$V{SumInwardDebitTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="874" y="0" width="64" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardDebitBatchCount}!=null?$V{SumInwardDebitBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="729" y="0" width="66" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardCreditTransactionCount}!=null?$V{SumInwardCreditTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="795" y="0" width="78" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardCreditTransactionAmount}!=null?$V{SumInwardCreditTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="665" y="0" width="64" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumInwardCreditBatchCount}!=null?$V{SumInwardCreditBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="539" y="0" width="60" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardDebitTransactionCount}!=null?$V{SumOutwardDebitTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="599" y="0" width="66" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardDebitTransactionAmount}!=null?$V{SumOutwardDebitTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="477" y="0" width="62" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardDebitBatchCount}!=null?$V{SumOutwardDebitBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="339" y="0" width="60" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardCreditTransactionCount}!=null?$V{SumOutwardCreditTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="399" y="0" width="78" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardCreditTransactionAmount}!=null?$V{SumOutwardCreditTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="274" y="0" width="64" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumOutwardCreditBatchCount}!=null?$V{SumOutwardCreditBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1576" y="0" width="64" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumWaitingSettlementTransactionCount}!=null?$V{SumWaitingSettlementTransactionCount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1641" y="0" width="78" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumWaitingSettlementTransactionAmount}!=null?$V{SumWaitingSettlementTransactionAmount}: new Double(0)]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement mode="Opaque" x="1509" y="0" width="67" height="30" backcolor="#E0DEDE">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="8" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$V{SumWaitingSettlementBatchCount}!=null?$V{SumWaitingSettlementBatchCount}: new Double(0)]]></textFieldExpression>
			</textField>
		</band>
	</summary>
</jasperReport>
