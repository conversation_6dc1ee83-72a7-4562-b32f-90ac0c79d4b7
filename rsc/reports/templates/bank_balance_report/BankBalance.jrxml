<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="CompensattionRecap" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="1.jrdax"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="712"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="277"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="461"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="528"/>
	<parameter name="logo" class="java.lang.String"/>
	<parameter name="sesSeq" class="java.lang.String"/>
	<parameter name="participantCode" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT
    s.SESSIONSEQ AS "Sequence",
    p.CODE AS "BankCode",
    p.NAME AS "BankName",
    b.STARTBALANCE AS "BeginningBalance",
    COALESCE(bat.batch_count, 0) AS "TotalBatches",
    COALESCE(tran.transaction_count, 0) AS "TotalTransactions",
    b.BALANCE AS "CurrentBalance",
    (SELECT COUNT(*) FROM ATSBATCHES bat_total WHERE bat_total.SESSIONID = s.ID) AS "batchTotalCount",
    (SELECT COUNT(*) FROM ATSTRANSACTIONS tran_total WHERE tran_total.SESSIONID = s.ID) AS "transactionTotalCount"
FROM
    ATSBDI_SESSIONS s
JOIN
    ATSACC_BALANCES b ON s.ID = b.REFSESSIONID
JOIN
    ATSPRT_PARTICIPANTS p ON b.REFACCOUNTID = p.ID
LEFT JOIN (
    SELECT
        bat.SESSIONID,
        bat.INSTDPARTIID,
        COUNT(*) AS batch_count
    FROM
        ATSBATCHES bat
    GROUP BY
        bat.SESSIONID, bat.INSTDPARTIID
) bat ON s.ID = bat.SESSIONID AND p.ID = bat.INSTDPARTIID
LEFT JOIN (
    SELECT
        tran.SESSIONID,
        tran.INSTDPARTIID,
        COUNT(*) AS transaction_count
    FROM
        ATSTRANSACTIONS tran
    GROUP BY
        tran.SESSIONID, tran.INSTDPARTIID
) tran ON s.ID = tran.SESSIONID AND p.ID = tran.INSTDPARTIID
WHERE
    (s.SESSIONSEQ = $P{sesSeq} OR $P{sesSeq} IS NULL)
    AND ((p.CODE = $P{participantCode} OR $P{participantCode} IS NULL)
         OR p.CODE IN (
             SELECT REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL)
             FROM DUAL
             CONNECT BY REGEXP_SUBSTR($P{participantCode}, '[^,]+', 1, LEVEL) IS NOT NULL
         ))]]>
	</queryString>
	<field name="sequence" class="java.lang.String"/>
	<field name="bankCode" class="java.lang.String"/>
	<field name="bankName" class="java.lang.String"/>
	<field name="beginningBalance" class="java.lang.String"/>
	<field name="totalBatches" class="java.lang.String"/>
	<field name="totalTransactions" class="java.lang.String"/>
	<field name="currentBalance" class="java.lang.String"/>
	<field name="batchTotalCount" class="java.lang.String"/>
	<field name="transactionTotalCount" class="java.lang.String"/>
	<variable name="SumCrAmount" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{beginningBalance})]]></variableExpression>
	</variable>
	<variable name="SumDbBal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{totalBatches})]]></variableExpression>
	</variable>
	<variable name="SumCrBal" class="java.lang.Double" calculation="Sum">
		<variableExpression><![CDATA[Double.parseDouble($F{totalTransactions})]]></variableExpression>
	</variable>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="285" splitType="Stretch">
			<staticText>
				<reportElement x="250" y="125" width="303" height="25" forecolor="#0A0A0A"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<text><![CDATA[Kuwait Automated Clearing House]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="10" y="190" width="550" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Filters]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="10" y="220" width="120" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Session Sequence]]></text>
			</staticText>
			<textField>
				<reportElement x="140" y="220" width="170" height="30" >
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sesSeq} != null ? $P{sesSeq}  :  "-"]]></textFieldExpression>
			</textField>
			<image hAlign="Center" vAlign="Middle" isUsingCache="true">
				<reportElement x="330" y="5" width="140" height="115"/>
				<imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
			</image>
			<staticText>
				<reportElement mode="Transparent" x="15" y="250" width="158" height="5" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Session Sequence]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="10" y="247" width="120" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Banks]]></text>
			</staticText>
			<textField>
				<reportElement x="140" y="250" width="170" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{participantCode} != null ? $P{participantCode}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="250" y="160" width="303" height="30" forecolor="#0A0A0A"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<text><![CDATA[Bank Balance  Report]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="480" y="230" width="70" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Currency]]></text>
			</staticText>
			<textField>
				<reportElement x="560" y="230" width="150" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Kuwaiti Dinar / دينار كويتي"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="730" y="230" width="70" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[العملة]]></text>
			</staticText>
			<staticText>
				<reportElement x="-10" y="-20" width="210" height="70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw                                  ]]></text>
			</staticText>
			<staticText>
				<reportElement x="670" y="-20" width="142" height="70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<text><![CDATA[بنك الكويت المركزي
ص.ب. 526 صفاة 13006 الكويت
http://www.cbk.gov.kw]]></text>
			</staticText>
		</band>
	</title>
	<columnHeader>
		<band height="85" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="430" y="10" width="100" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Batches]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="530" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Transactions]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="660" y="10" width="120" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Current Balance]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="10" width="90" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Sequence]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="10" width="210" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bank / البنك]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="300" y="50" width="130" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[الرصيد الافتتاحي]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="300" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Start Balance]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="0" y="50" width="90" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[التسلسل]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="90" y="50" width="100" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Code / رمز]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="190" y="50" width="110" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Name / اسم]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="430" y="50" width="100" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[إجمالي الدفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="530" y="50" width="130" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[إجمالي المعاملات ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="660" y="50" width="120" height="35" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[الرصيد الحالي]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="52" splitType="Stretch">
			<textField>
				<reportElement x="90" y="0" width="100" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankCode}!=null?$F{bankCode}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="190" y="0" width="110" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{bankName}!=null?$F{bankName}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="300" y="0" width="130" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{beginningBalance}!=null?$F{beginningBalance}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="530" y="0" width="130" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{totalTransactions} == null) ? new BigDecimal(0) : $F{totalTransactions}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="430" y="0" width="100" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{totalBatches} == null) ? new BigDecimal(0) : $F{totalBatches}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="660" y="0" width="120" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[($F{totalTransactions} == null) ? new BigDecimal(0) : $F{totalTransactions}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="0" y="0" width="90" height="51">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sequence}!=null?$F{sequence}: "-"]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="40" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="80" splitType="Stretch">
			<textField>
				<reportElement x="640" y="16" width="100" height="30"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="740" y="16" width="100" height="30"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMM, dd, yyyy">
				<reportElement x="0" y="20" width="150" height="26"/>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="100" splitType="Stretch">
			<textField>
				<reportElement x="408" y="70" width="162" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{batchTotalCount} != null ?  $F{batchTotalCount}  :  "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="570" y="70" width="180" height="30">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionTotalCount} != null ? $F{transactionTotalCount}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="408" y="10" width="162" height="30" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Batch Count]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="570" y="10" width="180" height="30" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Transaction Count]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="408" y="40" width="162" height="30" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[إجمالي عدد الدفعات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="570" y="40" width="180" height="30" forecolor="#FFFFFF" backcolor="#515658">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[إجمالي عدد المعاملات ]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
