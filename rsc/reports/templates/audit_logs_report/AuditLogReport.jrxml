<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.6.final using JasperReports Library version 6.20.6-5c96b6aa8a39ac1dc6b6bea4b81168e16dd39231  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="UserAuditReport" pageWidth="870" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="57be2f48-22f9-4717-9672-7373b9b3a062">
    <property name="com.jaspersoft.studio.data.sql.tables" value=""/>
    <property name="net.sf.jasperreports.export.xls.remove.empty.space.between.rows" value="true"/>
    <property name="net.sf.jasperreports.export.xls.ignore.cell.border" value="false"/>
    <property name="net.sf.jasperreports.export.xls.collapse.row.span" value="false"/>
    <property name="net.sf.jasperreports.export.xls.font.size.fix.enabled" value="true"/>
    <property name="com.jaspersoft.studio.data.defaultdataadapter" value="ach"/>
    <property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="712"/>
    <property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="277"/>
    <property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="461"/>
    <property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="528"/>
    <property name="com.jaspersoft.studio.report.description" value="UserAuditReport"/>
    <style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table 1_TH" mode="Opaque" backcolor="#F0F8FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table 1_CH" mode="Opaque" backcolor="#BFE1FF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <style name="Table 1_TD" mode="Opaque" backcolor="#FFFFFF">
        <box>
            <pen lineWidth="0.5" lineColor="#000000"/>
            <topPen lineWidth="0.5" lineColor="#000000"/>
            <leftPen lineWidth="0.5" lineColor="#000000"/>
            <bottomPen lineWidth="0.5" lineColor="#000000"/>
            <rightPen lineWidth="0.5" lineColor="#000000"/>
        </box>
    </style>
    <subDataset name="dataset1" uuid="51084137-2b52-4e94-8adf-ab4573ce5322">
        <parameter name="pusername" class="java.lang.String"/>
        <parameter name="pdatefrm" class="java.lang.String"/>
        <parameter name="pdateto" class="java.lang.String"/>
        <parameter name="includeSrvOperations" class="java.lang.String"/>
        <parameter name="recordsIds" class="java.lang.String"/>
        <parameter name="poperationType" class="java.lang.String"/>
        <queryString>
            <![CDATA[SELECT ACH_AUDIT_LOG_VIEW.ID as "CHANGE_GROUP_ID", ACH_AUDIT_LOG_VIEW.Z_CREATED_BY  AS "username", ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE  AS "creationDate", ACH_AUDIT_LOG_VIEW.ACTIONNAME  AS "actionName", ACH_AUDIT_LOG_VIEW.WORKSTATION  AS "workstation", ACH_AUDIT_LOG_VIEW.VIEWNAME  as "viewName", ACH_AUDIT_LOG_VIEW.CHANGE_GROUP_TYPE AS "CHANGE_GROUP_TYPE", ACH_AUDIT_LOG_VIEW.ENTITYID, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_ON, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_QUEUED, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_STATUS, ACH_AUDIT_LOG_VIEW.Z_ASSIGNED_GROUP, ACH_AUDIT_LOG_VIEW.Z_ASSIGNED_USER, ACH_AUDIT_LOG_VIEW.Z_DELETED_BY, ACH_AUDIT_LOG_VIEW.Z_DELETED_FLAG, ACH_AUDIT_LOG_VIEW.Z_DELETED_ON, ACH_AUDIT_LOG_VIEW.Z_EDITABLE, ACH_AUDIT_LOG_VIEW.Z_LOCKED_BY, ACH_AUDIT_LOG_VIEW.Z_LOCKED_UNTIL, ACH_AUDIT_LOG_VIEW.Z_ORG_ID, ACH_AUDIT_LOG_VIEW.Z_TENANT_ID, ACH_AUDIT_LOG_VIEW.Z_UPDATED_BY, ACH_AUDIT_LOG_VIEW.Z_UPDATING_DATE, ACH_AUDIT_LOG_VIEW.Z_WORKFLOW_ID, ACH_AUDIT_LOG_VIEW.Z_WS_TOKEN, ACH_AUDIT_LOG_VIEW.Z_DRAFT_STATUS, ACH_AUDIT_LOG_VIEW.Z_DRAFT_ID, ACH_AUDIT_LOG_VIEW.Z_STATUS_ID, ACH_AUDIT_LOG_VIEW.INCLUDE_SRV_OPERATIONS FROM ACH_AUDIT_LOG_VIEW where  ($P{recordsIds} is null OR ACH_AUDIT_LOG_VIEW.ID in (Select Regexp_Substr($P{recordsIds} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{recordsIds},'[^,]+' ,1 ,Level) Is Not Null) )
            AND ($P{pusername} IS NULL OR ACH_AUDIT_LOG_VIEW.Z_CREATED_BY = 'ATSMAKER@ATS')
            AND ($P{includeSrvOperations} is null OR ACH_AUDIT_LOG_VIEW.INCLUDE_SRV_OPERATIONS in (Select Regexp_Substr($P{includeSrvOperations} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{includeSrvOperations},'[^,]+' ,1 ,Level) Is Not Null) )
             AND ($P{poperationType} is null OR ACH_AUDIT_LOG_VIEW.OPERATION_TYPE in (Select Regexp_Substr($P{poperationType} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{poperationType},'[^,]+' ,1 ,Level) Is Not Null) )
            AND ($P{pdatefrm} IS NULL OR (ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE >= TO_DATE($P{pdatefrm}, 'YYYY-MM-DD"T"HH24:MI')))
            AND ($P{pdateto} IS NULL OR (ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE <= TO_DATE($P{pdateto}, 'YYYY-MM-DD"T"HH24:MI')))]]>
        </queryString>
        <field name="CHANGE_GROUP_ID" class="java.math.BigDecimal"/>
        <field name="username" class="java.lang.String"/>
        <field name="creationDate" class="java.sql.Date"/>
        <field name="workstation" class="java.lang.String"/>
        <field name="actionName" class="java.lang.String"/>
        <field name="viewName" class="java.lang.String"/>
        <field name="CHANGE_GROUP_TYPE" class="java.lang.String"/>
    </subDataset>
    <subDataset name="fieldsChangesDataset" uuid="fb7b6d67-01b8-40f0-9bba-8603d67aab9f">
        <property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
        <parameter name="pchange_group_id" class="java.lang.String"/>
        <parameter name="pchange_group_type" class="java.lang.String"/>
        <queryString>
            <![CDATA[select id, FIELD_NAME, OLD_VALUE,NEW_VALUE,CHANGE_GROUP_ID from JPTR_CHNG_HISTRY_ITEM
where CHANGE_GROUP_ID = $P{pchange_group_id} AND $P{pchange_group_type}='CHANGE_HISTORY']]>
        </queryString>
        <field name="ID" class="java.lang.String"/>
        <field name="FIELD_NAME" class="java.lang.String"/>
        <field name="OLD_VALUE" class="java.lang.String"/>
        <field name="NEW_VALUE" class="java.lang.String"/>
        <field name="CHANGE_GROUP_ID" class="java.math.BigDecimal"/>
    </subDataset>
    <parameter name="logo" class="java.lang.String"/>
    <parameter name="pusername" class="java.lang.String"/>
    <parameter name="pdatefrm" class="java.lang.String"/>
    <parameter name="pdateto" class="java.lang.String"/>
    <parameter name="includeSrvOperations" class="java.lang.String"/>
    <parameter name="recordsIds" class="java.lang.String"/>
    <parameter name="poperationType" class="java.lang.String"/>
    <queryString>
        <![CDATA[SELECT ACH_AUDIT_LOG_VIEW.ID as "CHANGE_GROUP_ID", ACH_AUDIT_LOG_VIEW.Z_CREATED_BY  AS "username", ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE  AS "creationDate", ACH_AUDIT_LOG_VIEW.ACTIONNAME  AS "actionName", ACH_AUDIT_LOG_VIEW.WORKSTATION  AS "workstation", ACH_AUDIT_LOG_VIEW.VIEWNAME  as "viewName", ACH_AUDIT_LOG_VIEW.CHANGE_GROUP_TYPE AS "CHANGE_GROUP_TYPE", ACH_AUDIT_LOG_VIEW.ENTITYID, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_ON, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_QUEUED, ACH_AUDIT_LOG_VIEW.Z_ARCHIVE_STATUS, ACH_AUDIT_LOG_VIEW.Z_ASSIGNED_GROUP, ACH_AUDIT_LOG_VIEW.Z_ASSIGNED_USER, ACH_AUDIT_LOG_VIEW.Z_DELETED_BY, ACH_AUDIT_LOG_VIEW.Z_DELETED_FLAG, ACH_AUDIT_LOG_VIEW.Z_DELETED_ON, ACH_AUDIT_LOG_VIEW.Z_EDITABLE, ACH_AUDIT_LOG_VIEW.Z_LOCKED_BY, ACH_AUDIT_LOG_VIEW.Z_LOCKED_UNTIL, ACH_AUDIT_LOG_VIEW.Z_ORG_ID, ACH_AUDIT_LOG_VIEW.Z_TENANT_ID, ACH_AUDIT_LOG_VIEW.Z_UPDATED_BY, ACH_AUDIT_LOG_VIEW.Z_UPDATING_DATE, ACH_AUDIT_LOG_VIEW.Z_WORKFLOW_ID, ACH_AUDIT_LOG_VIEW.Z_WS_TOKEN, ACH_AUDIT_LOG_VIEW.Z_DRAFT_STATUS, ACH_AUDIT_LOG_VIEW.Z_DRAFT_ID, ACH_AUDIT_LOG_VIEW.Z_STATUS_ID, ACH_AUDIT_LOG_VIEW.INCLUDE_SRV_OPERATIONS FROM ACH_AUDIT_LOG_VIEW where  ($P{recordsIds} is null OR ACH_AUDIT_LOG_VIEW.ID in (Select Regexp_Substr($P{recordsIds} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{recordsIds},'[^,]+' ,1 ,Level) Is Not Null) )
            AND ($P{pusername} IS NULL OR ACH_AUDIT_LOG_VIEW.Z_CREATED_BY = 'ATSMAKER@ATS')
            AND ($P{includeSrvOperations} is null OR ACH_AUDIT_LOG_VIEW.INCLUDE_SRV_OPERATIONS in (Select Regexp_Substr($P{includeSrvOperations} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{includeSrvOperations},'[^,]+' ,1 ,Level) Is Not Null) )
             AND ($P{poperationType} is null OR ACH_AUDIT_LOG_VIEW.OPERATION_TYPE in (Select Regexp_Substr($P{poperationType} ,'[^,]+' ,1 ,Level) ID From Dual Connect By Regexp_Substr($P{poperationType},'[^,]+' ,1 ,Level) Is Not Null) )
            AND ($P{pdatefrm} IS NULL OR (ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE >= TO_DATE($P{pdatefrm}, 'YYYY-MM-DD"T"HH24:MI')))
            AND ($P{pdateto} IS NULL OR (ACH_AUDIT_LOG_VIEW.Z_CREATION_DATE <= TO_DATE($P{pdateto}, 'YYYY-MM-DD"T"HH24:MI')))]]>
    </queryString>
    <background>
        <band splitType="Stretch"/>
    </background>
    <title>
        <band height="278" splitType="Stretch">
            <staticText>
                <reportElement x="260" y="110" width="280" height="30" forecolor="#0A0A0A" uuid="85b1f43c-d99e-4145-80a6-082a8673e12d"/>
                <textElement textAlignment="Center">
                    <font fontName="Arial" size="20" isBold="true"/>
                </textElement>
                <text><![CDATA[Kuwait Automated Clearing House]]></text>
            </staticText>
            <staticText>
                <reportElement mode="Transparent" x="0" y="160" width="60" height="30" forecolor="#030000" backcolor="#0702F7" uuid="fac7f451-7d41-4f72-92d6-4176dc23c976">
                    <property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
                </reportElement>
                <box>
                    <pen lineWidth="0.0" lineColor="#030000"/>
                </box>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="14" isBold="true" isUnderline="true"/>
                </textElement>
                <text><![CDATA[Filters]]></text>
            </staticText>
            <image hAlign="Center" vAlign="Middle" isUsingCache="true">
                <reportElement x="330" y="-10" width="140" height="115" uuid="c2b3fee9-f463-458c-80c7-1411f81e835e"/>
                <imageExpression><![CDATA[$P{logo} != null && !"".equals($P{logo}) ? net.sf.jasperreports.util.Base64Util.decode($P{logo}) : null]]></imageExpression>
            </image>
            <staticText>
                <reportElement x="280" y="140" width="240" height="30" forecolor="#0A0A0A" uuid="923f1a2f-c73f-4cb2-9c33-3fcdd822c599"/>
                <textElement textAlignment="Center">
                    <font fontName="Arial" size="20" isBold="true"/>
                </textElement>
                <text><![CDATA[User Audit Report]]></text>
            </staticText>
            <staticText>
                <reportElement x="-10" y="-20" width="210" height="70" uuid="93d7ce11-1389-4a58-ab0c-b97d933b83cb">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="11"/>
                </textElement>
                <text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw                                  ]]></text>
            </staticText>
            <staticText>
                <reportElement x="670" y="-20" width="142" height="70" uuid="8dfe65d9-4269-4b6f-9f54-a32b4d7ac8f1">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle">
                    <font fontName="Arial" size="11"/>
                </textElement>
                <text><![CDATA[بنك الكويت المركزي
ص.ب. 526 صفاة 13006 الكويت
http://www.cbk.gov.kw]]></text>
            </staticText>
            <staticText>
                <reportElement x="0" y="200" width="80" height="30" uuid="c7d782c0-aa16-427d-ba0f-dcc67bf6a7c7"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[User Name]]></text>
            </staticText>
            <textField>
                <reportElement x="82" y="200" width="162" height="30" uuid="efebda88-9ee4-40ad-9e8e-06ea349f5ee5"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[($P{pusername}== null || "".equals($P{pusername}.trim())) ? " - "
: $P{pusername}]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="324" y="200" width="185" height="30" uuid="75585ba3-f4bc-4a28-b0ca-db3e10fe4526">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[$P{pdatefrm} == null  ?
 " - " : $P{pdatefrm}.replace('T',' ')]]></textFieldExpression>
            </textField>
            <textField>
                <reportElement x="597" y="200" width="190" height="30" uuid="56418930-9fd2-4e99-8da5-7c9d53a1e94b">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[$P{pdateto} == null  ?
 " - " : $P{pdateto}.replace('T',' ')]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="253" y="200" width="64" height="30" uuid="2a52de7b-f9db-4a11-81c3-a30878d44dbe">
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[Date From]]></text>
            </staticText>
            <staticText>
                <reportElement x="526" y="200" width="64" height="30" uuid="969f9867-ef26-4d4b-873b-c2624a8a27b3">
                    <property name="com.jaspersoft.studio.unit.x" value="pixel"/>
                    <property name="com.jaspersoft.studio.unit.width" value="px"/>
                </reportElement>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[Date To]]></text>
            </staticText>
            <staticText>
                <reportElement x="2" y="240" width="148" height="30" uuid="910124a0-02ed-4d28-b1d7-ce03089b36c0"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[INclude User Operations]]></text>
            </staticText>
            <textField>
                <reportElement x="150" y="240" width="94" height="30" uuid="5b35bd08-5373-424d-8f22-b08cda823c71"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[($P{includeSrvOperations}== null || "".equals($P{includeSrvOperations}.trim())) ? " - "
: $P{includeSrvOperations}]]></textFieldExpression>
            </textField>
            <staticText>
                <reportElement x="253" y="240" width="94" height="30" uuid="43bd33ac-269a-4b0a-b67f-265ff30c1014"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <text><![CDATA[Operation Type]]></text>
            </staticText>
            <textField>
                <reportElement x="353" y="240" width="447" height="30" uuid="498a1983-f339-451a-a5fd-dcdc8576aadf"/>
                <textElement textAlignment="Left" verticalAlignment="Middle"/>
                <textFieldExpression><![CDATA[($P{poperationType}== null || "".equals($P{poperationType}.trim())) ? " - "
: $P{poperationType}]]></textFieldExpression>
            </textField>
        </band>
    </title>
    <columnHeader>
        <band height="17" splitType="Stretch"/>
    </columnHeader>
    <detail>
        <band height="169">
            <printWhenExpression><![CDATA[$V{REPORT_COUNT}.intValue()==1]]></printWhenExpression>
            <componentElement>
                <reportElement stretchType="ElementGroupHeight" x="-10" y="6" width="841" height="129" uuid="4955ad4e-e256-4241-af7b-74e108d5ae22">
                    <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
                    <property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
                    <property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
                    <property name="com.jaspersoft.studio.table.style.detail"/>
                </reportElement>
                <jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" whenNoDataType="AllSectionsNoDetail">
                    <datasetRun subDataset="dataset1" uuid="c6df5780-2f3a-4e3a-966a-aff1ff4370c1">
                        <datasetParameter name="pusername">
                            <datasetParameterExpression><![CDATA[$P{pusername}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="pdatefrm">
                            <datasetParameterExpression><![CDATA[$P{pdatefrm}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="pdateto">
                            <datasetParameterExpression><![CDATA[$P{pdateto}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="includeSrvOperations">
                            <datasetParameterExpression><![CDATA[$P{includeSrvOperations}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="recordsIds">
                            <datasetParameterExpression><![CDATA[$P{recordsIds}]]></datasetParameterExpression>
                        </datasetParameter>
                        <datasetParameter name="poperationType">
                            <datasetParameterExpression><![CDATA[$P{poperationType}]]></datasetParameterExpression>
                        </datasetParameter>
                    </datasetRun>
                    <jr:column width="110" uuid="cfec02ae-13dc-4df6-8d14-617649aaa6c9">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="110" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="36a9b833-383b-4af2-87e4-71773e270508"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[Username]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <textField>
                                <reportElement x="0" y="0" width="110" height="60" uuid="5f5f0274-bf6a-48c9-9f36-671afcb36770"/>
                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                </box>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <textFieldExpression><![CDATA[$F{username}]]></textFieldExpression>
                            </textField>
                        </jr:detailCell>
                    </jr:column>
                    <jr:column width="110" uuid="8ed9f7aa-df9e-42e3-8c6d-c6817ad5082e">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="110" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="78350f1e-02c7-4ed3-885a-1acb540ae2ea"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[Execution Date/Time]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <textField>
                                <reportElement x="0" y="0" width="110" height="60" uuid="66c876dc-3094-438d-8448-2379c79b8141"/>
                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                </box>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <textFieldExpression><![CDATA[$F{creationDate}]]></textFieldExpression>
                            </textField>
                        </jr:detailCell>
                    </jr:column>
                    <jr:column width="110" uuid="024ed971-294e-4d28-9670-b5ffbf07c842">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="110" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="05cb01cf-8475-4ee4-88fe-9e87b29130a5"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[User Workstation]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <textField>
                                <reportElement x="0" y="0" width="110" height="60" uuid="cb725b89-5b5c-4a71-b481-14359f8fa897"/>
                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                </box>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <textFieldExpression><![CDATA[$F{workstation}==null || "null".equals($F{workstation}) || $F{workstation}.trim().isEmpty() ? "-" : $F{workstation}]]></textFieldExpression>
                            </textField>
                        </jr:detailCell>
                    </jr:column>
                    <jr:column width="80" uuid="e8b0c60f-e070-4416-bf5d-43f26c8330ab">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="80" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="7022b725-3772-4678-939c-855966cad436"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[View Name]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <textField>
                                <reportElement x="0" y="0" width="80" height="60" uuid="4592f575-769d-40bb-9740-06b593ba1079"/>
                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="1">
                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                </box>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <textFieldExpression><![CDATA[$F{viewName}]]></textFieldExpression>
                            </textField>
                        </jr:detailCell>
                    </jr:column>
                    <jr:column width="90" uuid="ed5048ad-1e9b-44a2-bcca-58500d8a900d">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="90" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="980ca0ac-bf60-4450-8786-cbfa0747a8e7"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[Action]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <textField>
                                <reportElement x="0" y="0" width="90" height="60" uuid="45650b51-a9a8-4a66-8494-d7d4e8cc041b"/>
                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
                                    <topPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
                                    <leftPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
                                    <bottomPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
                                    <rightPen lineWidth="1.0" lineStyle="Solid" lineColor="#000000"/>
                                </box>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <textFieldExpression><![CDATA[$F{actionName}]]></textFieldExpression>
                            </textField>
                        </jr:detailCell>
                    </jr:column>
                    <jr:column width="341" uuid="d2d06c35-362a-476e-9952-4606818ef655">
                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column6"/>
                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                            <staticText>
                                <reportElement mode="Opaque" x="0" y="0" width="341" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="ab1b3b01-3e7d-4aae-87aa-5d085b7ec093"/>
                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                <text><![CDATA[Changes]]></text>
                            </staticText>
                        </jr:columnHeader>
                        <jr:detailCell style="Table_TD" height="60">
                            <componentElement>
                                <reportElement x="0" y="0" width="341" height="60" uuid="31ac4069-f767-4524-a739-9f6df419f909">
                                    <property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
                                    <property name="com.jaspersoft.studio.table.style.table_header" value="Table 1_TH"/>
                                    <property name="com.jaspersoft.studio.table.style.column_header" value="Table 1_CH"/>
                                    <property name="com.jaspersoft.studio.table.style.detail" value="Table 1_TD"/>
                                </reportElement>
                                <jr:table>
                                    <datasetRun subDataset="fieldsChangesDataset" uuid="9aeee64e-04ba-465d-95d9-e5733d608f19">
                                        <datasetParameter name="pchange_group_id">
                                            <datasetParameterExpression><![CDATA[$F{CHANGE_GROUP_ID}]]></datasetParameterExpression>
                                        </datasetParameter>
                                        <datasetParameter name="pchange_group_type">
                                            <datasetParameterExpression><![CDATA[$F{CHANGE_GROUP_TYPE}]]></datasetParameterExpression>
                                        </datasetParameter>
                                    </datasetRun>
                                    <jr:column width="100" uuid="977fcf76-8c02-41ab-9df2-cf3768ba9f6b">
                                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column0"/>
                                        <jr:columnHeader style="Table_CH" height="30" rowSpan="1">
                                            <staticText>
                                                <reportElement mode="Opaque" x="0" y="0" width="100" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="b4a38580-8133-4ea8-80ae-39bacfec2c24"/>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <text><![CDATA[Field Name]]></text>
                                            </staticText>
                                        </jr:columnHeader>
                                        <jr:detailCell style="Table_TD" height="30">
                                            <textField>
                                                <reportElement x="0" y="0" width="100" height="30" uuid="032e37f1-8a22-4b94-aeb9-1c88941d132d"/>
                                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
                                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                </box>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <textFieldExpression><![CDATA[$F{FIELD_NAME}]]></textFieldExpression>
                                            </textField>
                                        </jr:detailCell>
                                    </jr:column>
                                    <jr:column width="110" uuid="c408c9c6-1f3a-4b8a-adc3-46a9370e3655">
                                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column0"/>
                                        <jr:columnHeader style="Table 1_CH" height="30" rowSpan="1">
                                            <staticText>
                                                <reportElement mode="Opaque" x="0" y="0" width="110" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="4a3fe403-0e8b-4993-a7e9-88977095a4d3"/>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <text><![CDATA[Old Value]]></text>
                                            </staticText>
                                        </jr:columnHeader>
                                        <jr:detailCell style="Table_TD" height="30">
                                            <textField>
                                                <reportElement x="0" y="0" width="110" height="30" uuid="876c877a-aaaa-4d5f-bfe0-b32d5583d8b5"/>
                                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
                                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                </box>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <textFieldExpression><![CDATA[$F{OLD_VALUE}== null || "null".equals($F{OLD_VALUE}) || $F{OLD_VALUE}.trim().isEmpty() ? "-" : $F{OLD_VALUE}]]></textFieldExpression>
                                            </textField>
                                        </jr:detailCell>
                                    </jr:column>
                                    <jr:column width="131" uuid="bb407edb-92dc-4522-851e-431b31756a75">
                                        <property name="com.jaspersoft.studio.components.table.model.column.name" value="Column0"/>
                                        <jr:columnHeader style="Table 1_CH" height="30" rowSpan="1">
                                            <staticText>
                                                <reportElement mode="Opaque" x="0" y="0" width="131" height="30" forecolor="#FFFFFF" backcolor="#515658" uuid="00ffc77d-9871-4aaf-8ed5-b352a3ed10a4"/>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <text><![CDATA[New Value]]></text>
                                            </staticText>
                                        </jr:columnHeader>
                                        <jr:detailCell style="Table_TD" height="30">
                                            <textField>
                                                <reportElement x="0" y="0" width="131" height="30" uuid="80e4925e-76c5-4c2e-a923-cf615a9ba8df"/>
                                                <box topPadding="1" leftPadding="1" bottomPadding="1" rightPadding="0">
                                                    <topPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <leftPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <bottomPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                    <rightPen lineWidth="0.0" lineStyle="Solid" lineColor="#000000"/>
                                                </box>
                                                <textElement textAlignment="Center" verticalAlignment="Middle"/>
                                                <textFieldExpression><![CDATA[$F{NEW_VALUE}==null || "null".equals($F{NEW_VALUE}) || $F{NEW_VALUE}.trim().isEmpty() ? "-" : $F{NEW_VALUE}]]></textFieldExpression>
                                            </textField>
                                        </jr:detailCell>
                                    </jr:column>
                                </jr:table>
                            </componentElement>
                        </jr:detailCell>
                    </jr:column>
                </jr:table>
            </componentElement>
        </band>
    </detail>
    <columnFooter>
        <band height="12" splitType="Stretch"/>
    </columnFooter>
    <pageFooter>
        <band height="51" splitType="Stretch">
            <textField evaluationTime="Master">
                <reportElement x="617" y="16" width="100" height="30" uuid="2b26ff67-f0a3-440e-bd83-65e01a94e27d"/>
                <textElement textAlignment="Right"/>
                <textFieldExpression><![CDATA["Page " + $V{MASTER_CURRENT_PAGE}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Master">
                <reportElement x="717" y="16" width="100" height="30" uuid="94fd5f10-21ba-4165-bfc0-bbdbaa143789"/>
                <textElement textAlignment="Left"/>
                <textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
            </textField>
            <textField evaluationTime="Master" pattern="MMM, dd, yyyy">
                <reportElement x="0" y="20" width="150" height="26" forecolor="#000000" uuid="912dbde0-c947-4cc8-8c30-d6abf926190d"/>
                <textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
            </textField>
        </band>
    </pageFooter>
</jasperReport>
