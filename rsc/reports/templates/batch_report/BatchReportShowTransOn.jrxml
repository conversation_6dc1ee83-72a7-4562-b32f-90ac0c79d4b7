<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="BatchReportOff" pageWidth="842" pageHeight="595" orientation="Landscape" whenNoDataType="AllSectionsNoDetail" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" >
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DataAdapter1.jrdax"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="712"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="277"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="461"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="528"/>
	<parameter name="logo" class="java.lang.String"/>
	<parameter name="sessionSequenceNumber" class="java.lang.String"/>
	<parameter name="participant" class="java.lang.String"/>
	<parameter name="batchIDNumber" class="java.lang.String"/>
	<parameter name="categoryPurpose" class="java.lang.String"/>
	<parameter name="sessionDate" class="java.lang.String"/>
	<parameter name="P_TOGGLE" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT
    s.SESSIONSEQ AS "Sequence",
    t.TXID AS "transactionIDNumber",
    instg.CODE AS "instructingParticipant",
    t.AMOUNT AS "transactionAmount",
    dbtr.CODE AS "debtorBank",
    dbtr_acc.ACCNUMBER AS "debtorAccount",
    dbtr.NAME AS "debtorName",
    crdtr.CODE AS "creditorBank",
    crdtr_acc.ACCNUMBER AS "creditorAccount",
    crdtr.NAME AS "creditorName",
    tp.DESCRIPTION AS "transactionPurpose",
    rvs.NAME AS "transactionStatus",
    b.SETTLEMENTDATE AS "settlementDate",
     b.BATCHID AS "batchIDNumber",
    instg.CODE AS "instructingParticipant",
    b.AMOUNT AS "totalAmount",
    (SELECT COUNT(*) FROM ATSTRANSACTIONS t WHERE t.BATCHID = b.ID) AS "numberofTransactions",
    cp.NAME AS "categoryPurpose",
    rvs.NAME AS "batchStatus",
    (SELECT COUNT(*) FROM ATSBATCHES) AS "totalNumberofBatches",
    (SELECT COUNT(*) FROM ATSTRANSACTIONS) AS "totalNumberofBatchTransactions",
    (SELECT SUM(AMOUNT) FROM ATSBATCHES) AS "grandAmountofBatches",
    (SELECT SUM(AMOUNT) FROM ATSTRANSACTIONS) AS "GrandAmountofTransactions"
FROM
    ATSBDI_SESSIONS s
    JOIN ATSBATCHES b
        ON s.ID = b.SESSIONID
    JOIN ATSTRANSACTIONS t
        ON b.ID = t.BATCHID
    JOIN ATSPRT_PARTICIPANTS instg
        ON t.INSTGPARTIID = instg.ID
    LEFT JOIN
    ATSMSG_CTGPURPS cp ON b.CATPURPID = cp.ID
    LEFT JOIN ATSPRT_PARTICIPANTS dbtr
        ON t.DBTRPARTIID = dbtr.ID
    LEFT JOIN ATSACC_Accounts dbtr_acc
        ON dbtr_acc.PARTICIPANTID = dbtr.ID
    LEFT JOIN ATSPRT_PARTICIPANTS crdtr
        ON t.CRDTRPARTIID = crdtr.ID
    LEFT JOIN ATSACC_Accounts crdtr_acc
        ON crdtr_acc.PARTICIPANTID = crdtr.ID
    LEFT JOIN ATSMSG_TRANSPURPS tp
        ON t.TXPURPID = tp.ID
    LEFT JOIN ATSSTATES rvs
        ON t.STATEID = rvs.ID
WHERE
    ($P{P_TOGGLE} = 'true')
    AND (s.BUSINESSDT = TO_DATE($P{sessionDate}, 'YYYY-MM-DD') OR $P{sessionDate} IS NULL)
    AND (s.SESSIONSEQ = $P{sessionSequenceNumber} OR $P{sessionSequenceNumber} IS NULL)
    AND (instg.CODE = $P{participant} OR $P{participant} IS NULL OR $P{participant} = 'ALL')
    AND (b.BATCHID = $P{batchIDNumber} OR $P{batchIDNumber} IS NULL)
    AND (cp.NAME = $P{categoryPurpose} OR $P{categoryPurpose} IS NULL)
ORDER BY
    s.SESSIONSEQ, t.TXID]]>
	</queryString>
	<field name="totalNumberofBatchTransactions" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="totalNumberofBatchTransactions"/>
	</field>
	<field name="sequence" class="java.lang.String"/>
	<field name="instructingParticipant" class="java.lang.String"/>
	<field name="settlementDate" class="java.sql.Date"/>
	<field name="transactionIDNumber" class="java.lang.String"/>
	<field name="transactionAmount" class="java.lang.String"/>
	<field name="debtorBank" class="java.lang.String"/>
	<field name="debtorAccount" class="java.lang.String"/>
	<field name="debtorName" class="java.lang.String"/>
	<field name="creditorBank" class="java.lang.String"/>
	<field name="creditorAccount" class="java.lang.String"/>
	<field name="creditorName" class="java.lang.String"/>
	<field name="transactionPurpose" class="java.lang.String"/>
	<field name="transactionStatus" class="java.lang.String"/>
	<field name="grandAmountoftransactions" class="java.lang.String"/>
	<field name="batchIDNumber" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="batchIDNumber"/>
	</field>
	<field name="categoryPurpose" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="categoryPurpose"/>
	</field>
	<field name="batchStatus" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="batchStatus"/>
	</field>
	<field name="totalNumberofBatches" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="totalNumberofBatches"/>
	</field>
	<field name="numberofTransactions" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="numberofTransactions"/>
	</field>
	<field name="grandAmountofbatches" class="java.lang.String">
		<property name="net.sf.jasperreports.sql.field.column.name" value="grandAmountofBatches"/>
	</field>
	<field name="totalAmount" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="300" splitType="Stretch">
			<staticText>
				<reportElement x="260" y="110" width="280" height="30" forecolor="#0A0A0A"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<text><![CDATA[Kuwait Automated Clearing House]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="160" width="60" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Filters]]></text>
			</staticText>
			<image hAlign="Center" vAlign="Middle" isUsingCache="true">
				<reportElement x="330" y="-10" width="140" height="115"/>
				<imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
			</image>
			<staticText>
				<reportElement x="280" y="140" width="240" height="30" forecolor="#0A0A0A"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<text><![CDATA[Batch Report]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="480" y="230" width="70" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Currency]]></text>
			</staticText>
			<textField>
				<reportElement x="560" y="230" width="150" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA["Kuwaiti Dinar / دينار كويتي"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="730" y="230" width="70" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[العملة]]></text>
			</staticText>
			<staticText>
				<reportElement x="-10" y="-20" width="210" height="70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[CENTRAL BANK OF KUWAIT
P.O.BOX 526 SAFAT 13006 - Kuwait
http://www.cbk.gov.kw                                  ]]></text>
			</staticText>
			<staticText>
				<reportElement x="670" y="-20" width="142" height="70">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<text><![CDATA[بنك الكويت المركزي
ص.ب. 526 صفاة 13006 الكويت
http://www.cbk.gov.kw]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="263" width="80" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Batch ID Number:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="220" y="203" width="90" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Category Purpose:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="233" width="80" height="27" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Participants:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="0" y="200" width="80" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Session Sequence]]></text>
			</staticText>
			<textField>
				<reportElement x="110" y="200" width="70" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sessionSequenceNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="233" width="70" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{participant}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="270" width="70" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{batchIDNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="203" width="71" height="30">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{categoryPurpose}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="220" y="266" width="90" height="30" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Show Transactions:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="220" y="238" width="90" height="17" forecolor="#030000" backcolor="#0702F7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Session Date:]]></text>
			</staticText>
			<textField>
				<reportElement x="330" y="243" width="70" height="20">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sessionDate}!=null? $P{sessionDate}: "-"]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="330" y="270" width="90" height="23">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{P_TOGGLE}]]></textFieldExpression>
			</textField>
		</band>
	</title>
	<columnHeader>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="-10" y="0" width="40" height="40" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Seq]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="-10" y="40" width="40" height="48" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[تَسَلْسُل]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="70" y="0" width="40" height="40" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Trans ID No]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="110" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Instr Part]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="110" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[المُشَارِك المُوَجِّه ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="200" y="0" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Trans Amt]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="200" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[مَبْلَغ المُعَامَلَة ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="240" y="0" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Debtor Bank]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="240" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[بَنْك المَدِين ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="370" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[حِسَاب المَدِين ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="370" y="0" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Debtor Acc]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="410" y="-1" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Debtor Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="410" y="38" width="40" height="50" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[اِسْم المَدِين]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="450" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Creditor Bank]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="450" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[بَنْك الدَائِن ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="540" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Creditor Acc]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="540" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[حِسَاب الدَائِن]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="590" y="-1" width="50" height="40" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Creditor Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="590" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[اِسْم الدَائِن]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="640" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Trans Purpose]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="640" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[غَرَض المُعَامَلَة]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="690" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Trans Status]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="740" y="0" width="60" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Settlement Date]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="690" y="39" width="50" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[حَالَة المُعَامَلَة]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="740" y="39" width="60" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[تَارِيخ التَسْوِيَة ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="70" y="40" width="40" height="48" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[رَقْم مُعَرِّف المُعَامَلَة ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="30" y="0" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Batch ID No]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="30" y="40" width="40" height="48" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[رقم هوية الدفعة]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="160" y="-1" width="40" height="40" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Amt]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="160" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[المبلغ الإجمالي ]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="500" y="0" width="40" height="39" isPrintInFirstWholeBand="true" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[No of Trans]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="500" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[عدد المعاملات]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="280" y="0" width="40" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Cat Purp]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="280" y="39" width="40" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[غرض الفئة]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="320" y="0" width="50" height="39" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Batch Status]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="321" y="39" width="49" height="49" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[حالة الدفعة]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band splitType="Stretch"/>
		<band height="42">
			<textField>
				<reportElement x="740" y="-1" width="60" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{settlementDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="690" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionStatus}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="640" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionPurpose}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="590" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{creditorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="540" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{creditorAccount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="450" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{creditorBank}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="370" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{debtorAccount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="410" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{debtorName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="240" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{debtorBank}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="-10" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{sequence}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="70" y="-1" width="40" height="41" >
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionIDNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="-1" width="50" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{instructingParticipant}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="200" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{transactionAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="30" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{batchIDNumber}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="160" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="500" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{numberofTransactions}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="280" y="-1" width="40" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{categoryPurpose}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="321" y="-1" width="49" height="41">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{batchStatus}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="115" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="51" splitType="Stretch">
			<textField>
				<reportElement x="640" y="16" width="100" height="30"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="740" y="16" width="100" height="30"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMM, dd, yyyy">
				<reportElement x="0" y="20" width="150" height="26"/>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="183" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="364" y="40" width="170" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Number of Batch Transactions]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="364" y="75" width="170" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[إِجْمَالي عَدَد مُعَامَلات الدُفْعَة]]></text>
			</staticText>
			<textField>
				<reportElement x="364" y="109" width="170" height="36">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalNumberofBatchTransactions}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="535" y="40" width="160" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Grand Amount of transactions]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="535" y="75" width="160" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[المَبْلَغ الإِجْمالي لِلْمُعَامَلات]]></text>
			</staticText>
			<textField>
				<reportElement x="535" y="110" width="160" height="36">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grandAmountoftransactions}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="54" y="40" width="160" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Total Number of Batches]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="54" y="75" width="160" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[إِجْمَالي عَدَد الدُفَع]]></text>
			</staticText>
			<textField>
				<reportElement x="54" y="110" width="160" height="36">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="false"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{totalNumberofBatches}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="215" y="110" width="150" height="36">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{grandAmountofbatches}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Opaque" x="215" y="75" width="149" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[المَبْلَغ الإِجْمالي لِلدُفَع]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="214" y="40" width="150" height="35" forecolor="#FFFFFF" backcolor="#C2C2C2">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<printWhenExpression><![CDATA[$P{P_TOGGLE}.equals("true")]]></printWhenExpression>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="11" isBold="true"/>
				</textElement>
				<text><![CDATA[Grand Amount of batches]]></text>
			</staticText>
		</band>
	</summary>
</jasperReport>
