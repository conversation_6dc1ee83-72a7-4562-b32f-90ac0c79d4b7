package com.progressoft.delegators;

import com.opensymphony.workflow.WorkflowException;


class FunctionObject<T, R> {

    private final int order;
    private final Function<T, R, WorkflowException> function;
    private final Class<?> beanClass;

    FunctionObject(Class<?> beanClass, int order, Function<T, R, WorkflowException> function) {
        this.order = order;
        this.function = function;
        this.beanClass = beanClass;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        FunctionObject that = (FunctionObject) o;
        return order == that.order && function.equals(that.function);

    }

    @Override
    public int hashCode() {
        int result = order;
        result = 31 * result + function.hashCode();
        return result;
    }

    @Override
    public String toString() {
        return "FunctionObject{" +
                "order=" + order +
                ", function=" + function +
                ", beanClass=" + beanClass +
                '}';
    }

    public Class<?> getBeanClass() {
        return beanClass;
    }

    int getOrder() {
        return order;
    }

    public Function<T, R, WorkflowException> getFunction() {
        return function;
    }

}
